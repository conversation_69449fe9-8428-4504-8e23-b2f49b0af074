<template>
  <a-drawer
    v-model:open="visible"
    title="自动核价"
    placement="left"
    :width="'90%'"
    :closable="true"
    :mask-closable="false"
    class="auto-pricing-drawer"
  >
    <div class="auto-pricing-container">
      <!-- 配置区域 -->
      <div
        v-if="!isRunning && !isCompleted"
        class="config-section"
      >
        <a-card
          title="自动核价配置"
          size="small"
        >
          <a-form
            :model="config"
            layout="vertical"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="店铺">
                  <a-select
                    v-model:value="config.shop"
                    placeholder="选择店铺"
                  >
                    <a-select-option value="ZenithCai">ZenithCai</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="站点">
                  <a-select
                    v-model:value="config.site"
                    placeholder="选择站点"
                  >
                    <a-select-option value="美国站">美国站</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="重复次数">
                  <a-input-number
                    v-model:value="config.repeatCount"
                    :min="1"
                    :max="10"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="页面大小">
                  <a-input-number
                    v-model:value="config.pageSize"
                    :min="10"
                    :max="1000"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="16">
                <a-form-item label="SKC筛选">
                  <a-input 
                    v-model:value="config.skcFilter" 
                    placeholder="留空则自动获取系统相关SKU"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 同意核价条件 -->
            <a-divider>同意核价条件</a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="库存阈值">
                  <a-input-number
                    v-model:value="config.stockThreshold"
                    :min="0"
                    addon-after="件"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="利润率">
                  <a-input-number
                    v-model:value="config.profitMargin"
                    :min="0"
                    :max="100"
                    addon-after="%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 重新报价条件 -->
            <a-divider>重新报价条件</a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="最大核价次数">
                  <a-input-number
                    v-model:value="config.maxPricingAttempts"
                    :min="1"
                    :max="10"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="降价幅度">
                  <a-input-number
                    v-model:value="config.priceReduction"
                    :min="0.1"
                    :step="0.1"
                    addon-after="%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 放弃核价条件 -->
            <a-divider>放弃核价条件</a-divider>
            <a-form-item label="放弃策略">
              <a-radio-group v-model:value="config.abandonPricing">
                <a-radio :value="1">拒绝，放弃上新</a-radio>
                <a-radio :value="2">不处理</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>

          <div class="action-buttons">
            <a-button 
              type="primary" 
              size="large"
              :icon="h(PlayCircleOutlined)"
              :loading="isInitializing"
              @click="startAutoPricing"
            >
              开始自动核价
            </a-button>
          </div>
        </a-card>
      </div>

      <!-- 进度区域 -->
      <div
        v-if="isRunning || isCompleted"
        class="progress-section"
      >
        <AutoPricingProgress
          :status="status"
          :progress="progress"
          :current-item="currentItem"
          :stats="stats"
          @pause="pauseAutoPricing"
          @resume="resumeAutoPricing"
          @stop="stopAutoPricing"
        />
      </div>

      <!-- 结果区域 -->
      <div
        v-if="isCompleted"
        class="results-section"
      >
        <AutoPricingResults
          :approved-results="approvedResults"
          :repriced-results="repricedResults"
          :rejected-results="rejectedResults"
          :stats="stats"
        />
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import AutoPricingProgress from './AutoPricingProgress.vue'
import AutoPricingResults from './AutoPricingResults.vue'
import { useAutoPricing } from '../../../../composables/useAutoPricing'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 自动核价配置
const config = ref({
  shop: 'ZenithCai',
  site: '美国站',
  repeatCount: 1,
  pageSize: 100,
  skcFilter: '',
  // 同意核价条件
  stockThreshold: 10,
  profitMargin: 40,
  // 重新报价条件
  maxPricingAttempts: 4,
  priceReduction: 1,
  // 放弃核价条件
  abandonPricing: 1 // 1: 拒绝，放弃上新; 2: 不处理
})

// 使用自动核价组合式函数
const {
  status,
  progress,
  currentItem,
  stats,
  approvedResults,
  repricedResults,
  rejectedResults,
  isRunning,
  isCompleted,
  isInitializing,
  startPricing,
  pausePricing,
  resumePricing,
  stopPricing
} = useAutoPricing()

// 开始自动核价
const startAutoPricing = async () => {
  try {
    await startPricing(config.value)
    message.success('自动核价已开始')
  } catch (error) {
    message.error('启动自动核价失败: ' + (error as Error).message)
  }
}

// 暂停自动核价
const pauseAutoPricing = () => {
  pausePricing()
  message.info('自动核价已暂停')
}

// 恢复自动核价
const resumeAutoPricing = () => {
  resumePricing()
  message.info('自动核价已恢复')
}

// 停止自动核价
const stopAutoPricing = () => {
  stopPricing()
  message.warning('自动核价已停止')
}
</script>

<style scoped>
.auto-pricing-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.progress-section {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.results-section {
  flex: 1;
  overflow: auto;
}

.action-buttons {
  text-align: center;
  margin-top: 24px;
}

:deep(.ant-drawer-body) {
  padding: 16px;
  height: calc(100vh - 55px);
  overflow: hidden;
}

:deep(.ant-card-body) {
  padding: 16px;
}

:deep(.ant-divider) {
  margin: 16px 0 12px 0;
}
</style>
