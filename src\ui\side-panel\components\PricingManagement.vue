<template>
  <div class="pricing-management">
    <a-card
      title="商品定价管理"
      size="small"
    >
      <!-- 商品信息展示 -->
      <div
        v-if="productInfo"
        class="product-info"
      >
        <a-descriptions
          :column="2"
          size="small"
          bordered
        >
          <a-descriptions-item label="商品名称">
            {{ productInfo.productName }}
          </a-descriptions-item>
          <a-descriptions-item label="货号">
            {{ productInfo.extCode }}
          </a-descriptions-item>
          <a-descriptions-item label="改价次数">
            <a-tag :color="getReviewTimesColor(productInfo.reviewTimes)">
              {{ productInfo.reviewTimes }} 次
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="申报状态">
            <a-tag :color="getStatusColor(productInfo.status)">
              {{ getStatusText(productInfo.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="原申报价">
            ¥{{ formatPrice(productInfo.originalPrice) }}
          </a-descriptions-item>
          <a-descriptions-item label="官方建议价">
            ¥{{ formatPrice(productInfo.suggestedPrice) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 操作选择区域 -->
      <div class="operation-section">
        <a-divider>选择操作</a-divider>
        
        <a-form
          :model="operationForm"
          layout="vertical"
        >
          <a-form-item label="操作类型">
            <a-radio-group 
              v-model:value="operationForm.supplierResult" 
              @change="onOperationChange"
            >
              <a-radio :value="1">
                <span class="text-green-600">同意</span>
                <span class="text-gray-500 text-sm ml-2">使用官方建议价</span>
              </a-radio>
              <a-radio :value="2">
                <span class="text-orange-600">重报</span>
                <span class="text-gray-500 text-sm ml-2">自定义价格</span>
              </a-radio>
              <a-radio :value="3">
                <span class="text-red-600">放弃</span>
                <span class="text-gray-500 text-sm ml-2">放弃上新</span>
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 重报价格输入框 -->
          <a-form-item 
            v-if="operationForm.supplierResult === 2"
            label="重报价格"
            :rules="[{ required: true, message: '请输入重报价格' }]"
          >
            <a-input-number
              v-model:value="operationForm.customPrice"
              :min="0.01"
              :step="0.01"
              :precision="2"
              placeholder="请输入重报价格"
              style="width: 200px"
              addon-before="¥"
            />
            <div class="text-gray-500 text-xs mt-1">
              建议价格：¥{{ formatPrice(productInfo?.suggestedPrice || 0) }}
            </div>
          </a-form-item>

          <!-- 价格对比信息 -->
          <div
            v-if="operationForm.supplierResult !== 3 && productInfo"
            class="price-comparison"
          >
            <a-alert
              :message="getPriceComparisonMessage()"
              :type="getPriceComparisonType()"
              show-icon
              class="mb-4"
            />
          </div>

          <!-- 提交按钮 -->
          <a-form-item>
            <a-space>
              <a-button 
                type="primary" 
                :loading="submitting"
                :disabled="!canSubmit"
                @click="submitOperation"
              >
                提交
              </a-button>
              <a-button @click="resetForm">
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 操作历史 -->
      <div
        v-if="operationHistory.length > 0"
        class="operation-history"
      >
        <a-divider>操作历史</a-divider>
        <a-timeline size="small">
          <a-timeline-item 
            v-for="(item, index) in operationHistory" 
            :key="index"
            :color="getHistoryItemColor(item.type)"
          >
            <div class="history-item">
              <div class="history-action">
                <a-tag
                  :color="getHistoryItemColor(item.type)"
                  size="small"
                >
                  {{ item.action }}
                </a-tag>
                <span class="text-gray-500 text-sm ml-2">{{ item.timestamp }}</span>
              </div>
              <div class="history-details text-sm text-gray-600">
                {{ item.details }}
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { priceReviewService } from '../../../services/priceReviewService'

// Props
interface ProductInfo {
  priceOrderId: number
  productSkuId: number
  productName: string
  extCode: string
  reviewTimes: number
  status: 'pending' | 'approved' | 'rejected' | 'repriced'
  originalPrice: number
  suggestedPrice: number
}

interface Props {
  productInfo?: ProductInfo
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'operation-success': [result: any]
  'operation-error': [error: string]
}>()

// 响应式数据
const operationForm = ref({
  supplierResult: 1, // 1=同意, 2=重报, 3=放弃
  customPrice: undefined as number | undefined
})

const submitting = ref(false)
const operationHistory = ref<Array<{
  type: 'accept' | 'reprice' | 'reject'
  action: string
  details: string
  timestamp: string
}>>([])

// 计算属性
const canSubmit = computed(() => {
  if (!props.productInfo) return false
  if (operationForm.value.supplierResult === 2) {
    return operationForm.value.customPrice && operationForm.value.customPrice > 0
  }
  return true
})

// 监听操作类型变化
const onOperationChange = () => {
  if (operationForm.value.supplierResult !== 2) {
    operationForm.value.customPrice = undefined
  }
}

// 格式化价格
const formatPrice = (price: number) => {
  return (price / 100).toFixed(2)
}

// 获取改价次数颜色
const getReviewTimesColor = (times: number) => {
  if (times === 1) return 'green'
  if (times <= 3) return 'orange'
  return 'red'
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'processing'
    case 'approved': return 'success'
    case 'rejected': return 'error'
    case 'repriced': return 'warning'
    default: return 'default'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待确认'
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'repriced': return '已重报'
    default: return '未知'
  }
}

// 获取价格对比信息
const getPriceComparisonMessage = () => {
  if (!props.productInfo) return ''
  
  const currentPrice = operationForm.value.supplierResult === 2 
    ? operationForm.value.customPrice || 0
    : props.productInfo.suggestedPrice / 100
  
  const originalPrice = props.productInfo.originalPrice / 100
  const suggestedPrice = props.productInfo.suggestedPrice / 100
  
  if (operationForm.value.supplierResult === 1) {
    return `将使用官方建议价 ¥${suggestedPrice.toFixed(2)}，比原申报价${originalPrice > suggestedPrice ? '降低' : '提高'}了 ¥${Math.abs(originalPrice - suggestedPrice).toFixed(2)}`
  } else if (operationForm.value.supplierResult === 2) {
    return `重报价格 ¥${currentPrice.toFixed(2)}，比建议价${currentPrice > suggestedPrice ? '提高' : '降低'}了 ¥${Math.abs(currentPrice - suggestedPrice).toFixed(2)}`
  }
  
  return ''
}

// 获取价格对比类型
const getPriceComparisonType = () => {
  if (!props.productInfo) return 'info'
  
  const currentPrice = operationForm.value.supplierResult === 2 
    ? operationForm.value.customPrice || 0
    : props.productInfo.suggestedPrice / 100
  
  const suggestedPrice = props.productInfo.suggestedPrice / 100
  
  if (operationForm.value.supplierResult === 1) {
    return 'success'
  } else if (operationForm.value.supplierResult === 2) {
    if (Math.abs(currentPrice - suggestedPrice) / suggestedPrice > 0.1) {
      return 'warning'
    }
    return 'info'
  }
  
  return 'info'
}

// 获取历史项目颜色
const getHistoryItemColor = (type: string) => {
  switch (type) {
    case 'accept': return 'green'
    case 'reprice': return 'orange'
    case 'reject': return 'red'
    default: return 'blue'
  }
}

// 提交操作
const submitOperation = async () => {
  if (!props.productInfo || !canSubmit.value) return
  
  try {
    submitting.value = true
    
    const { priceOrderId, productSkuId } = props.productInfo
    let result: boolean = false
    let actionText = ''
    let details = ''
    
    switch (operationForm.value.supplierResult) {
      case 1: // 同意
        const acceptPrice = priceReviewService.formatPriceToCents(props.productInfo.suggestedPrice / 100)
        result = await priceReviewService.singleAcceptPrice(priceOrderId, productSkuId, acceptPrice)
        actionText = '同意申报价'
        details = `使用官方建议价 ¥${formatPrice(props.productInfo.suggestedPrice)}`
        break
        
      case 2: // 重报
        if (!operationForm.value.customPrice) {
          message.error('请输入重报价格')
          return
        }
        const repriceCents = priceReviewService.formatPriceToCents(operationForm.value.customPrice)
        result = await priceReviewService.singleRepricing(priceOrderId, productSkuId, repriceCents)
        actionText = '重新报价'
        details = `重报价格 ¥${operationForm.value.customPrice.toFixed(2)}`
        break
        
      case 3: // 放弃
        result = await priceReviewService.singleRejectPricing(priceOrderId, productSkuId)
        actionText = '放弃上新'
        details = '已放弃该商品的价格申报'
        break
    }
    
    if (result) {
      message.success(`${actionText}成功`)
      
      // 添加到操作历史
      operationHistory.value.unshift({
        type: operationForm.value.supplierResult === 1 ? 'accept' : 
              operationForm.value.supplierResult === 2 ? 'reprice' : 'reject',
        action: actionText,
        details,
        timestamp: new Date().toLocaleString()
      })
      
      emit('operation-success', { action: actionText, details })
      resetForm()
    } else {
      message.error(`${actionText}失败`)
      emit('operation-error', `${actionText}失败`)
    }
    
  } catch (error) {
    console.error('提交操作失败:', error)
    message.error('操作失败: ' + (error as Error).message)
    emit('operation-error', (error as Error).message)
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  operationForm.value = {
    supplierResult: 1,
    customPrice: undefined
  }
}

// 监听产品信息变化，重置表单
watch(() => props.productInfo, () => {
  resetForm()
  operationHistory.value = []
}, { immediate: true })
</script>

<style scoped>
.pricing-management {
  max-width: 800px;
}

.product-info {
  margin-bottom: 24px;
}

.operation-section {
  margin-bottom: 24px;
}

.price-comparison {
  margin: 16px 0;
}

.operation-history {
  margin-top: 24px;
}

.history-item {
  padding: 4px 0;
}

.history-action {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.history-details {
  margin-left: 8px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-radio-wrapper) {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

:deep(.ant-input-number) {
  width: 100%;
}
</style>
