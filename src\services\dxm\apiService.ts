/**
 * 店小秘API服务 (通用API调用服务)
 *
 * 功能说明：
 * - 提供统一的店小秘API调用接口
 * - 支持GET/POST请求，自动处理认证
 * - 支持直接调用和脚本注入两种方式
 * - 统一错误处理和响应格式
 *
 * 使用场景：
 * - 替代各服务中分散的fetch调用
 * - 提供标准化的API调用方式
 */

export class DianxiaomiApiService {
  /**
   * 调用店小秘API
   */
  async callDianxiaomiAPI(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiApiService] 直接调用店小秘API:', apiConfig.url)

      const method = apiConfig.method || 'GET'
      const headers: Record<string, string> = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'Origin': 'https://www.dianxiaomi.com',
        'Referer': 'https://www.dianxiaomi.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        ...apiConfig.headers
      }

      let body: string | FormData | undefined

      if (method === 'POST' && apiConfig.data) {
        if (apiConfig.data instanceof FormData) {
          body = apiConfig.data
          // FormData会自动设置Content-Type，不要手动设置
        } else {
          headers['Content-Type'] = 'application/x-www-form-urlencoded'
          body = new URLSearchParams(apiConfig.data).toString()
        }
      }

      console.info('[DianxiaomiApiService] 发送请求:', { method, url: apiConfig.url, headers })

      const response = await fetch(apiConfig.url, {
        method: method,
        headers: headers,
        body: body,
        credentials: 'include' // 重要：包含cookies进行身份验证
      })

      console.info('[DianxiaomiApiService] 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      console.info('[DianxiaomiApiService] 店小秘API响应:', result)

      return {
        success: true,
        data: result
      }

    } catch (error) {
      console.error('[DianxiaomiApiService] 店小秘API调用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '调用失败'
      }
    }
  }

  /**
   * 获取店小秘Token状态
   */
  async getDianxiaomiTokenStatus(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/userInfo.json',
      method: 'GET'
    })
  }

  /**
   * 获取店铺列表
   */
  async getShopList(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/shop/list.json',
      method: 'GET'
    })
  }

  /**
   * 查找店小秘标签页
   */
  async findDianxiaomiTab(): Promise<chrome.tabs.Tab | null> {
    const tabs = await chrome.tabs.query({})

    const dxmDomains = [
      'dianxiaomi.com',
      'www.dianxiaomi.com'
    ]

    for (const tab of tabs) {
      if (tab.url) {
        const url = new URL(tab.url)
        if (dxmDomains.some(domain => url.hostname.includes(domain))) {
          return tab
        }
      }
    }

    return null
  }

  /**
   * 注入脚本并调用API
   */
  async injectAndCallAPI(tabId: number, apiConfig: any): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiApiService] 注入脚本调用店小秘API...')

      // 注入脚本到页面
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: this.callAPIFromPage,
        args: [apiConfig]
      })

      if (results && results[0] && results[0].result) {
        const result = results[0].result
        if (result.success) {
          console.info('[DianxiaomiApiService] 注入脚本成功调用API:', result.data)
          return result
        } else {
          return {
            success: false,
            error: result.error || '注入脚本执行失败'
          }
        }
      } else {
        return {
          success: false,
          error: '注入脚本无返回结果'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiApiService] 注入脚本失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '注入脚本失败'
      }
    }
  }

  /**
   * 在页面中执行的API调用函数
   */
  private callAPIFromPage(apiConfig: any): { success: boolean; data?: any; error?: string } {
    try {
      console.info('[Injected Script] 开始调用店小秘API:', apiConfig.url)

      const method = apiConfig.method || 'GET'
      const headers: Record<string, string> = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...apiConfig.headers
      }

      const fetchOptions: RequestInit = {
        method,
        headers,
        credentials: 'include'
      }

      if (method === 'POST' && apiConfig.data) {
        if (apiConfig.data instanceof FormData) {
          fetchOptions.body = apiConfig.data
          delete headers['Content-Type'] // FormData会自动设置
        } else {
          fetchOptions.body = JSON.stringify(apiConfig.data)
        }
      }

      return fetch(apiConfig.url, fetchOptions)
        .then(response => {
          console.info('[Injected Script] API响应状态:', response.status)

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          return response.json()
        })
        .then(data => {
          console.info('[Injected Script] API响应数据:', data)
          return {
            success: true,
            data
          }
        })
        .catch(error => {
          console.error('[Injected Script] API调用失败:', error)
          return {
            success: false,
            error: error instanceof Error ? error.message : 'API调用失败'
          }
        })
    } catch (error) {
      console.error('[Injected Script] API调用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API调用失败'
      }
    }
  }
}

// 创建单例实例
export const dianxiaomiApiService = new DianxiaomiApiService()
export default dianxiaomiApiService
