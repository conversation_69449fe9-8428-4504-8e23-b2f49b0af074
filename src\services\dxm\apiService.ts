/**
 * 店小秘API服务 (统一门面服务)
 *
 * 架构说明：
 * - 作为UI层的唯一入口，实现门面模式
 * - 内部调用各个专业服务，实现职责分离
 * - 提供统一的错误处理、认证、缓存等横切关注点
 * - 对外暴露简洁的业务接口，隐藏内部复杂性
 *
 * 设计原则：
 * - 高内聚：相关功能聚合在专业服务中
 * - 低耦合：UI层只依赖本服务接口
 * - 单一职责：每个内部服务专注特定领域
 *
 * 使用方式：
 * ```typescript
 * import { dianxiaomiApiService } from '@/services/dxm/apiService'
 * const loginStatus = await dianxiaomiApiService.checkLoginStatus()
 * ```
 */

// 导入内部专业服务
import { dianxiaomiDetectionService } from './dianxiaomiDetectionService'
import type {
  DianxiaomiLoginStatus,
  ShopAccount,
  Warehouse,
  FreightTemplate,
  ProductCategory
} from './dianxiaomiDetectionService'

// 业务接口定义
export interface LoginStatus {
  isLoggedIn: boolean
  message: string
  shopCount?: number
  userInfo?: any
  error?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export class DianxiaomiApiService {

  // ==================== 业务门面方法 ====================

  /**
   * 检测店小秘登录状态
   * @returns 登录状态信息
   */
  async checkLoginStatus(): Promise<LoginStatus> {
    try {
      console.info('[DianxiaomiApiService] 检测登录状态...')
      const result = await dianxiaomiDetectionService.checkLoginStatus()

      return {
        isLoggedIn: result.isLoggedIn,
        message: result.message,
        shopCount: result.shopCount,
        userInfo: result.userInfo,
        error: result.error
      }
    } catch (error) {
      console.error('[DianxiaomiApiService] 登录状态检测失败:', error)
      return {
        isLoggedIn: false,
        message: '登录状态检测失败',
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 获取店铺账号列表
   * @returns 店铺账号数据
   */
  async getShopAccounts(): Promise<ApiResponse<ShopAccount[]>> {
    try {
      console.info('[DianxiaomiApiService] 获取店铺账号...')
      const result = await dianxiaomiDetectionService.getShopAccounts()

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        message: result.success ? '获取店铺账号成功' : result.error
      }
    } catch (error) {
      console.error('[DianxiaomiApiService] 获取店铺账号失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取店铺账号失败'
      }
    }
  }

  /**
   * 获取仓库列表
   * @returns 仓库数据
   */
  async getWarehouses(): Promise<ApiResponse<any>> {
    try {
      console.info('[DianxiaomiApiService] 获取仓库列表...')
      const result = await dianxiaomiDetectionService.getWarehouses()

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        message: result.success ? '获取仓库列表成功' : result.error
      }
    } catch (error) {
      console.error('[DianxiaomiApiService] 获取仓库列表失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取仓库列表失败'
      }
    }
  }

  /**
   * 获取运费模板列表
   * @returns 运费模板数据
   */
  async getFreightTemplates(): Promise<ApiResponse<FreightTemplate[]>> {
    try {
      console.info('[DianxiaomiApiService] 获取运费模板...')
      const result = await dianxiaomiDetectionService.getFreightTemplates()

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        message: result.success ? '获取运费模板成功' : result.error
      }
    } catch (error) {
      console.error('[DianxiaomiApiService] 获取运费模板失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取运费模板失败'
      }
    }
  }

  /**
   * 获取商品分类列表
   * @returns 商品分类数据
   */
  async getProductCategories(): Promise<ApiResponse<ProductCategory[]>> {
    try {
      console.info('[DianxiaomiApiService] 获取商品分类...')
      const result = await dianxiaomiDetectionService.getProductCategories()

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        message: result.success ? '获取商品分类成功' : result.error
      }
    } catch (error) {
      console.error('[DianxiaomiApiService] 获取商品分类失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取商品分类失败'
      }
    }
  }

  /**
   * 根据父分类获取子分类
   * @param parentId 父分类ID
   * @returns 子分类数据
   */
  async getCategoriesByParent(parentId?: number): Promise<ProductCategory[]> {
    try {
      console.info('[DianxiaomiApiService] 获取子分类...', parentId)
      return await dianxiaomiDetectionService.getCategoriesByParent(parentId)
    } catch (error) {
      console.error('[DianxiaomiApiService] 获取子分类失败:', error)
      return []
    }
  }

  /**
   * 获取选中的店铺ID
   * @returns 店铺ID
   */
  async getSelectedShopId(): Promise<string | null> {
    try {
      return await dianxiaomiDetectionService.getSelectedShopId()
    } catch (error) {
      console.error('[DianxiaomiApiService] 获取选中店铺ID失败:', error)
      return null
    }
  }

  /**
   * 设置选中的店铺ID
   * @param shopId 店铺ID
   */
  async setSelectedShopId(shopId: string): Promise<void> {
    try {
      await dianxiaomiDetectionService.setSelectedShopId(shopId)
    } catch (error) {
      console.error('[DianxiaomiApiService] 设置选中店铺ID失败:', error)
    }
  }

  // ==================== 底层API调用方法 ====================

  /**
   * 调用店小秘API (底层方法)
   */
  async callDianxiaomiAPI(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiApiService] 直接调用店小秘API:', apiConfig.url)

      const method = apiConfig.method || 'GET'
      const headers: Record<string, string> = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'Origin': 'https://www.dianxiaomi.com',
        'Referer': 'https://www.dianxiaomi.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        ...apiConfig.headers
      }

      let body: string | FormData | undefined

      if (method === 'POST' && apiConfig.data) {
        if (apiConfig.data instanceof FormData) {
          body = apiConfig.data
          // FormData会自动设置Content-Type，不要手动设置
        } else {
          headers['Content-Type'] = 'application/x-www-form-urlencoded'
          body = new URLSearchParams(apiConfig.data).toString()
        }
      }

      console.info('[DianxiaomiApiService] 发送请求:', { method, url: apiConfig.url, headers })

      const response = await fetch(apiConfig.url, {
        method: method,
        headers: headers,
        body: body,
        credentials: 'include' // 重要：包含cookies进行身份验证
      })

      console.info('[DianxiaomiApiService] 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      console.info('[DianxiaomiApiService] 店小秘API响应:', result)

      return {
        success: true,
        data: result
      }

    } catch (error) {
      console.error('[DianxiaomiApiService] 店小秘API调用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '调用失败'
      }
    }
  }

  /**
   * 获取店小秘Token状态
   */
  async getDianxiaomiTokenStatus(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/userInfo.json',
      method: 'GET'
    })
  }

  /**
   * 获取店铺列表
   */
  async getShopList(): Promise<{ success: boolean; data?: any; error?: string }> {
    return this.callDianxiaomiAPI({
      url: 'https://www.dianxiaomi.com/api/shop/list.json',
      method: 'GET'
    })
  }


}

// 重新导出类型定义，供外部使用
export type {
  DianxiaomiLoginStatus,
  ShopAccount,
  Warehouse,
  FreightTemplate,
  ProductCategory
} from './dianxiaomiDetectionService'

// 创建单例实例
export const dianxiaomiApiService = new DianxiaomiApiService()
export default dianxiaomiApiService
