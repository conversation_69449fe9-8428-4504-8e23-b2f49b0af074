/**
 * 店小秘图片上传服务 (专用图片处理服务)
 *
 * 功能说明：
 * - 处理图片上传到店小秘平台
 * - 支持多种图片格式和尺寸处理
 * - 提供图片压缩和优化功能
 * - 管理图片上传状态和进度
 *
 * 使用场景：
 * - 商品图片上传
 * - 图片批量处理
 */

/**
 * 上传签名数据接口
 */
interface UploadSignData {
  url: string
  fields: Record<string, string>
}

export class DianxiaomiImageUploadService {
  /**
   * 获取上传签名
   */
  async getUploadSign(fileName: string): Promise<UploadSignData> {
    try {
      console.info('[DianxiaomiImageUploadService] 获取上传签名:', fileName)

      const response = await fetch('https://www.dianxiaomi.com/popTemuCategory/getUploadSign.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
          fileName: fileName,
          fileType: 'image/jpeg'
        }).toString()
      })

      if (!response.ok) {
        throw new Error(`获取上传签名失败: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.info('[DianxiaomiImageUploadService] 上传签名响应:', result)

      if (!result.success || !result.data) {
        throw new Error(result.errorMsg || '获取上传签名失败')
      }

      return {
        url: result.data.url,
        fields: result.data.fields
      }
    } catch (error) {
      console.error('[DianxiaomiImageUploadService] 获取上传签名失败:', error)
      throw new Error(`获取上传签名失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 上传文件到腾讯云COS
   */
  async uploadToCOS(base64Data: string, signData: UploadSignData, fileName: string): Promise<string> {
    try {
      console.info('[DianxiaomiImageUploadService] 开始上传到腾讯云COS:', fileName)

      // 将base64转换为Blob
      const base64WithoutPrefix = base64Data.replace(/^data:image\/[a-z]+;base64,/, '')
      const binaryString = atob(base64WithoutPrefix)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      const blob = new Blob([bytes], { type: 'image/jpeg' })

      // 构建FormData
      const formData = new FormData()
      
      // 添加签名字段
      Object.entries(signData.fields).forEach(([key, value]) => {
        formData.append(key, value)
      })
      
      // 添加文件
      formData.append('file', blob, fileName)

      console.info('[DianxiaomiImageUploadService] 上传URL:', signData.url)
      console.info('[DianxiaomiImageUploadService] 上传字段:', signData.fields)

      const response = await fetch(signData.url, {
        method: 'POST',
        body: formData
      })

      console.info('[DianxiaomiImageUploadService] COS响应状态:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('[DianxiaomiImageUploadService] COS错误响应:', errorText)
        throw new Error(`上传到COS失败: ${response.status} ${response.statusText}`)
      }

      // 构建最终的图片URL
      const finalUrl = `${signData.url}/${signData.fields.key}`
      console.info('[DianxiaomiImageUploadService] 上传成功，最终URL:', finalUrl)

      return finalUrl
    } catch (error) {
      console.error('[DianxiaomiImageUploadService] 上传到COS失败:', error)
      throw new Error(`上传到COS失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 上传图片到店小秘（完整流程）
   */
  async uploadImageToDianxiaomi(base64Data: string, fileName: string): Promise<string> {
    try {
      console.info('[DianxiaomiImageUploadService] 开始上传图片到店小秘:', fileName)

      // 步骤1：获取上传签名
      const signData = await this.getUploadSign(fileName)
      console.info('[DianxiaomiImageUploadService] 获取上传签名成功:', signData.url)

      // 步骤2：上传文件到腾讯云COS
      const finalUrl = await this.uploadToCOS(base64Data, signData, fileName)

      console.info('[DianxiaomiImageUploadService] 图片上传成功:', finalUrl)
      return finalUrl

    } catch (error) {
      console.error('[DianxiaomiImageUploadService] 图片上传失败:', error)
      throw new Error(`图片上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 压缩图片到目标大小
   */
  async compressImageToTarget(base64Data: string, targetSizeKB: number = 500): Promise<string> {
    try {
      console.info('[DianxiaomiImageUploadService] 开始压缩图片，目标大小:', targetSizeKB, 'KB')

      // 简单的压缩逻辑：如果图片已经小于目标大小，直接返回
      const currentSizeKB = Math.round((base64Data.length * 3) / 4 / 1024)
      console.info('[DianxiaomiImageUploadService] 当前图片大小:', currentSizeKB, 'KB')

      if (currentSizeKB <= targetSizeKB) {
        console.info('[DianxiaomiImageUploadService] 图片已满足大小要求，无需压缩')
        return base64Data
      }

      // 这里可以添加更复杂的压缩逻辑
      // 目前简单返回原图片
      console.warn('[DianxiaomiImageUploadService] 图片压缩功能待实现，返回原图片')
      return base64Data

    } catch (error) {
      console.error('[DianxiaomiImageUploadService] 图片压缩失败:', error)
      return base64Data // 压缩失败时返回原图片
    }
  }

  /**
   * 下载图片并转换为base64
   */
  async downloadImageAsBase64(imageUrl: string): Promise<string> {
    try {
      console.info('[DianxiaomiImageUploadService] 下载图片:', imageUrl)

      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
      }

      const blob = await response.blob()
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
          const base64 = reader.result as string
          console.info('[DianxiaomiImageUploadService] 图片转换为base64成功，大小:', Math.round(base64.length / 1024), 'KB')
          resolve(base64)
        }
        reader.onerror = () => reject(new Error('图片转换为base64失败'))
        reader.readAsDataURL(blob)
      })
    } catch (error) {
      console.error('[DianxiaomiImageUploadService] 下载图片失败:', error)
      throw new Error(`下载图片失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 批量处理图片URL
   */
  async processImageUrls(imageUrls: string[], maxConcurrent: number = 3): Promise<string[]> {
    try {
      console.info('[DianxiaomiImageUploadService] 开始批量处理图片，数量:', imageUrls.length)

      const processedUrls: string[] = []

      // 分批处理图片
      for (let i = 0; i < imageUrls.length; i += maxConcurrent) {
        const batch = imageUrls.slice(i, i + maxConcurrent)

        const batchPromises = batch.map(async (imageUrl, index) => {
          try {
            // 1. 下载图片
            const base64Data = await this.downloadImageAsBase64(imageUrl)

            // 2. 压缩图片
            const compressedBase64 = await this.compressImageToTarget(base64Data)

            // 3. 上传到店小秘
            const fileName = `amazon_image_${Date.now()}_${i + index}.jpg`
            const uploadedUrl = await this.uploadImageToDianxiaomi(compressedBase64, fileName)

            return uploadedUrl
          } catch (error) {
            console.warn('[DianxiaomiImageUploadService] 处理图片失败:', imageUrl, error)
            return null
          }
        })

        const batchResults = await Promise.all(batchPromises)
        processedUrls.push(...batchResults.filter(url => url !== null) as string[])

        // 批次间延迟
        if (i + maxConcurrent < imageUrls.length) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      console.info('[DianxiaomiImageUploadService] 批量处理完成，成功处理:', processedUrls.length, '张图片')
      return processedUrls

    } catch (error) {
      console.error('[DianxiaomiImageUploadService] 批量处理图片失败:', error)
      throw new Error(`批量处理图片失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}

// 创建单例实例
export const dianxiaomiImageUploadService = new DianxiaomiImageUploadService()
export default dianxiaomiImageUploadService
