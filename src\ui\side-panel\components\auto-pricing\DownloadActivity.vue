<script setup lang="ts">
import { ref } from 'vue'
import { DownloadOutlined } from '@ant-design/icons-vue'

// 下载活动数据表单
const downloadForm = ref({
  shop: '',
  activityType: '官方大促',
  exportType: '全部',
  needWarehouseData: '是',
  skcFilter: ''
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 活动类型选项
const activityTypeOptions = [
  { value: '官方大促', label: '官方大促' },
  { value: '品牌活动', label: '品牌活动' },
  { value: '限时抢购', label: '限时抢购' }
]

// 导出类型选项
const exportTypeOptions = [
  { value: '全部', label: '全部' },
  { value: '已报名', label: '已报名' },
  { value: '未报名', label: '未报名' }
]

// 是否需要货盘数据选项
const needWarehouseDataOptions = [
  { value: '是', label: '是' },
  { value: '否', label: '否' }
]

// 获取活动数据
const getActivityData = () => {
  console.log('获取活动数据:', downloadForm.value)
  alert('开始下载活动数据！')
}
</script>

<template>
  <div>
    <a-form
      :model="downloadForm"
      layout="vertical"
      class="space-y-6"
      @finish="getActivityData"
    >
      <!-- 基础配置 -->
      <a-card
        title="基础配置"
        class="mb-6"
      >
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="12">
            <a-form-item label="店铺">
              <a-select
                v-model:value="downloadForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 活动类型 -->
          <a-col :span="12">
            <a-form-item label="活动类型">
              <a-select
                v-model:value="downloadForm.activityType"
                placeholder="选择活动类型"
              >
                <a-select-option
                  v-for="type in activityTypeOptions"
                  :key="type.value"
                  :value="type.value"
                >
                  {{ type.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <!-- 导出类型 -->
          <a-col :span="12">
            <a-form-item label="导出类型">
              <a-select
                v-model:value="downloadForm.exportType"
                placeholder="选择导出类型"
              >
                <a-select-option
                  v-for="type in exportTypeOptions"
                  :key="type.value"
                  :value="type.value"
                >
                  {{ type.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 是否需要货盘数据 -->
          <a-col :span="12">
            <a-form-item label="是否需要货盘数据">
              <a-select
                v-model:value="downloadForm.needWarehouseData"
                placeholder="选择是否需要货盘数据"
              >
                <a-select-option
                  v-for="option in needWarehouseDataOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="downloadForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 下载说明 -->
      <a-card
        title="下载说明"
        class="mb-6"
      >
        <a-alert
          type="info"
          show-icon
          message="活动数据下载"
          description="系统将根据设置的条件下载相应的活动数据，包括商品信息、活动状态、报名情况等。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div>• 官方大促：包含平台官方举办的大型促销活动</div>
          <div>• 品牌活动：包含品牌方自主举办的营销活动</div>
          <div>• 限时抢购：包含限时特价、秒杀等活动</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <DownloadOutlined />
          </template>
          开始下载数据
        </a-button>
      </div>
    </a-form>
  </div>
</template>
