<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  DownOutlined,
  CrownOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue'
// import LoginModal from './LoginModal.vue'

// 菜单项配置 - 参考大卖家设计
const menuItems = ref([
  {
    id: 'dashboard',
    name: '工作台',
    icon: '🏠',
    route: '/side-panel/dashboard',
    active: true
  },
  {
    id: 'product-center',
    name: '上品中心',
    icon: '📊',
    route: '/side-panel/product-center',
    active: false
  },

  {
    id: 'auto-pricing',
    name: '自动核价',
    icon: '🤖',
    route: '/side-panel/auto-pricing',
    active: false
  },
  {
    id: 'forbidden-words',
    name: '违禁词库',
    icon: '⚠️',
    route: '/side-panel/forbidden-words',
    active: false
  },
  {
    id: 'member-service',
    name: '会员服务',
    icon: '👑',
    route: '/side-panel/member-service',
    active: false
  },
  {
    id: 'indexeddb-manager',
    name: '数据管理',
    icon: '🗄️',
    route: '/side-panel/indexeddb-manager',
    active: false
  },

])

// 设置活跃菜单项
const setActiveMenuItem = (itemId: string) => {
  menuItems.value.forEach(item => {
    item.active = item.id === itemId
  })
}

// 获取路由对象
const route = useRoute()
const router = useRouter()

// 点击菜单项 - 使用 Vue Router 导航
const handleMenuClick = (item: { id: string; route: string }) => {
  setActiveMenuItem(item.id)
  // 使用 Vue Router 进行导航
  router.push(item.route)
}

// 使用登录状态管理
const { isLoggedIn, user, login, logout } = useAuth()

// 显示用户菜单
const showUserMenu = ref(false)

// 显示登录模态框
const showLoginModal = ref(false)

// 切换用户菜单
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

// 关闭用户菜单
const closeUserMenu = () => {
  showUserMenu.value = false
}

// 处理登录
const handleLogin = async (credentials: { username: string; password: string }) => {
  const result = await login(credentials)
  if (result.success) {
    showLoginModal.value = false
    alert('登录成功！')
  } else {
    alert(result.message)
  }
}

// 处理登出
const handleLogout = () => {
  logout()
  showUserMenu.value = false
  alert('已退出登录')
}

// 根据当前路由设置活跃菜单项
const updateActiveMenuItem = () => {
  if (!route || !route.path) return

  const currentPath = route.path

  // 找到匹配的菜单项
  const matchedItem = menuItems.value.find(item => item.route === currentPath)
  if (matchedItem) {
    setActiveMenuItem(matchedItem.id)
  }
}

// 监听路由变化
watch(() => route?.path, updateActiveMenuItem, { immediate: true })

// 组件挂载时设置活跃菜单项
onMounted(() => {
  updateActiveMenuItem()
})
</script>

<template>
  <!-- Chrome Extension 专用导航栏 -->
  <a-layout-header class="extension-navbar">
    <div class="navbar-container">
      <!-- 左侧品牌区域 -->
      <div class="navbar-left">
        <!-- 品牌Logo -->
        <div class="brand-section">
          <a-avatar
            :size="40"
            class="brand-avatar"
          >
            <template #icon>
              <span class="brand-text">胡</span>
            </template>
          </a-avatar>
          <div class="brand-info">
            <h1 class="brand-title">胡建大卖家</h1>
            <p class="brand-subtitle">Temu管理工具</p>
          </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="navbar-nav">
          <a-space :size="8">
            <a-button
              v-for="item in menuItems"
              :key="item.id"
              :type="item.active ? 'primary' : 'text'"
              :size="'default'"
              class="nav-button"
              @click="handleMenuClick(item)"
            >
              <template #icon>
                <span class="nav-icon">{{ item.icon }}</span>
              </template>
              {{ item.name }}
            </a-button>
          </a-space>
        </nav>
      </div>

      <!-- 右侧用户信息区域 -->
      <div class="navbar-right">
        <!-- 积分显示 -->
        <a-tag
          v-if="isLoggedIn && user"
          color="gold"
          class="user-points"
        >
          <template #icon>
            <span>💎</span>
          </template>
          {{ user.points }} 积分
        </a-tag>

        <!-- 店铺信息 -->
        <a-tag
          v-if="isLoggedIn && user"
          color="green"
          class="user-shop"
        >
          <template #icon>
            <span>🏪</span>
          </template>
          {{ user.shopCount }}/{{ user.maxShops }}
        </a-tag>

        <!-- 用户菜单 -->
        <a-dropdown
          v-if="isLoggedIn && user"
          :trigger="['click']"
          placement="bottomRight"
        >
          <template #overlay>
            <a-menu>
              <a-menu-item key="profile">
                <UserOutlined />
                个人设置
              </a-menu-item>
              <a-menu-item key="upgrade">
                <CrownOutlined />
                会员升级
              </a-menu-item>
              <a-menu-item key="help">
                <QuestionCircleOutlined />
                帮助中心
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item
                key="logout"
                @click="handleLogout"
              >
                <LogoutOutlined />
                退出登录
              </a-menu-item>
            </a-menu>
          </template>
          <a-button
            type="text"
            class="user-avatar-btn"
          >
            <a-avatar
              :size="32"
              :src="user.avatar"
            >
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
            <span class="user-name">{{ user.username }}</span>
            <DownOutlined />
          </a-button>
        </a-dropdown>

        <!-- 未登录状态 -->
        <a-button
          v-else
          type="primary"
          @click="showLoginModal = true"
        >
          <template #icon>
            <UserOutlined />
          </template>
          登录
        </a-button>
      </div>
    </div>
  </a-layout-header>

  <!-- 登录模态框 -->
  <!-- <LoginModal
    :visible="showLoginModal"
    @close="showLoginModal = false"
    @login="handleLogin"
  /> -->
</template>

<style scoped>
/* ========================================
   Chrome Extension TopNavbar 现代化样式
======================================== */

.extension-navbar {
  background: linear-gradient(135deg, var(--bg-container) 0%, var(--bg-elevated) 100%);
  border-bottom: 1px solid var(--border-color-split);
  box-shadow: var(--shadow-md);
  padding: 0 var(--space-2xl);
  height: 72px;
  line-height: 72px;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 100%;
}

/* 左侧品牌区域 */
.navbar-left {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
  flex: 1;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.brand-avatar {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  border: 3px solid var(--bg-container);
  box-shadow: var(--shadow-brand);
  transition: var(--transition-all);
}

.brand-avatar:hover {
  box-shadow: var(--shadow-xl);
  transform: scale(1.05);
}

.brand-text {
  font-weight: var(--font-bold);
  color: white;
  font-size: var(--text-lg);
}

.brand-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  line-height: var(--leading-tight);
}

.brand-title {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin: 0;
}

/* 导航菜单 */
.navbar-nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-button {
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  transition: var(--transition-all);
  height: 44px;
  padding: 0 var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover::before {
  left: 100%;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-brand);
}

.nav-icon {
  font-size: var(--text-base);
}

/* 右侧用户区域 */
.navbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.user-points,
.user-shop {
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
  border: none;
  box-shadow: var(--shadow-xs);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.user-avatar-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
}

.user-avatar-btn:hover {
  background: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.user-name {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .extension-navbar {
    padding: 0 var(--space-md);
  }

  .navbar-left {
    gap: var(--space-md);
  }

  .brand-info {
    display: none;
  }

  .nav-button {
    padding: 0 var(--space-md);
  }

  .nav-button span:not(.nav-icon) {
    display: none;
  }

  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .user-points,
  .user-shop {
    display: none;
  }
}

/* 确保下拉菜单在最上层 */
:deep(.ant-dropdown) {
  z-index: 1050;
}

/* Ant Design 组件定制 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-dark) 100%);
  border: none;
  box-shadow: var(--shadow-xs);
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, var(--brand-primary-light) 0%, var(--brand-primary) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

:deep(.ant-tag) {
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
  border: none;
  box-shadow: var(--shadow-xs);
}

:deep(.ant-dropdown-menu) {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  padding: var(--space-sm);
}

:deep(.ant-dropdown-menu-item) {
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-xs);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.ant-dropdown-menu-item:hover) {
  background: var(--bg-secondary);
  transform: translateX(2px);
}
</style>
