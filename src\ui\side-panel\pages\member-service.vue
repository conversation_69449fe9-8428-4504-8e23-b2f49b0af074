<script setup lang="ts">
import { ref, onMounted, computed, reactive, h } from 'vue'
import { useNotification } from '../../../composables/useNotification'
import { getApiUrl } from '../../../config'
import { Modal } from 'ant-design-vue'
import {
  ShopOutlined,
  CreditCardOutlined,
  Bar<PERSON>hartOutlined,
  DollarOutlined,
  TeamOutlined,
  UserOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  DisconnectOutlined,
  StarFilled
} from '@ant-design/icons-vue'

// 定义接口类型
interface ShopBinding {
  beginDate: number
  endDate: number
  level: number
  fromPlat: string
  mid: number
  shopName: string
  levelName: string
  shopId: string
  type: number
  createDate: number
  mallId: string
  mallName: string
  mallStatus: number
  isSemiManagedMall: boolean
  logoUrl: string
  status: string
  userId: string
  shopDescription?: string
  shopAddress?: string
  contactPhone?: string
  contactEmail?: string
  shopRating?: number
  isVerified?: boolean
  remarks?: string
}

interface ApiResponse {
  code: number
  msg: string
  data: ShopBinding[]
}

// 会员服务页面
const activeTab = ref('shop-binding')

// 通知系统
const { success, error: showError, warning, info } = useNotification()

// 示例数据
const memberInfo = ref({
  phone: '13023862193',
  remainingBindings: 2,
  memberType: '星耀会员'
})

// 左侧菜单项
const menuItems = ref([
  {
    id: 'shop-binding',
    name: '店铺绑定',
    icon: ShopOutlined
  },
  {
    id: 'payment-plan',
    name: '支付套餐',
    icon: CreditCardOutlined
  },
  {
    id: 'sales-orders',
    name: '销售订单🔥',
    icon: BarChartOutlined
  },
  {
    id: 'settlement-records',
    name: '结算记录',
    icon: DollarOutlined
  },
  {
    id: 'my-users',
    name: '我的用户',
    icon: TeamOutlined
  },
  {
    id: 'my-info',
    name: '我的信息',
    icon: UserOutlined
  }
])

// 表格列定义
const columns = [
  {
    title: '平台',
    dataIndex: 'fromPlat',
    key: 'fromPlat',
    width: 80
  },
  {
    title: '店铺ID',
    dataIndex: 'shopId',
    key: 'shopId',
    width: 120
  },
  {
    title: '店铺名称',
    dataIndex: 'shopName',
    key: 'shopName',
    width: 200
  },
  {
    title: '商城名称',
    dataIndex: 'mallName',
    key: 'mallName',
    width: 150
  },
  {
    title: '会员等级',
    dataIndex: 'levelName',
    key: 'levelName',
    width: 120
  },
  {
    title: '会员类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '生效日期',
    dataIndex: 'beginDate',
    key: 'beginDate',
    width: 120
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 120
  },
  {
    title: '评分',
    dataIndex: 'shopRating',
    key: 'shopRating',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 店铺绑定数据
const shopBindings = ref<ShopBinding[]>([])

// 加载状态
const loading = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `共 ${total} 条记录，显示 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
})

// 使用配置管理的 API URL

// 格式化日期
const formatDate = (timestamp: number): string => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

// 获取状态显示文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'active': '正常',
    'inactive': '停用',
    'suspended': '暂停'
  }
  return statusMap[status] || status
}

// 获取状态样式
const getStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    'active': 'text-green-600 bg-green-100',
    'inactive': 'text-gray-600 bg-gray-100',
    'suspended': 'text-red-600 bg-red-100'
  }
  return classMap[status] || 'text-gray-600 bg-gray-100'
}

// 获取会员类型显示文本
const getMemberTypeText = (type: number): string => {
  const typeMap: Record<number, string> = {
    1: '系统赠送',
    2: '订阅支付',
    3: '一次性购买'
  }
  return typeMap[type] || '未知'
}

// 获取店铺绑定列表
const fetchShopBindings = async () => {
  try {
    loading.value = true

    const response = await fetch(getApiUrl('/dmj/shop/binding/dmjList'), {
      method: 'POST',
      headers: {
        'Accept': '*/*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        keyword: searchKeyword.value,
        pageSize: pagination.value.pageSize,
        currentPage: pagination.value.current
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      shopBindings.value = result.data || []
      pagination.value.total = result.total || result.data?.length || 0
      success('数据加载成功', `共加载 ${shopBindings.value.length} 条记录`)
    } else {
      throw new Error(result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取店铺绑定列表失败:', error)
    showError('加载失败', error instanceof Error ? error.message : '网络错误')
    shopBindings.value = []
  } finally {
    loading.value = false
  }
}

// 切换菜单项
const switchTab = (tabId: string) => {
  activeTab.value = tabId
  if (tabId === 'shop-binding') {
    fetchShopBindings()
  }
}

// 刷新数据
const refreshData = () => {
  info('刷新中', '正在重新加载数据...')
  fetchShopBindings()
}

// 搜索
const handleSearch = () => {
  pagination.value.current = 1
  fetchShopBindings()
}

// 绑定店铺
const bindShop = () => {
  info('绑定店铺', '请在 Temu 商家后台页面进行店铺检测和绑定')
  // 这里可以集成之前的店铺绑定功能
}

// 分页变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchShopBindings()
}

// 查看店铺详情
const viewShopDetail = (shop: ShopBinding) => {
  info('店铺详情', `店铺：${shop.shopName}，地址：${shop.shopAddress || '未设置'}`)
  // 这里可以打开详情弹窗或跳转到详情页面
}

// 解绑店铺
const unbindShop = (shop: ShopBinding) => {
  Modal.confirm({
    title: '确认解绑',
    content: `确定要解绑店铺"${shop.shopName}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        // 这里应该调用解绑 API
        warning('功能开发中', '解绑功能正在开发中...')
        // 模拟API调用
        // await fetch(getApiUrl('/dmj/shop/binding/unbind'), {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({ shopId: shop.shopId })
        // })
        // success('解绑成功', '店铺已成功解绑')
        // fetchShopBindings()
      } catch (error) {
        showError('解绑失败', '网络错误或系统异常')
      }
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  fetchShopBindings()
})
</script>

<template>
  <div class="h-full flex bg-gray-50">
    <!-- 左侧菜单 -->
    <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto">
      <a-card
        :bordered="false"
        class="h-full"
      >
        <template #title>
          <div class="flex items-center space-x-2">
            <span class="text-xl">👤</span>
            <span class="text-lg font-semibold">会员服务</span>
          </div>
        </template>

        <a-menu
          :selected-keys="[activeTab]"
          mode="inline"
          @click="({ key }) => switchTab(key)"
        >
          <a-menu-item
            v-for="item in menuItems"
            :key="item.id"
          >
            <template #icon>
              <component :is="item.icon" />
            </template>
            {{ item.name }}
          </a-menu-item>
        </a-menu>
      </a-card>
    </div>

    <!-- 右侧内容区域 -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-y-auto p-6">
        <!-- 店铺绑定 -->
        <div v-if="activeTab === 'shop-binding'">
          <!-- 页面标题和操作区域 -->
          <a-card
            class="mb-6 shadow-sm"
            :bordered="false"
          >
            <template #title>
              <div class="flex items-center space-x-2">
                <ShopOutlined />
                <span class="text-lg font-semibold">店铺绑定管理</span>
              </div>
            </template>

            <!-- 操作区域 -->
            <div class="flex flex-col space-y-4">
              <!-- 搜索和操作按钮 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索店铺名称或ID"
                    style="width: 300px"
                    @press-enter="handleSearch"
                  >
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>

                  <a-button
                    type="primary"
                    @click="handleSearch"
                  >
                    <template #icon>
                      <SearchOutlined />
                    </template>
                    搜索
                  </a-button>

                  <a-button
                    :loading="loading"
                    @click="refreshData"
                  >
                    <template #icon>
                      <ReloadOutlined />
                    </template>
                    刷新
                  </a-button>
                </div>
              </div>

              <!-- 会员信息提示 -->
              <a-alert
                type="info"
                show-icon
                :message="`剩余可绑店铺次数：${memberInfo.remainingBindings}/个店铺/月会员`"
                :action="
                  h('a-button', {
                    type: 'primary',
                    size: 'small',
                    onClick: bindShop
                  }, '绑定店铺')
                "
              />
            </div>
          </a-card>

          <!-- 店铺绑定表格 -->
          <a-card
            :bordered="false"
            class="shadow-sm"
          >
            <a-table
              :columns="columns"
              :data-source="shopBindings"
              :pagination="pagination"
              :loading="loading"
              :scroll="{ x: 1200 }"
              row-key="mallId"
              @change="handleTableChange"
            >
              <!-- 平台列 -->
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'fromPlat'">
                  <a-tag color="blue">
                    {{ record.fromPlat.toUpperCase() }}
                  </a-tag>
                </template>

                <!-- 店铺名称列 -->
                <template v-else-if="column.key === 'shopName'">
                  <div class="flex items-center space-x-2">
                    <a-avatar
                      v-if="record.logoUrl"
                      :src="record.logoUrl"
                      :size="24"
                    />
                    <span>{{ record.shopName }}</span>
                    <a-tag
                      v-if="record.isVerified"
                      color="success"
                      size="small"
                    >
                      已验证
                    </a-tag>
                  </div>
                </template>

                <!-- 会员等级列 -->
                <template v-else-if="column.key === 'levelName'">
                  <a-tag color="purple">
                    {{ record.levelName }}
                  </a-tag>
                </template>

                <!-- 会员类型列 -->
                <template v-else-if="column.key === 'type'">
                  {{ getMemberTypeText(record.type) }}
                </template>

                <!-- 状态列 -->
                <template v-else-if="column.key === 'status'">
                  <a-tag :color="record.status === 'active' ? 'success' : record.status === 'inactive' ? 'default' : 'error'">
                    {{ getStatusText(record.status) }}
                  </a-tag>
                </template>

                <!-- 日期列 -->
                <template v-else-if="column.key === 'beginDate'">
                  {{ formatDate(record.beginDate) }}
                </template>

                <template v-else-if="column.key === 'endDate'">
                  {{ formatDate(record.endDate) }}
                </template>

                <!-- 评分列 -->
                <template v-else-if="column.key === 'shopRating'">
                  <div
                    v-if="record.shopRating"
                    class="flex items-center space-x-1"
                  >
                    <StarFilled class="text-yellow-500" />
                    <span>{{ record.shopRating.toFixed(2) }}</span>
                  </div>
                  <span
                    v-else
                    class="text-gray-400"
                  >-</span>
                </template>

                <!-- 操作列 -->
                <template v-else-if="column.key === 'actions'">
                  <a-space>
                    <a-button
                      type="link"
                      size="small"
                      @click="viewShopDetail(record)"
                    >
                      <template #icon>
                        <EyeOutlined />
                      </template>
                      详情
                    </a-button>

                    <a-button
                      type="link"
                      danger
                      size="small"
                      @click="unbindShop(record)"
                    >
                      <template #icon>
                        <DisconnectOutlined />
                      </template>
                      解绑
                    </a-button>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </div>

        <!-- 支付套餐 -->
        <div v-else-if="activeTab === 'payment-plan'">
          <a-card
            :bordered="false"
            class="shadow-sm"
          >
            <template #title>
              <div class="flex items-center space-x-2">
                <CreditCardOutlined />
                <span class="text-lg font-semibold">支付套餐</span>
              </div>
            </template>

            <a-empty description="功能开发中..." />
          </a-card>
        </div>

        <!-- 销售订单 -->
        <div v-else-if="activeTab === 'sales-orders'">
          <a-card
            :bordered="false"
            class="shadow-sm"
          >
            <template #title>
              <div class="flex items-center space-x-2">
                <BarChartOutlined />
                <span class="text-lg font-semibold">销售订单</span>
                <a-tag
                  color="red"
                  size="small"
                >
                  🔥
                </a-tag>
              </div>
            </template>

            <a-empty description="功能开发中..." />
          </a-card>
        </div>

        <!-- 结算记录 -->
        <div v-else-if="activeTab === 'settlement-records'">
          <a-card
            :bordered="false"
            class="shadow-sm"
          >
            <template #title>
              <div class="flex items-center space-x-2">
                <DollarOutlined />
                <span class="text-lg font-semibold">结算记录</span>
              </div>
            </template>

            <a-empty description="功能开发中..." />
          </a-card>
        </div>

        <!-- 我的用户 -->
        <div v-else-if="activeTab === 'my-users'">
          <a-card
            :bordered="false"
            class="shadow-sm"
          >
            <template #title>
              <div class="flex items-center space-x-2">
                <TeamOutlined />
                <span class="text-lg font-semibold">我的用户</span>
              </div>
            </template>

            <a-empty description="功能开发中..." />
          </a-card>
        </div>

        <!-- 我的信息 -->
        <div v-else-if="activeTab === 'my-info'">
          <a-card
            :bordered="false"
            class="shadow-sm"
          >
            <template #title>
              <div class="flex items-center space-x-2">
                <UserOutlined />
                <span class="text-lg font-semibold">我的信息</span>
              </div>
            </template>

            <a-empty description="功能开发中..." />
          </a-card>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.ant-menu-item) {
  margin: 4px 0;
  border-radius: 6px;
}

:deep(.ant-table-thead > tr > th) {
  font-weight: 600;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-pagination) {
  margin-top: 16px;
}
</style>
