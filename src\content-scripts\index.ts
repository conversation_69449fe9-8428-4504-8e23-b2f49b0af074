// Content script for Temu extension - Universal message router
// Routes messages to appropriate specialized content scripts
// No UI injection - only background functionality and message routing

self.onerror = function (message, source, lineno, colno, error) {
  console.info("Error: " + message)
  console.info("Source: " + source)
  console.info("Line: " + lineno)
  console.info("Column: " + colno)
  console.info("Error object: " + error)
}

console.info("[Universal Router] Content script loaded on:", window.location.href)

// 检测当前页面类型
function detectPageType(): string {
  const hostname = window.location.hostname
  
  if (hostname.includes('dianxiaomi.com')) {
    return 'dianxiaomi'
  } else if (hostname.includes('kuajingmaihuo.com') || hostname.includes('temu.com')) {
    return 'temu'
  } else if (hostname.includes('amazon.com')) {
    return 'amazon'
  }
  
  return 'unknown'
}

// 通用工具函数：等待元素出现
function waitForElement(selector: string, timeout: number = 5000): Promise<Element | null> {
  return new Promise((resolve) => {
    const element = document.querySelector(selector)
    if (element) {
      resolve(element)
      return
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector)
      if (element) {
        observer.disconnect()
        resolve(element)
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    setTimeout(() => {
      observer.disconnect()
      resolve(null)
    }, timeout)
  })
}

// 通用工具函数：延迟执行
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 扩展全局接口类型
declare global {
  interface Window {
    dianxiaomiHandler?: {
      handleMessage: (request: any, sender: any, sendResponse: (response: any) => void) => boolean
    }
    temuUnified?: {
      handleMessage: (request: any, sender: any, sendResponse: (response: any) => void) => boolean
    }
  }
}

// 消息路由器 - 将消息分发到对应的专用模块
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.info('[Universal Router] 收到消息:', request)
  
  const pageType = detectPageType()
  console.info('[Universal Router] 当前页面类型:', pageType)

  // 根据页面类型和消息类型路由到对应的处理器
  if (pageType === 'dianxiaomi') {
    // 店小秘相关消息
    if (request.action?.startsWith('DXM_') ||
        request.action === 'GET_SHOP_ACCOUNTS' ||
        request.action === 'GET_WAREHOUSES' ||
        request.action === 'GET_FREIGHT_TEMPLATES' ||
        request.action === 'GET_PRODUCT_CATEGORIES' ||
        request.action === 'GET_TEMU_CATEGORIES') {
      
      // 检查是否有店小秘处理器
      if (window.dianxiaomiHandler) {
        console.info('[Universal Router] 转发到店小秘处理器')
        return window.dianxiaomiHandler.handleMessage(request, sender, sendResponse)
      } else {
        console.warn('[Universal Router] 店小秘处理器未加载')
        sendResponse({ success: false, error: '店小秘处理器未加载' })
        return true
      }
    }
  } else if (pageType === 'temu') {
    // Temu相关消息
    if (request.action?.startsWith('TEMU_') ||
        request.action === 'GET_ANTI_CONTENT' ||
        request.action === 'GET_TEMU_DATA' ||
        request.action === 'MAKE_AUTHENTICATED_REQUEST' ||
        request.action === 'GET_AUTH_INFO' ||
        request.action === 'GET_MALL_ID' ||
        request.action === 'CHECK_LOGIN_STATUS') {
      
      // 检查是否有Temu处理器
      if (window.temuUnified) {
        console.info('[Universal Router] 转发到Temu处理器')
        return window.temuUnified.handleMessage(request, sender, sendResponse)
      } else {
        console.warn('[Universal Router] Temu处理器未加载')
        sendResponse({ success: false, error: 'Temu处理器未加载' })
        return true
      }
    }
  }

  // 通用消息处理
  if (request.action === 'PING') {
    sendResponse({ success: true, message: 'pong', pageType })
    return true
  }

  if (request.action === 'GET_PAGE_INFO') {
    sendResponse({
      success: true,
      data: {
        url: window.location.href,
        title: document.title,
        pageType: pageType,
        timestamp: Date.now()
      }
    })
    return true
  }

  // 未知消息
  console.warn('[Universal Router] 未知消息:', request.action)
  sendResponse({ success: false, error: '未知的action: ' + request.action })
  return true
})

console.info("[Universal Router] ✅ 消息路由器已初始化")
