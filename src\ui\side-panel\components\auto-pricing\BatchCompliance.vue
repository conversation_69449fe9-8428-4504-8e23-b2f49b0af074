<script setup lang="ts">
import { ref } from 'vue'
import { UploadOutlined, PlayCircleOutlined } from '@ant-design/icons-vue'

// 批量合规中心表单
const complianceForm = ref({
  shop: '',
  onSale: '',
  complianceType: '加利福尼亚州65号法案',
  spuFilter: '',
  complianceTemplate: null,
  timeInterval: 3
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 是否在售选项
const onSaleOptions = [
  { value: '是', label: '是' },
  { value: '否', label: '否' }
]

// 合规信息类型选项
const complianceTypeOptions = [
  { value: '加利福尼亚州65号法案', label: '加利福尼亚州65号法案' },
  { value: '商品实拍图', label: '商品实拍图' },
  { value: '商品资质', label: '商品资质' },
  { value: '商品产地补充', label: '商品产地补充' },
  { value: '商品类目纠正', label: '商品类目纠正' },
  { value: '商品tro检测', label: '商品tro检测' }
]

// 开始合规中心上传
const startComplianceUpload = () => {
  console.log('开始合规中心上传:', complianceForm.value)
  alert('批量合规中心功能已启动！')
}

// 选择合规信息模板
const selectComplianceTemplate = () => {
  console.log('选择合规信息模板')
}

// 文件上传处理
const handleFileChange = (info: any) => {
  console.log('文件上传:', info)
}
</script>

<template>
  <div>
    <a-form
      :model="complianceForm"
      layout="vertical"
      class="space-y-6"
      @finish="startComplianceUpload"
    >
      <!-- 基础配置 -->
      <a-card
        title="基础配置"
        class="mb-6"
      >
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="complianceForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 是否在售 -->
          <a-col :span="8">
            <a-form-item label="是否在售">
              <a-select
                v-model:value="complianceForm.onSale"
                placeholder="选择在售状态"
              >
                <a-select-option
                  v-for="option in onSaleOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 时间间隔 -->
          <a-col :span="8">
            <a-form-item label="时间间隔(秒)">
              <a-input-number
                v-model:value="complianceForm.timeInterval"
                :min="1"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 合规信息类型 -->
        <a-form-item label="合规信息类型">
          <a-select
            v-model:value="complianceForm.complianceType"
            placeholder="选择合规信息类型"
          >
            <a-select-option
              v-for="type in complianceTypeOptions"
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- SPU筛选 -->
        <a-form-item label="SPU筛选">
          <a-input
            v-model:value="complianceForm.spuFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 模板上传 -->
      <a-card
        title="合规信息模板"
        class="mb-6"
      >
        <a-form-item label="选择合规信息模板">
          <a-upload
            :file-list="[]"
            :before-upload="() => false"
            @change="handleFileChange"
          >
            <a-button>
              <template #icon>
                <UploadOutlined />
              </template>
              选择模板文件
            </a-button>
          </a-upload>
          <div class="text-sm text-gray-500 mt-2">
            支持图片格式(.jpg, .png, .gif)和文档格式(.pdf, .doc, .docx)
          </div>
        </a-form-item>
      </a-card>

      <!-- 合规说明 -->
      <a-card
        title="合规说明"
        class="mb-6"
      >
        <a-alert
          type="info"
          show-icon
          message="批量合规中心"
          description="根据选择的合规信息类型，批量为商品上传相应的合规文件和信息。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div>• 加利福尼亚州65号法案：需要上传相关警告标签</div>
          <div>• 商品实拍图：需要上传商品的实际拍摄图片</div>
          <div>• 商品资质：需要上传相关的资质证明文件</div>
          <div>• 其他类型：根据具体要求上传对应文件</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始批量上传
        </a-button>
      </div>
    </a-form>
  </div>
</template>
