<template>
  <div class="amazon-price-info">
    <!-- Amazon价格信息标题 -->
    <div class="amazon-header">
      <span class="amazon-title">🛒 Amazon实时信息</span>
      <a-button
        type="link"
        size="small"
        class="refresh-btn"
        :loading="loading"
        :title="loading ? '获取中...' : '强制刷新Amazon价格'"
        @click="() => refreshAmazonPrice(true)"
      >
        🔄 刷新
      </a-button>
    </div>

    <!-- Amazon完整价格信息 -->
    <div
      v-if="amazonData"
      class="amazon-content"
    >
      <!-- 货盘平台和库存 - 合并到一行 -->
      <div class="info-row">
        <span class="label">货盘平台:</span>
        <div class="platform-info">
          <a
            :href="amazonUrl"
            target="_blank"
            class="platform-link"
            :title="`打开Amazon商品页面: ${amazonUrl}`"
          >
            AM
          </a>
          <span class="separator">|</span>
          <span class="label-inline">库存:</span>
          <span
            class="stock-value"
            :class="stockStatusClass"
          >{{ amazonData.stock }}</span>
        </div>
      </div>

      <!-- 成本信息 -->
      <div class="info-row">
        <span class="label">成本:</span>
        <div class="cost-value">
          <span class="cny-cost">¥{{ cnyCost }}</span>
          <span class="usd-cost">${{ amazonData.usdPrice }}</span>
        </div>
      </div>

      <!-- 申报价格 -->
      <div class="info-row">
        <span class="label">申报价格:</span>
        <span class="declared-price">{{ declaredPrice }}</span>
      </div>

      <!-- 官方申报价 - 只有在真正获取到时才显示 -->
      <div
        v-if="enhancedPriceData?.officialDeclaredPrice"
        class="info-row"
      >
        <span class="label">官方申报价:</span>
        <span class="official-price">¥{{ officialDeclaredPrice }}</span>
      </div>

      <!-- 毛利信息 - 只有在有Amazon价格和官方申报价时才显示 -->
      <div
        v-if="amazonData && enhancedPriceData?.officialDeclaredPrice"
        class="info-row"
      >
        <span class="label">毛利:</span>
        <div class="profit-value">
          <span
            class="profit-amount"
            :class="profitStatusClass"
          >¥{{ profitAmount }}</span>
          <span class="profit-rate">({{ profitRate }}%)</span>
        </div>
      </div>

      <!-- 更新时间 -->
      <div
        v-if="amazonData"
        class="update-time"
      >
        <span class="time-value">{{ formatUpdateTime }}</span>
        <span
          v-if="dataSource"
          class="data-source"
        >{{ dataSource }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-else-if="loading"
      class="loading-state"
    >
      <div class="loading-indicator"></div>
      <span class="loading-text">获取Amazon价格中...</span>
    </div>

    <!-- 无数据状态 -->
    <div
      v-else
      class="no-data-state"
    >
      <span class="no-data-text">暂无Amazon价格数据</span>
      <div
        v-if="true"
        class="debug-info"
        style="font-size: 10px; color: #999; margin: 4px 0;"
      >
        调试: extCode={{ props.extCode }}, loading={{ loading }}, hasData={{ !!enhancedPriceData }}
      </div>
      <div class="button-group">
        <a-button
          type="link"
          size="small"
          :title="'从缓存或后台获取Amazon价格'"
          @click="() => refreshAmazonPrice(false)"
        >
          点击获取
        </a-button>
        <a-button
          type="link"
          size="small"
          :title="'强制刷新Amazon价格'"
          @click="() => refreshAmazonPrice(true)"
        >
          强制刷新
        </a-button>
        <a-button
          type="link"
          size="small"
          :title="'调试缓存状态'"
          @click="debugCache"
        >
          调试缓存
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { amazonPriceService } from '../../../services/amazon/amazonPriceService'
import type { AmazonPriceInfo } from '../../../services/amazon/amazonPriceService'

// Props
interface Props {
  extCode: string
  exchangeRate?: number
  declaredPrice?: string
  officialDeclaredPrice?: number
  priceOrderId?: number
}

const props = withDefaults(defineProps<Props>(), {
  exchangeRate: 7.2,
  declaredPrice: '0.00¥',
  officialDeclaredPrice: 0,
  priceOrderId: undefined
})

// Emits
const emit = defineEmits<{
  priceUpdated: [data: { usdPrice: string; stock: number; updateTime: string }]
}>()

// 响应式数据
const loading = ref(false)
const enhancedPriceData = ref<AmazonPriceInfo | null>(null)
const dataSource = ref<string>('')

// 计算属性
const amazonData = computed(() => enhancedPriceData.value?.amazonPrice || null)

const cnyCost = computed(() => {
  if (!amazonData.value) return '0.00'
  return (parseFloat(amazonData.value.usdPrice) * props.exchangeRate).toFixed(2)
})

const stockStatusClass = computed(() => {
  if (!amazonData.value) return ''
  const stock = parseInt(amazonData.value.stock)
  if (stock <= 5) return 'stock-low'
  if (stock <= 20) return 'stock-medium'
  return 'stock-high'
})

const declaredPrice = computed(() => {
  return props.declaredPrice || '0.00¥'
})

const officialDeclaredPrice = computed(() => {
  // 优先使用增强数据中的官方申报价，否则使用props
  const official = enhancedPriceData.value?.officialDeclaredPrice ?? props.officialDeclaredPrice ?? 0
  return official.toFixed(2)
})

const profitAmount = computed(() => {
  // 优先使用增强数据中的利润，否则计算
  if (enhancedPriceData.value?.profit !== undefined) {
    return enhancedPriceData.value.profit.toFixed(2)
  }
  if (!amazonData.value) return '0.00'
  const cost = parseFloat(cnyCost.value)
  const official = parseFloat(officialDeclaredPrice.value)
  return (official - cost).toFixed(2)
})

const profitRate = computed(() => {
  // 优先使用增强数据中的利润率，否则计算
  if (enhancedPriceData.value?.profitRate !== undefined) {
    return enhancedPriceData.value.profitRate.toFixed(2)
  }
  if (!amazonData.value) return '0.00'
  const cost = parseFloat(cnyCost.value)
  const official = parseFloat(officialDeclaredPrice.value)
  if (official === 0) return '0.00'
  return ((official - cost) / official * 100).toFixed(2)
})

const profitStatusClass = computed(() => {
  const rate = parseFloat(profitRate.value)
  if (rate >= 30) return 'profit-high'
  if (rate >= 15) return 'profit-medium'
  return 'profit-low'
})

const formatUpdateTime = computed(() => {
  if (!enhancedPriceData.value?.lastUpdated) return ''
  const date = new Date(enhancedPriceData.value.lastUpdated)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
})

// Amazon商品链接
const amazonUrl = computed(() => {
  if (!props.extCode) return ''

  // 从extCode中提取ASIN (格式: B07X46RK45[am]1)
  const asin = props.extCode.split('[am]')[0]
  if (!asin) return ''

  return `https://www.amazon.com/dp/${asin}`
})





// 方法
const debugCache = () => {
  console.info('[AmazonPriceInfo] 调试缓存状态:', {
    extCode: props.extCode,
    enhancedPriceData: enhancedPriceData.value,
    dataSource: dataSource.value,
    loading: loading.value
  })
}

const refreshAmazonPrice = async (forceRefresh: boolean = false) => {
  console.info('[AmazonPriceInfo] 开始获取价格信息:', {
    extCode: props.extCode,
    forceRefresh,
    exchangeRate: props.exchangeRate,
    priceOrderId: props.priceOrderId
  })

  if (!props.extCode || !props.extCode.includes('[am]')) {
    console.warn('[AmazonPriceInfo] 无效的Amazon商品编码:', props.extCode)
    return
  }

  loading.value = true
  try {
    console.info('[AmazonPriceInfo] 获取增强Amazon价格信息:', props.extCode, forceRefresh ? '(强制刷新)' : '')

    // 使用增强的Amazon价格服务获取数据，包含业务计算字段
    const enhancedData = await amazonPriceService.getEnhancedAmazonPriceInfo(
      props.extCode,
      props.exchangeRate,
      props.officialDeclaredPrice,
      forceRefresh
    )

    enhancedPriceData.value = enhancedData
    dataSource.value = enhancedData.dataSource === 'cache' ? '(缓存24h)' : '(实时)'

    // 发出价格更新事件
    if (enhancedData.amazonPrice) {
      const updateData = {
        usdPrice: enhancedData.amazonPrice.usdPrice,
        stock: parseInt(enhancedData.amazonPrice.stock),
        updateTime: new Date(enhancedData.lastUpdated).toISOString(),
        officialDeclaredPrice: enhancedData.officialDeclaredPrice,
        profit: enhancedData.profit,
        profitRate: enhancedData.profitRate
      }
      emit('priceUpdated', updateData)
    }

    // 记录增强价格信息获取结果
    console.info('[AmazonPriceInfo] 增强Amazon价格信息获取成功:', {
      extCode: enhancedData.extCode,
      hasAmazonPrice: !!enhancedData.amazonPrice,
      hasOfficialPrice: !!enhancedData.officialDeclaredPrice,
      profit: enhancedData.profit,
      profitRate: enhancedData.profitRate,
      dataSource: enhancedData.dataSource,
      lastUpdated: enhancedData.lastUpdated
    })
  } catch (error) {
    console.error('[AmazonPriceInfo] 增强价格信息获取失败:', error)
    enhancedPriceData.value = null
  } finally {
    loading.value = false
  }
}

// 监听extCode变化
watch(() => props.extCode, (newExtCode) => {
  if (newExtCode && newExtCode.includes('[am]')) {
    refreshAmazonPrice(false) // 不强制刷新，优先使用缓存
  }
}, { immediate: true })

// 组件挂载时获取数据
onMounted(() => {
  if (props.extCode && props.extCode.includes('[am]')) {
    refreshAmazonPrice(false) // 不强制刷新，优先使用缓存
  }
})
</script>

<style>
.amazon-price-info {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  background: #ffffff;
  margin: 4px 0;
}

.amazon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.amazon-title {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.refresh-btn {
  padding: 0;
  height: auto;
  font-size: 11px;
  color: #1677ff;
}

.amazon-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
  font-size: 11px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-weight: normal;
  min-width: 60px;
}

.label-inline {
  color: #666;
  font-weight: normal;
  margin-left: 8px;
}

.platform-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.platform-link {
  color: #1677ff;
  text-decoration: none;
  font-weight: 500;
  padding: 1px 4px;
  border-radius: 2px;
  border: 1px solid #1677ff;
  font-size: 10px;
}

.platform-link:hover {
  background: #1677ff;
  color: white;
}

.separator {
  color: #ccc;
  font-size: 10px;
}

.stock-value {
  font-size: 11px;
  font-weight: 500;
}

.stock-high {
  color: #52c41a;
}

.stock-medium {
  color: #faad14;
}

.stock-low {
  color: #ff4d4f;
}

.cost-value {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cny-cost {
  font-size: 11px;
  font-weight: 500;
  color: #52c41a;
}

.usd-cost {
  font-size: 10px;
  color: #999;
}

.declared-price {
  font-size: 11px;
  font-weight: 500;
  color: #ff4d4f;
}

.official-price {
  font-size: 11px;
  font-weight: 500;
  color: #1677ff;
}

.profit-value {
  display: flex;
  align-items: center;
  gap: 4px;
}

.profit-amount {
  font-size: 11px;
  font-weight: 500;
}

.profit-high {
  color: #52c41a;
}

.profit-medium {
  color: #faad14;
}

.profit-low {
  color: #ff4d4f;
}

.profit-rate {
  font-size: 10px;
  color: #999;
}

.update-time {
  margin-top: 4px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding-top: 4px;
  border-top: 1px solid #f0f0f0;
}

.time-value {
  font-size: 9px;
  color: #999;
}

.data-source {
  font-size: 8px;
  color: #1677ff;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.loading-indicator {
  width: 12px;
  height: 12px;
  border: 1px solid #f0f0f0;
  border-top: 1px solid #1677ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-left: 6px;
  font-size: 10px;
  color: #666;
}

.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.no-data-text {
  font-size: 10px;
  color: #999;
}
</style>
