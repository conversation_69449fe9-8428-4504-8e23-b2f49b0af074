<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// 自动下架商品表单
const delistForm = ref({
  shop: '',
  site: '美国站',
  quickFilter: '流量限制中',
  stockThreshold: '',
  skcFilter: '',
  autoDelistThreshold: 0
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 快速筛选选项
const quickFilterOptions = [
  { value: '流量限制中', label: '流量限制中' },
  { value: '已下架', label: '已下架' },
  { value: '库存不足', label: '库存不足' }
]

// 开始自动下架
const startAutoDelist = () => {
  console.log('开始自动下架:', delistForm.value)
  alert('自动下架功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="delistForm"
      layout="vertical"
      class="space-y-6"
      @finish="startAutoDelist"
    >
      <!-- 基础配置 -->
      <a-card
        title="基础配置"
        class="mb-6"
      >
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="delistForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="8">
            <a-form-item label="站点">
              <a-select
                v-model:value="delistForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 快速筛选 -->
          <a-col :span="8">
            <a-form-item label="快速筛选">
              <a-select
                v-model:value="delistForm.quickFilter"
                placeholder="选择筛选条件"
              >
                <a-select-option
                  v-for="filter in quickFilterOptions"
                  :key="filter.value"
                  :value="filter.value"
                >
                  {{ filter.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 库存阈值 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="库存阈值">
              <a-input-number
                v-model:value="delistForm.stockThreshold"
                placeholder="输入库存阈值"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <!-- 自动下架阈值 -->
          <a-col :span="12">
            <a-form-item label="自动下架阈值">
              <a-input-number
                v-model:value="delistForm.autoDelistThreshold"
                :min="0"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="delistForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 下架规则说明 -->
      <a-card
        title="下架规则说明"
        class="mb-6"
      >
        <a-alert
          type="info"
          show-icon
          message="自动下架规则"
          description="系统将根据设置的条件自动下架不符合要求的商品，包括库存不足、流量限制等情况。"
        />
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始自动下架
        </a-button>
      </div>
    </a-form>
  </div>
</template>
