/**
 * 店小秘UI注入器 (DianXiaoMi UI Injector)
 *
 * 文件功能：专门负责在店小秘商品配置页面注入UI元素和处理页面交互
 *
 * 主要职责：
 * 1. 页面UI增强 - 在特定页面注入自定义按钮和界面元素
 * 2. 脚本注入 - 动态注入MAIN world脚本到页面主环境
 * 3. 用户交互 - 处理用户点击、表单验证等页面交互
 * 4. 数据收集 - 通过事件与页面JavaScript通信，收集配置数据
 * 5. 扩展通信 - 将收集的数据发送到扩展后台
 *
 * 运行环境：ISOLATED world (Content Script)
 * 目标页面：店小秘商品配置相关页面
 *
 * 与 dianxiaomi-api-handler.ts 的区别：
 * - 本文件：专注于页面UI注入和用户交互
 * - api-handler：专注于API调用和数据处理
 *
 * 注意：此文件只在特定的店小秘页面运行，不是通用的API处理器
 */

console.info('[DXM ISOLATED] 店小秘配置注入器开始加载...');

// 这个类现在将管理整个流程，包括注入和UI
class DxmConfigInjector {
  private readonly logPrefix = '[DXM ISOLATED]';

  constructor() {
    console.info(`${this.logPrefix} 🚀 店小秘配置注入器启动`);
    this.init();
  }

  /**
   * 初始化
   */
  private init(): void {
    if (!this.isTargetPage()) {
      console.info(`${this.logPrefix} 不在目标页面，退出`);
      return;
    }
    
    // 1. 动态注入 MAIN world 脚本
    this.injectMainWorldScript();

    // 2. 设置 UI
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupUI());
    } else {
      this.setupUI();
    }
  }

  /**
   * 动态注入 MAIN world 脚本到页面主环境
   */
  private async injectMainWorldScript(): Promise<void> {
    try {
      console.info(`${this.logPrefix} 开始注入 MAIN world 脚本...`);
      const scriptUrl = chrome.runtime.getURL('src/lib/dianxiaomi-main-world-injector.js');

      const script = document.createElement('script');
      script.src = scriptUrl;
      script.type = 'text/javascript';

      script.onload = () => {
        console.info(`${this.logPrefix} MAIN world 脚本已加载并执行`);
        script.remove();
      };
      script.onerror = (error) => {
        console.error(`${this.logPrefix} MAIN world 脚本加载失败:`, error);
        this.showNotification('核心脚本加载失败，请刷新页面重试', 'error');
        script.remove();
      };

      (document.head || document.documentElement).appendChild(script);
    } catch (error) {
      console.error(`${this.logPrefix} 注入 MAIN world 脚本时发生异常:`, error);
    }
  }

  /**
   * 设置页面 UI
   */
  private setupUI(): void {
    if (document.querySelector('#dxm-save-config-btn')) return;

    try {
      console.info(`${this.logPrefix} 开始设置 UI`);
      this.hideOriginalSaveButtons();
      this.createSaveConfigButton();
      this.injectStyles();
      console.info(`${this.logPrefix} UI 设置完成`);
    } catch (error) {
      console.error(`${this.logPrefix} UI 设置失败:`, error);
    }
  }
  
  // ... hideOriginalSaveButtons, createSaveConfigButton, injectStyles 方法保持不变 ...

  /**
   * 处理保存配置
   */
  private async handleSaveConfig(): Promise<void> {
    console.info(`${this.logPrefix} 开始保存配置`);
    
    // 调用页面现有的验证功能
    const validationResult = await this.validateUsingPageValidation();
    if (!validationResult.isValid) {
      this.showNotification(`表单验证失败：${validationResult.message}`, 'error');
      return;
    }
    
    this.updateButtonState('loading', '保存中...');

    try {
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('操作超时 (5s)，页面函数可能无响应。')), 5000)
      );

      const dataPromise = new Promise<Record<string, unknown>>((resolve, reject) => {
        const handleDataResponse = (event: CustomEvent) => {
          window.removeEventListener('dxm-data-response', handleDataResponse as EventListener);
          if (event.detail.success) {
            console.info(`${this.logPrefix} 从 MAIN world 接收到数据:`, event.detail.data);
            resolve(event.detail.data);
          } else {
            reject(new Error(event.detail.error));
          }
        };
        window.addEventListener('dxm-data-response', handleDataResponse as EventListener);

        const pushtype = this.getCurrentPushType();
        console.info(`${this.logPrefix} 请求数据，pushtype: ${pushtype}`);
        window.dispatchEvent(new CustomEvent('dxm-request-data', { detail: { pushtype } }));
      });

      const rawData = await Promise.race([dataPromise, timeoutPromise]);
      const configData = this.processRawData(rawData);

      if (!configData) {
        throw new Error('处理配置数据失败');
      }

      await this.sendDataToExtension(configData);

      this.updateButtonState('success', '保存成功');
      this.showNotification('配置数据保存成功！', 'success');

    } catch (error) {
      console.error(`${this.logPrefix} 保存配置失败:`, error);
      this.updateButtonState('error', '保存失败');
      this.showNotification(`保存失败: ${(error as Error).message}`, 'error');
    } finally {
      setTimeout(() => this.updateButtonState('idle', '保存上品设置'), 3000);
    }
  }

  // === 以下是辅助方法 ===
  /**
   * 使用页面现有的验证功能
   */
  private async validateUsingPageValidation(): Promise<{ isValid: boolean; message: string }> {
    try {
      console.info(`${this.logPrefix} 调用页面验证功能`);
      
      // 方案1: 尝试调用页面的验证函数
      const globalWindow = window as unknown as { validateForm?: () => boolean | { success: boolean; message?: string } };
      if (typeof globalWindow.validateForm === 'function') {
        const result = globalWindow.validateForm();
        if (result === false || (typeof result === 'object' && !result.success)) {
          const errorMsg = typeof result === 'object' && 'message' in result ? result.message : '表单验证失败，请检查必填项';
          return { isValid: false, message: errorMsg || '表单验证失败，请检查必填项' };
        }
      }
      
      // 方案2: 尝试查找并触发原始提交按钮的验证
      const originalSubmitBtn = document.querySelector('button[type="submit"], input[type="submit"], .btn-submit, #submit-btn');
      if (originalSubmitBtn) {
        // 创建一个临时的点击事件来触发验证
        const clickEvent = new MouseEvent('click', { bubbles: true, cancelable: true });
        const validation = this.interceptValidation(originalSubmitBtn as HTMLElement, clickEvent);
        if (!validation.isValid) {
          return validation;
        }
      }
      
      // 方案3: 检查jQuery验证器（如果页面使用jQuery Validate）
      const globalWithJQuery = window as unknown as { $?: { (selector: string): { length: number; first(): { valid?: () => boolean } } } };
      if (typeof globalWithJQuery.$ !== 'undefined' && globalWithJQuery.$('form').length > 0) {
        const $form = globalWithJQuery.$('form').first();
        if (typeof $form.valid === 'function') {
          const isValid = $form.valid();
          if (!isValid) {
            return { isValid: false, message: '请完善表单中的必填信息' };
          }
        }
      }
      
      // 方案4: 检查HTML5原生验证
      const form = document.querySelector('form');
      if (form && !form.checkValidity()) {
        return { isValid: false, message: '请完善表单中的必填信息' };
      }
      
      // 方案5: 检查页面是否有错误提示显示
      const errorMessages = document.querySelectorAll('.error, .invalid, .field-error, .form-error, .alert-danger');
      const visibleErrors = Array.from(errorMessages).filter(el => {
        const style = window.getComputedStyle(el);
        return style.display !== 'none' && style.visibility !== 'hidden' && el.textContent?.trim();
      });
      
      if (visibleErrors.length > 0) {
        const errorText = visibleErrors[0].textContent?.trim() || '表单验证失败';
        return { isValid: false, message: errorText };
      }
      
      console.info(`${this.logPrefix} 页面验证通过`);
      return { isValid: true, message: '' };
      
    } catch (error) {
      console.error(`${this.logPrefix} 页面验证出错:`, error);
      // 验证出错时允许继续，但给出警告
      return { isValid: true, message: '' };
    }
  }

  /**
   * 拦截验证过程
   */
  private interceptValidation(button: HTMLElement, event: MouseEvent): { isValid: boolean; message: string } {
    let validationFailed = false;
    let errorMessage = '';
    
    // 临时阻止默认行为来检查验证
    const originalHandler = button.onclick;
    
    // 临时替换点击处理器
    button.onclick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      
      // 恢复原始处理器
      button.onclick = originalHandler;
      
      // 检查是否有验证错误出现
      setTimeout(() => {
        const errors = document.querySelectorAll('.error, .invalid, .field-error');
        if (errors.length > 0) {
          validationFailed = true;
          errorMessage = errors[0].textContent?.trim() || '验证失败';
        }
      }, 100);
    };
    
    // 触发点击
    button.click();
    
    return { 
      isValid: !validationFailed, 
      message: errorMessage || '请完善必填信息' 
    };
  }

  private isTargetPage(): boolean {
    const url = window.location.href;
    return url.includes('popTemuAdd.htm') || url.includes('popTemuEdit.htm') || url.includes('popTemuProduct') || url.includes('tikTokProduct') || url.includes('localTemu') || url.includes('sheinProduct') || url.includes('smtlocalProduct');
  }
  private hideOriginalSaveButtons(): void {
    try {
      const formFooter = document.querySelector('.form-actions');
      if (formFooter) {
        const buttons = formFooter.querySelectorAll('button, input[type="button"], input[type="submit"]');
        buttons.forEach(btn => {
          (btn as HTMLElement).classList.add('dxm-hidden-button');
        });
      }
    } catch(e) { console.error('Hiding buttons failed', e); }
  }
  private createSaveConfigButton() {
    if (document.querySelector('#dxm-save-config-btn')) return;
    const button = document.createElement('button');
    button.id = 'dxm-save-config-btn';
    button.textContent = '保存上品设置';
    button.className = 'dxm-btn dxm-btn-primary';
    button.addEventListener('click', () => this.handleSaveConfig());
    document.body.appendChild(button);
  }
  private injectStyles() {
    if (document.querySelector('#dxm-injected-styles')) return;
    const style = document.createElement('style');
    style.id = 'dxm-injected-styles';
    style.textContent = `
      .dxm-btn { position: fixed !important; bottom: 120px !important; right: 30px !important; z-index: 2147483647 !important; padding: 12px 16px !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; border: none !important; border-radius: 6px !important; box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important; cursor: pointer !important; transition: background-color 0.3s, transform 0.1s; }
      .dxm-btn:active { transform: scale(0.98); }
      .dxm-btn-primary, .dxm-btn-idle { background-color: #1890ff !important; }
      .dxm-hidden-button { display: none !important; }
      .dxm-btn-loading { background-color: #a0a0a0 !important; cursor: wait !important; }
      .dxm-btn-success { background-color: #52c41a !important; }
      .dxm-btn-error { background-color: #ff4d4f !important; }
      .dxm-notification { position: fixed; top: 20px; right: 20px; padding: 12px 20px; border-radius: 4px; color: white; font-size: 14px; z-index: 2147483647; max-width: 350px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); line-height: 1.5; }
      .dxm-notification-success { background-color: #52c41a; }
      .dxm-notification-error { background-color: #ff4d4f; }
      .dxm-notification-info { background-color: #1890ff; }
    `;
    document.head.appendChild(style);
  }
  private getCurrentPushType(): string {
    const urlParams = new URLSearchParams(window.location.search);
    const pushtype = urlParams.get('pushtype');
    if (pushtype) return pushtype;
    return 'popTemuProduct'; // Default
  }
  private processRawData(rawData: Record<string, unknown>): Record<string, unknown> | null {
    if (!rawData) return null;
    return {
      timestamp: new Date().toISOString(),
      source: 'dianxiaomi_system_function',
      url: window.location.href,
      pushtype: this.getCurrentPushType(),
      rawData: rawData,
      id: rawData.id || '',
      name: rawData.name || '',
      shopId: rawData.shopId || '',
      platform: this.getPlatformFromPushType(this.getCurrentPushType()),
      categoryId: rawData.categoryId || '',
      attributes: rawData.attributes || [],
      freightTemplateId: rawData.freightTemplateId || '',
      warehouseId: rawData.warehouseId || '',
      businessSite: rawData.businessSite || rawData.region2Id || ''
    };
  }
  private getPlatformFromPushType(pushtype: string): string {
    if (pushtype.toLowerCase().includes('temu')) return 'Temu';
    if (pushtype.toLowerCase().includes('tiktok')) return 'TikTok';
    if (pushtype.toLowerCase().includes('shein')) return 'Shein';
    if (pushtype.toLowerCase().includes('smt')) return 'SMT';
    return 'Unknown';
  }
  private async sendDataToExtension(configData: Record<string, unknown>) {
    try {
      console.info(`${this.logPrefix} 完整的 configData:`, configData);

      // 提取分类信息和属性数据
      const categoryInfo = (configData as any).rawData.categoryInfo;
      const attributes = (configData as any).attributes;

      console.info(`${this.logPrefix} 提取的数据:`, {
        categoryInfo: categoryInfo,
        attributes: attributes,
        configDataKeys: Object.keys(configData),
        hasCategoryInfo: !!categoryInfo,
        hasAttributes: !!attributes,
        attributesLength: attributes ? attributes.length : 0
      });

      // 更新 temu-extension-basic-config
      await this.updateTemuExtensionBasicConfig(attributes, categoryInfo);

      // 保存分类信息到插件本地缓存
      if (categoryInfo) {
        await this.saveCategoryInfoToPluginCache(categoryInfo, configData);
      }

      await chrome.storage.local.set({ 'dxm_config_data': configData });
      await chrome.runtime.sendMessage({
        action: 'DXM_CONFIG_DATA_SAVED',
        data: configData,
        categoryInfo: categoryInfo
      });
    } catch (e) {
      if (!chrome.runtime?.id) { throw new Error('扩展上下文已失效，请刷新页面。'); }
      throw e;
    }
  }

  /**
   * 更新 temu-extension-basic-config 中的数据
   */
  private async updateTemuExtensionBasicConfig(attributes: any, categoryInfo: any): Promise<void> {
    try {
      console.info(`${this.logPrefix} 开始更新 temu-extension-basic-config...`)
      console.info(`${this.logPrefix} 接收到的参数:`, {
        attributes: attributes,
        categoryInfo: categoryInfo,
        attributesType: typeof attributes,
        categoryInfoType: typeof categoryInfo,
        attributesLength: Array.isArray(attributes) ? attributes.length : 'not array'
      });

      // 获取现有的配置
      const result = await chrome.storage.local.get(['temu-extension-basic-config'])
      const basicConfig = result['temu-extension-basic-config'] || {}

      console.info(`${this.logPrefix} 现有配置:`, basicConfig);

      // 更新 productAttributes
      if (attributes && Array.isArray(attributes)) {
        const productAttributes: Record<string, any> = {}

        // 遍历 attributes 数组，转换为 productAttributes 格式
        attributes.forEach((item, index) => {
          if (item && typeof item === 'object' && item.propName) {
            productAttributes[index.toString()] = {
              id: item.propName.toLowerCase().replace(/\s+/g, '_'),
              name: item.propName,
              numberInputValue: item.numberInputValue || "",
              pid: item.pid,
              propName: item.propName,
              propValue: item.propValue || "",
              refPid: item.refPid,
              required: false,
              templatePid: item.templatePid,
              type: "select",
              value: item.propValue || "",
              valueUnit: item.valueUnit || "",
              vid: item.vid
            }
          }
        })

        basicConfig.productAttributes = productAttributes
        console.info(`${this.logPrefix} 更新了 ${Object.keys(productAttributes).length} 个产品属性`)
        console.info(`${this.logPrefix} 产品属性详情:`, productAttributes)
      } else {
        console.warn(`${this.logPrefix} attributes 为空或不是数组:`, attributes)
      }

      // 更新分类信息
      if (categoryInfo) {
        basicConfig.categoryInfo = categoryInfo
        basicConfig.productCategory = categoryInfo.categoryName
        console.info(`${this.logPrefix} 更新了分类信息: ${categoryInfo.categoryName}`)
        console.info(`${this.logPrefix} 分类信息详情:`, categoryInfo)
      } else {
        console.warn(`${this.logPrefix} categoryInfo 为空或未定义`)
      }

      // 保存更新后的配置
      console.info(`${this.logPrefix} 准备保存的配置:`, basicConfig)
      await chrome.storage.local.set({ 'temu-extension-basic-config': basicConfig })
      console.info(`${this.logPrefix} temu-extension-basic-config 更新完成`)

      // 验证保存结果
      const verifyResult = await chrome.storage.local.get(['temu-extension-basic-config'])
      console.info(`${this.logPrefix} 验证保存结果:`, verifyResult['temu-extension-basic-config'])

    } catch (error) {
      console.error(`${this.logPrefix} 更新 temu-extension-basic-config 失败:`, error)
    }
  }

  /**
   * 保存分类信息到插件本地缓存
   */
  private async saveCategoryInfoToPluginCache(categoryInfo: any, fullData: any): Promise<void> {
    try {
      console.info(`${this.logPrefix} 开始保存分类信息到插件缓存...`)

      // 构建符合插件缓存格式的数据结构
      const pluginCacheData = {
        apiConfig: {
          erp: "dianXiaoMi",
          imageSize: 800,
          platform: "temu",
          pointKey: "popTemuSave",
          shippingFrom: {
            "100": ["us"],
            "101": [],
            "102": ["gb", "de", "uk"],
            "118": ["jp"],
            "103,104": [],
            "105,106,107,109": []
          },
          txt: "popTemuSave.txt",
          url: "api/popTemuProduct/add.json"
        },
        data: {
          attributes: (fullData as any).attributes || "[]", // 原样保存 attributes 字段
          categoryId: (fullData as any).categoryId || categoryInfo.categoryId, // 优先使用原数据的 categoryId
          categoryType: "0",
          draftImgUrl: "",
          dxmPdfUrl: "",
          dxmState: "",
          freightTemplateId: (fullData as any).freightTemplateId || "",
          fullCid: "",
          goodsModel: "",
          id: null,
          instructionsId: "",
          instructionsName: "",
          instructionsTranslateId: "",
          op: 3,
          optionValue: "\"[]\"",
          outerGoodsUrl: "",
          packageImages: "",
          packageShape: "",
          packageType: "",
          productId: null,
          productOrigin: "CN",
          productSemiManagedReq: "100",
          productWarehouseRouteReq: (fullData as any).productWarehouseRouteReq || [],
          qualifiedEn: "",
          region2Id: (fullData as any).region2Id || "",
          sensitiveAttr: "",
          shipmentLimitSecond: (fullData as any).shipmentLimitSecond || "172800",
          shopId: (fullData as any).shopId || "",
          shopInfo: (fullData as any).shopInfo || {},
          sizeTemplateIds: ""
        },
        form: {
          attributes: (fullData as any).attributes || "[]", // 原样保存 attributes 字段
          categoryId: (fullData as any).categoryId || categoryInfo.categoryId, // 优先使用原数据的 categoryId
          categoryName: categoryInfo.categoryName, // 使用提取的分类名称
          categoryType: 0,
          certificationInfo: {},
          freightTemplateId: (fullData as any).freightTemplateId || "",
          op: 3,
          shipmentLimitSecond: (fullData as any).shipmentLimitSecond || "172800",
          shopId: (fullData as any).shopId || "",
          siteId: "100",
          siteIds: [],
          warehouseId: []
        },
        siteConfig: {
          currentSite: "ShangTemuView",
          erp: "dianXiaoMi"
        },
        userInfo: (fullData as any).userInfo || {},
        categoryInfo: categoryInfo
      }

      // 保存到 Chrome storage
      await chrome.storage.local.set({
        'dxm_product_config_cache': pluginCacheData,
        'dxm_category_info_cache': categoryInfo,
        'dxm_last_update': Date.now()
      })

      console.info(`${this.logPrefix} 分类信息已保存到插件缓存:`, categoryInfo)
      console.info(`${this.logPrefix} 完整配置已保存到插件缓存`)

    } catch (error) {
      console.error(`${this.logPrefix} 保存到插件缓存失败:`, error)
    }
  }
  private updateButtonState(state: string, text: string) {
    const button = document.querySelector('#dxm-save-config-btn');
    if (button) {
      button.textContent = text;
      button.className = `dxm-btn dxm-btn-${state}`;
    }
  }
  private showNotification(message: string, type: 'success' | 'error' | 'info' = 'info') {
    try {
      const notification = document.createElement('div');
      notification.className = `dxm-notification dxm-notification-${type}`;
      notification.innerHTML = message; // 使用 innerHTML 支持换行
      document.body.appendChild(notification);
      
      // 错误消息显示更长时间
      const duration = type === 'error' ? 5000 : 3000;
      setTimeout(() => notification.remove(), duration);
    } catch (error) { 
      console.error('showNotification failed', error); 
    }
  }
}

// 启动注入器
new DxmConfigInjector();