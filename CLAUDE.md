# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
请使用中文回复。

## Project Overview

This is a Chrome browser extension for Temu and Amazon product management with integration to the DianXiaoMi (店小秘) e-commerce platform. The extension helps users collect product data from Amazon, map it to the DianXiaoMi format, and upload products efficiently.

## Development Commands

### Build and Development
- `npm run dev` - Start development mode for both Chrome and Firefox
- `npm run dev:chrome` - Development mode for Chrome only
- `npm run dev:firefox` - Development mode for Firefox only
- `npm run build` - Build for both browsers
- `npm run build:chrome` - Build Chrome extension to `dist/chrome/`
- `npm run build:firefox` - Build Firefox extension to `dist/firefox/`

### Code Quality
- `npm run lint` - Run ESLint with auto-fix and caching
- `npm run typecheck` - Run TypeScript type checking without emitting files
- `npm run format` - Format code with Prettier

### Testing
- Tests are located in `src/services/__tests__/`
- Run individual tests with tools in the testing infrastructure

### Extension Development
- `npm run launch` - Launch extension in browser for testing
- `npm run launch:all` - Launch in all available browsers
- `npm run lint:manifest` - Validate manifest.json files

## Architecture Overview

### Multi-Context Extension Structure
The extension operates across multiple browser contexts:

- **Background Script** (`src/background/index.ts`) - Main service worker handling API calls, data processing, and cross-origin requests
- **Content Scripts** - Inject into web pages to collect data and interact with page content
  - `src/content-scripts/index.ts` - Main content script
  - `src/content-scripts/amazon/` - Amazon-specific collection logic
  - `src/content-scripts/temu/` - Temu platform integration
  - `src/content-scripts/dxm/` - DianXiaoMi platform integration
- **UI Components** - Multiple interface entry points
  - `src/ui/side-panel/` - Main management interface (side panel)
  - `src/ui/action-popup/` - Browser action popup
  - `src/ui/options-page/` - Extension options/settings
  - `src/ui/setup/` - First-time setup flow

### Service Architecture
Services are organized by functionality in `src/services/`:

- **Amazon Services** (`src/services/amazon/`)
  - Data collection, price monitoring, product assembly
- **DianXiaoMi Services** (`src/services/dxm/`)
  - API integration, image upload, token management
- **Core Services**
  - `configStorageService.ts` - Configuration and settings management
  - `dataMapperService.ts` - Data transformation between platforms
  - `imageProcessingService.ts` - Image handling and optimization

### State Management
- Uses Pinia stores in `src/stores/`
- `useBrowserStorage` composables for persistent data
- Configuration stored in browser's local/sync storage

## Platform Integration

### Amazon Integration
- Extracts product data from Amazon product pages
- Collects pricing, images, descriptions, and specifications
- Handles product variations and SKU data
- Price monitoring and stock tracking

### DianXiaoMi (店小秘) Integration
- API authentication and token management
- Product upload with proper formatting
- Category and attribute mapping
- Image upload to DianXiaoMi's image service
- Shop and warehouse management

### Temu Integration
- Seller dashboard integration
- Product management on Temu platform
- Order and inventory synchronization

## Key Technical Patterns

### Content Script Communication
- Uses `webext-bridge` for cross-context messaging
- Background script acts as central message hub
- MAIN world scripts for page-level API access

### Data Processing Pipeline
1. **Collection** - Content scripts extract raw data
2. **Assembly** - Background services structure data
3. **Mapping** - Transform data between platform formats
4. **Validation** - Ensure data meets platform requirements
5. **Upload** - Submit to destination platform APIs

### Image Handling
- Downloads images to base64 for cross-origin access
- Processes and optimizes images for platform requirements
- Bulk image upload with progress tracking

## Configuration Management

### Extension Configuration
- Basic settings: language, theme, notification preferences
- Platform credentials and authentication tokens
- Product mapping rules and default values
- Store and warehouse configurations

### Product Configuration
- Category mappings between Amazon and DianXiaoMi
- Attribute transformations and value mappings
- Pricing rules and markup calculations
- Image processing preferences

## File Structure Notes

### Manifest Configuration
- Base config: `manifest.config.ts`
- Browser-specific: `manifest.chrome.config.ts`, `manifest.firefox.config.ts`
- Content script configurations define precise URL matching

### Build Configuration
- Vite-based build system with browser-specific configs
- TypeScript with strict typing enabled
- Vue 3 with Composition API throughout
- Auto-imports for commonly used functions and components

### Development Tools
- ESLint configuration supports TypeScript and Vue
- Prettier for code formatting
- Development server with HMR support
- Browser extension development utilities

## Important Implementation Details

### Cross-Origin Data Access
- Background script handles all external API calls
- Content scripts proxy requests through background
- Uses Chrome extension permissions for cross-origin access

### Authentication Management
- Token caching with expiration handling
- Automatic token refresh mechanisms
- Secure credential storage in browser storage

### Data Validation
- Comprehensive validation for product data
- Platform-specific requirement checking
- Error handling with user-friendly messages

### Performance Considerations
- Image processing done in background to avoid blocking UI
- Batch operations for multiple product handling
- Caching mechanisms for frequently accessed data

## Testing and Debugging

### Development Testing
- Use `test_upload_final.html` for upload functionality testing
- Browser developer tools for content script debugging
- Extension management page for background script logs

### Production Deployment
- Built extensions in `dist/` directories
- ZIP files created for store submission
- Manifest validation before deployment

## Dependencies of Note

- **Vue 3** + **TypeScript** - Modern reactive UI framework
- **Pinia** - State management
- **VueUse** - Composition utilities
- **webext-bridge** - Cross-context messaging
- **JSZip** - File compression for uploads
- **Ant Design Vue** - UI component library

## Security Considerations

- All external API calls go through background script
- Sensitive data (tokens, credentials) stored securely
- Content scripts have minimal permissions
- Cross-origin requests properly authenticated