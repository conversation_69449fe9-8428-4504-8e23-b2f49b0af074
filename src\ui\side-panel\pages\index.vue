<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 自动重定向到仪表板页面
const router = useRouter()
const route = useRoute()

// 在组件挂载后进行重定向，避免闪烁
onMounted(() => {
  // 如果访问的是根路径，重定向到仪表板页面
  if (route.path === '/side-panel' || route.path === '/side-panel/') {
    router.replace('/side-panel/dashboard')
  }
})
</script>

<template>
  <div class="h-full flex items-center justify-center bg-gray-50">
    <a-spin
      size="large"
      tip="正在加载..."
    >
      <div class="w-32 h-32"></div>
    </a-spin>
  </div>
</template>
