// ================================================================
//  页面环境拦截器 (page-interceptor.js)
//  此文件将通过 manifest.json 的 "world": "MAIN" 直接注入页面
// ================================================================

(function() {
  // 防止重复注入
  if (window.hasTemuInterceptorInjected) {
    return;
  }
  window.hasTemuInterceptorInjected = true;

  console.log('🚀 [Page-Interceptor] 脚本已注入页面主环境，开始设置拦截器...');

  const storeToLocalStorage = (key, value, expiryMinutes) => {
    try {
      const expiry = Date.now() + expiryMinutes * 60 * 1000;
      localStorage.setItem(`temu_cs_${key}`, value);
      localStorage.setItem(`temu_cs_${key}_expiry`, expiry.toString());
    } catch (e) {
      console.warn(`[Page-Interceptor] 保存 ${key} 到 localStorage 失败:`, e);
    }
  };

  const checkAndStoreHeaders = (headers, source) => {
    const lowerCaseHeaders = {};
    for (const key in headers) {
      lowerCaseHeaders[key.toLowerCase()] = headers[key];
    }

    const antiContent = lowerCaseHeaders['anti-content'];
    const mallId = lowerCaseHeaders['mallid'];
    const authorization = lowerCaseHeaders['authorization'];
    const xRequestedWith = lowerCaseHeaders['x-requested-with'];
    const xCsrfToken = lowerCaseHeaders['x-csrf-token'];
    const sessionId = lowerCaseHeaders['session-id'] || lowerCaseHeaders['sessionid'];

    if (antiContent) {
      console.log(`🎉 [Page-Interceptor] 捕获到 anti-content (来自 ${source})!`);
      storeToLocalStorage('anti_content', antiContent, 30); // 30分钟有效期
    }

    if (mallId) {
      console.log(`🎉 [Page-Interceptor] 捕获到 mallId (来自 ${source})!`);
      storeToLocalStorage('mall_id', mallId, 60); // 60分钟有效期
    }

    if (authorization) {
      console.log(`🎉 [Page-Interceptor] 捕获到 authorization (来自 ${source})!`);
      storeToLocalStorage('authorization', authorization, 30);
    }

    if (xRequestedWith) {
      console.log(`🎉 [Page-Interceptor] 捕获到 x-requested-with (来自 ${source})!`);
      storeToLocalStorage('x_requested_with', xRequestedWith, 60);
    }

    if (xCsrfToken) {
      console.log(`🎉 [Page-Interceptor] 捕获到 x-csrf-token (来自 ${source})!`);
      storeToLocalStorage('x_csrf_token', xCsrfToken, 30);
    }

    if (sessionId) {
      console.log(`🎉 [Page-Interceptor] 捕获到 session-id (来自 ${source})!`);
      storeToLocalStorage('session_id', sessionId, 60);
    }
  };

  // 1. 拦截 fetch
  const originalFetch = window.fetch;
  window.fetch = function (...args) {
    const [input, options] = args;

    // ==================【已修正部分 - START】==================
    // 之前的问题：当 fetch 的参数是 Request 对象时，无法正确获取 URL。
    // 修正：分别处理字符串和 Request 对象，以正确获取 URL。
    let urlString;
    if (typeof input === 'string') {
      urlString = input;
    } else if (input instanceof Request) {
      urlString = input.url;
    } else {
      urlString = input.toString(); // 兼容 URL 对象等其他情况
    }

    console.log(`🔍 [Page-Interceptor] 检测到 fetch 请求: ${urlString}`);

    // 域名匹配逻辑现在可以正常工作
    if (urlString.includes('kuajingmaihuo.com') || urlString.includes('temu.com')) {
      console.log(`✅ [Page-Interceptor] 匹配到目标域名: ${urlString}`);

      // 之前的问题：只从 options 中获取 headers，忽略了 Request 对象自身的 headers。
      // 修正：稳健地合并来自 Request 对象和 options 的 headers。
      const combinedHeaders = {};

      // 步骤 1: 从 Request 对象（如果存在）提取 headers
      if (input instanceof Request) {
        input.headers.forEach((value, key) => {
          combinedHeaders[key] = value;
        });
      }

      // 步骤 2: 从 options 对象（如果存在）提取 headers，它会覆盖 Request 对象中的同名 header
      if (options && options.headers) {
        if (options.headers instanceof Headers) {
          options.headers.forEach((value, key) => {
            combinedHeaders[key] = value;
          });
        } else { // 如果是普通对象
          Object.assign(combinedHeaders, options.headers);
        }
      }

      if (Object.keys(combinedHeaders).length > 0) {
        console.log(`📋 [Page-Interceptor] 请求头:`, combinedHeaders);
        checkAndStoreHeaders(combinedHeaders, 'fetch');
      } else {
        console.log(`⚠️ [Page-Interceptor] fetch 请求没有 headers`);
      }
    }
    // ==================【已修正部分 - END】==================

    return originalFetch.apply(this, args);
  };

  // 2. 拦截 XHR (此部分逻辑正确，无需修改)
  const originalXHRSetHeader = XMLHttpRequest.prototype.setRequestHeader;
  XMLHttpRequest.prototype.setRequestHeader = function (name, value) {
    const lowerName = name.toLowerCase();
    // 减少不必要的日志，只在捕获到重要头部时打印
    // console.log(`🔍 [Page-Interceptor] XHR 设置头部: ${name} = ${value.substring(0, 50)}...`);

    const importantHeaders = [
      'anti-content', 'mallid', 'authorization',
      'x-requested-with', 'x-csrf-token', 'session-id', 'sessionid'
    ];

    if (importantHeaders.includes(lowerName)) {
        console.log(`✅ [Page-Interceptor] XHR.setRequestHeader 捕获到重要头部: ${name}`);
        // 确保能关联到 URL
        const urlForContext = this._interceptorUrl || '未知URL';
        if (urlForContext.includes('kuajingmaihuo.com') || urlForContext.includes('temu.com')) {
           checkAndStoreHeaders({ [name]: value }, 'XHR-SetHeader');
        }
    }
    return originalXHRSetHeader.call(this, name, value);
  };

  // 3. 拦截 XHR open 方法以获取 URL 上下文 (此部分逻辑正确，无需修改)
  const originalXHROpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(method, url, ...args) {
    this._interceptorUrl = url; // 将 URL 存储在 XHR 实例上，供 setRequestHeader 使用
    console.log(`🔍 [Page-Interceptor] XHR 请求: ${method} ${url}`);
    return originalXHROpen.call(this, method, url, ...args);
  };
  
  // 4. 添加全局调试函数 (此部分逻辑正确，无需修改)
  window.temuInterceptorDebug = {
    getStoredData: () => {
      const keys = ['anti_content', 'mall_id', 'authorization', 'x_requested_with', 'x_csrf_token', 'session_id'];
      const data = {};
      keys.forEach(key => {
        const value = localStorage.getItem(`temu_cs_${key}`);
        const expiry = localStorage.getItem(`temu_cs_${key}_expiry`);
        data[key] = { 
          value, 
          expiry: expiry ? new Date(parseInt(expiry)).toLocaleString() : null,
          isExpired: expiry ? Date.now() > parseInt(expiry) : null 
        };
      });
      console.table(data);
      return data;
    },
    clearStoredData: () => {
      const keys = ['anti_content', 'mall_id', 'authorization', 'x_requested_with', 'x_csrf_token', 'session_id'];
      keys.forEach(key => {
        localStorage.removeItem(`temu_cs_${key}`);
        localStorage.removeItem(`temu_cs_${key}_expiry`);
      });
      console.log('🧹 [Page-Interceptor] 已清除所有存储的数据');
    }
  };

  console.log('✅ [Page-Interceptor] 所有拦截器设置完毕，正在监听所有网络请求...');
  console.log('🔧 [Page-Interceptor] 调试工具已添加到 window.temuInterceptorDebug，可在控制台使用 getStoredData() 和 clearStoredData()。');
})();