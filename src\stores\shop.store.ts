import { defineStore } from 'pinia'
import { ref, readonly } from 'vue'

// 子账号数据类型
export interface SubAccount {
  id: string
  loginId: string
  password: string
  createTime: string
  remark: string
  status: 'active' | 'inactive'
}

// 商品配置数据类型
export interface ShopProductConfig {
  erpPlatform: string
  publishSite: string
  autoPublish: boolean
  priceStrategy: string
  stockSync: boolean
  imageOptimization: boolean
  publishInterval?: string
  publishLimit?: number
  profitRate?: number
  priceSync?: boolean
  stockThreshold?: number
  outOfStockAction?: string
  watermark?: boolean
  imageQuality?: string
}

// 店铺信息类型
export interface ShopInfo {
  shopCount: string
  points: number
  imageTranslation: number
}

export const useShopStore = defineStore('shop', () => {
  // 店铺信息
  const shopInfo = ref<ShopInfo>({
    shopCount: '0 / 0',
    points: 30,
    imageTranslation: 0
  })

  // 商品配置
  const productConfig = ref<ShopProductConfig>({
    erpPlatform: '1', // 店小秘
    publishSite: 'T001', // Temu 半托
    autoPublish: true,
    priceStrategy: 'auto',
    stockSync: true,
    imageOptimization: true,
    publishInterval: '立即',
    publishLimit: 10,
    profitRate: 20,
    priceSync: true,
    stockThreshold: 5,
    outOfStockAction: '下架商品',
    watermark: false,
    imageQuality: '标准'
  })

  // 子账号列表
  const subAccounts = ref<SubAccount[]>([
    {
      id: '1',
      loginId: '<EMAIL>',
      password: '********',
      createTime: '2024-01-15 10:30:00',
      remark: '测试账号1',
      status: 'active'
    },
    {
      id: '2',
      loginId: '<EMAIL>',
      password: '********',
      createTime: '2024-01-16 14:20:00',
      remark: '测试账号2',
      status: 'inactive'
    }
  ])

  // 更新店铺信息
  const updateShopInfo = (info: Partial<ShopInfo>) => {
    shopInfo.value = { ...shopInfo.value, ...info }
  }

  // 更新商品配置
  const updateProductConfig = (config: Partial<ShopProductConfig>) => {
    productConfig.value = { ...productConfig.value, ...config }
  }

  // 添加子账号
  const addSubAccount = (account: Omit<SubAccount, 'id'>) => {
    const newAccount: SubAccount = {
      ...account,
      id: Date.now().toString()
    }
    subAccounts.value.unshift(newAccount)
    return newAccount
  }

  // 更新子账号
  const updateSubAccount = (id: string, updates: Partial<SubAccount>) => {
    const index = subAccounts.value.findIndex(acc => acc.id === id)
    if (index !== -1) {
      subAccounts.value[index] = { ...subAccounts.value[index], ...updates }
      return subAccounts.value[index]
    }
    return null
  }

  // 删除子账号
  const deleteSubAccount = (id: string) => {
    const index = subAccounts.value.findIndex(acc => acc.id === id)
    if (index !== -1) {
      subAccounts.value.splice(index, 1)
      return true
    }
    return false
  }

  // 切换子账号状态
  const toggleSubAccountStatus = (id: string) => {
    const account = subAccounts.value.find(acc => acc.id === id)
    if (account) {
      account.status = account.status === 'active' ? 'inactive' : 'active'
      return account
    }
    return null
  }

  // 获取活跃子账号数量
  const getActiveSubAccountsCount = computed(() => {
    return subAccounts.value.filter(acc => acc.status === 'active').length
  })

  // 获取总子账号数量
  const getTotalSubAccountsCount = computed(() => {
    return subAccounts.value.length
  })

  return {
    // 状态
    shopInfo: readonly(shopInfo),
    productConfig: readonly(productConfig),
    subAccounts: readonly(subAccounts),
    
    // 计算属性
    getActiveSubAccountsCount,
    getTotalSubAccountsCount,
    
    // 方法
    updateShopInfo,
    updateProductConfig,
    addSubAccount,
    updateSubAccount,
    deleteSubAccount,
    toggleSubAccountStatus
  }
})
