<script setup lang="ts">
import { ref } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'

// 批量添加广告表单
const adForm = ref({
  shop: '',
  site: '',
  adType: '搜索广告',
  budget: '',
  bidAmount: '',
  targetKeywords: '',
  skcFilter: '',
  timeInterval: 3
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = [
  { value: '美国站', label: '美国站' },
  { value: '英国站', label: '英国站' },
  { value: '德国站', label: '德国站' }
]

// 广告类型选项
const adTypeOptions = [
  { value: '搜索广告', label: '搜索广告' },
  { value: '展示广告', label: '展示广告' },
  { value: '视频广告', label: '视频广告' }
]

// 开始批量添加广告
const startBatchAd = () => {
  console.log('开始批量添加广告:', adForm.value)
  alert('批量添加广告功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="adForm"
      layout="vertical"
      class="space-y-6"
      @finish="startBatchAd"
    >
      <!-- 基础配置 -->
      <a-card
        title="基础配置"
        class="mb-6"
      >
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="adForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="8">
            <a-form-item label="站点">
              <a-select
                v-model:value="adForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 广告类型 -->
          <a-col :span="8">
            <a-form-item label="广告类型">
              <a-select
                v-model:value="adForm.adType"
                placeholder="选择广告类型"
              >
                <a-select-option
                  v-for="type in adTypeOptions"
                  :key="type.value"
                  :value="type.value"
                >
                  {{ type.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- SKC筛选 -->
        <a-form-item label="SKC筛选">
          <a-input
            v-model:value="adForm.skcFilter"
            placeholder="多个查询请英文逗号、空格依次输入(选填)"
          />
        </a-form-item>
      </a-card>

      <!-- 广告设置 -->
      <a-card
        title="广告设置"
        class="mb-6"
      >
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="预算($)">
              <a-input-number
                v-model:value="adForm.budget"
                placeholder="输入广告预算"
                :step="0.01"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="出价金额($)">
              <a-input-number
                v-model:value="adForm.bidAmount"
                placeholder="输入出价金额"
                :step="0.01"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="时间间隔(秒)">
              <a-input-number
                v-model:value="adForm.timeInterval"
                :min="1"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 目标关键词 -->
        <a-form-item label="目标关键词">
          <a-textarea
            v-model:value="adForm.targetKeywords"
            placeholder="输入目标关键词，多个关键词用逗号分隔"
            :rows="3"
          />
        </a-form-item>
      </a-card>

      <!-- 广告说明 -->
      <a-card
        title="广告说明"
        class="mb-6"
      >
        <a-alert
          type="info"
          show-icon
          message="批量添加广告"
          description="根据设置的条件批量为商品创建广告活动，提高商品曝光度。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div>• 搜索广告：在用户搜索相关关键词时展示</div>
          <div>• 展示广告：在相关页面位置展示商品</div>
          <div>• 视频广告：通过视频形式展示商品</div>
          <div>• 系统会根据设置的预算和出价自动管理广告</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始批量添加
        </a-button>
      </div>
    </a-form>
  </div>
</template>
