/**
 * Amazon数据组装服务
 * 处理Amazon商品数据的组装和格式化
 *
 * 注意：此服务已重构，使用统一的数据提取器
 */

import { AmazonDataService } from './amazonDataService'
import { dianxiaomiImageUploadService } from '../dxm/imageUploadService'
import AmazonDataExtractor from './extractors/amazonDataExtractor'
import type { AmazonProductData, DianxiaomiProductData } from './types'

export class AmazonDataAssemblyService {
  /**
   * 组装Amazon数据（带图片处理）
   */
  async assembleAmazonDataWithImageProcessing(
    html: string,
    options: {
      processImages?: boolean
      maxImages?: number
      compressImages?: boolean
    } = {}
  ): Promise<{ success: boolean; data?: AmazonProductData; error?: string }> {
    try {
      console.info('[AmazonDataAssemblyService] 开始组装Amazon数据（带图片处理）')

      const {
        processImages = true,
        maxImages = 10,
        compressImages = true
      } = options

      // 1. 提取基础数据
      const basicData = await this.extractBasicAmazonData(html)
      if (!basicData.success || !basicData.data) {
        return {
          success: false,
          error: basicData.error || '提取基础数据失败'
        }
      }

      const amazonData = basicData.data

      // 2. 处理图片（如果需要）
      if (processImages && amazonData.imageUrls.length > 0) {
        try {
          console.info('[AmazonDataAssemblyService] 开始处理图片，数量:', amazonData.imageUrls.length)

          // 限制图片数量
          const imagesToProcess = amazonData.imageUrls.slice(0, maxImages)

          // 处理图片
          const processedImages = await dianxiaomiImageUploadService.processImageUrls(
            imagesToProcess,
            3 // 最大并发数
          )

          if (processedImages.length > 0) {
            amazonData.imageUrls = processedImages
            amazonData.mainImageUrl = processedImages[0]
            console.info('[AmazonDataAssemblyService] 图片处理完成，成功处理:', processedImages.length, '张')
          } else {
            console.warn('[AmazonDataAssemblyService] 图片处理失败，使用原始图片URL')
          }
        } catch (imageError) {
          console.warn('[AmazonDataAssemblyService] 图片处理失败，使用原始图片URL:', imageError)
        }
      }

      console.info('[AmazonDataAssemblyService] Amazon数据组装完成:', amazonData)

      return {
        success: true,
        data: amazonData
      }

    } catch (error) {
      console.error('[AmazonDataAssemblyService] 组装Amazon数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '组装数据失败'
      }
    }
  }

  /**
   * 组装Amazon数据（新格式）
   */
  async assembleAmazonDataNewFormat(html: string): Promise<{ success: boolean; data?: DianxiaomiProductData; error?: string }> {
    try {
      console.info('[AmazonDataAssemblyService] 开始组装Amazon数据（新格式）')

      // 1. 提取Amazon数据
      const amazonResult = await this.extractBasicAmazonData(html)
      if (!amazonResult.success || !amazonResult.data) {
        return {
          success: false,
          error: amazonResult.error || '提取Amazon数据失败'
        }
      }

      const amazonData = amazonResult.data

      // 2. 转换为店小秘格式
      const dianxiaomiData: DianxiaomiProductData = {
        title: amazonData.title || '',
        description: this.formatDescription(amazonData),
        price: amazonData.price || 0,
        images: amazonData.imageUrls || [],
        category: amazonData.categoryPath || '',
        brand: amazonData.brand || '',
        specifications: amazonData.specifications || {},
        sourceUrl: amazonData.sourceUrl,
        
        // 额外字段
        asin: amazonData.asin,
        rating: amazonData.rating,
        reviewCount: amazonData.reviewCount,
        stockStatus: amazonData.stockStatus,
        currency: amazonData.currency || 'USD'
      }

      console.info('[AmazonDataAssemblyService] 新格式数据组装完成:', dianxiaomiData)

      return {
        success: true,
        data: dianxiaomiData
      }

    } catch (error) {
      console.error('[AmazonDataAssemblyService] 组装新格式数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '组装数据失败'
      }
    }
  }

  /**
   * 提取基础Amazon数据
   * 使用统一的数据提取器
   */
  private async extractBasicAmazonData(html: string): Promise<{ success: boolean; data?: AmazonProductData; error?: string }> {
    try {
      // 使用统一的数据提取器
      const amazonData = AmazonDataExtractor.extractFullProductData(html)

      // 提取主图和图片列表（这部分仍需要特殊处理）
      const mainImageUrl = AmazonDataService.extractMainImageFromHtml(html)
      const imageUrlsJson = AmazonDataService.extractImageUrlsFromHtml(html)
      const imageUrls = JSON.parse(imageUrlsJson)

      // 更新图片信息
      amazonData.mainImageUrl = mainImageUrl || undefined
      amazonData.imageUrls = imageUrls

      return {
        success: true,
        data: amazonData
      }

    } catch (error) {
      console.error('[AmazonDataAssemblyService] 提取基础数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '提取数据失败'
      }
    }
  }

  // 注意：所有数据提取方法已移动到 AmazonDataExtractor 统一处理

  // 所有数据提取方法已移动到 AmazonDataExtractor 统一处理

  /**
   * 格式化描述
   */
  private formatDescription(amazonData: AmazonProductData): string {
    const parts: string[] = []

    if (amazonData.bulletPoints && amazonData.bulletPoints.length > 0) {
      parts.push('产品特点：')
      amazonData.bulletPoints.forEach((point, index) => {
        parts.push(`${index + 1}. ${point}`)
      })
    }

    if (amazonData.specifications && Object.keys(amazonData.specifications).length > 0) {
      parts.push('\n产品规格：')
      Object.entries(amazonData.specifications).forEach(([key, value]) => {
        parts.push(`${key}: ${value}`)
      })
    }

    return parts.join('\n')
  }
}

// 创建单例实例
export const amazonDataAssemblyService = new AmazonDataAssemblyService()
export default amazonDataAssemblyService
