<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  defineProps,
  defineEmits,
  onMounted,
  onUnmounted,
  nextTick,
} from "vue"
import type {
  ShopAccount,
  FreightTemplate,
  ProductCategory,
} from "../../../services/dxm/dianxiaomiDetectionService"
import CategoryCascader from "./CategoryCascader.vue"
import ProductAttributes from "./ProductAttributes.vue"
import { getSiteOptions, getSiteById } from "../../../config/temuSites"
import configStorageService from "../../../services/configStorageService"
import type { ProductAttribute } from "../../../services/configStorageService"
import productConfigService from "../../../services/productConfigService"
import { message } from "ant-design-vue"
import { dianxiaomiApiService } from "../../../services/dxm/apiService"
import { <PERSON>ie<PERSON>elper } from "../../../utils/cookieHelper"

// Props
const props = defineProps<{
  basicForm: {
    erpPlatform: string
    publishSite: string
    shopAccount: string
    publishStatus: string
    businessSite: string
    warehouse: string
    freightTemplate: string
    shippingTime: string
    venue: string
    productCategory: string
    productAttributes: any[]
    publishStatusOptions?: any[]
    shippingTimeOptions?: any[]
    venueOptions?: any[]
  }
  dianxiaomiLoginStatus: {
    isLoggedIn: boolean
    message: string
    loading: boolean
  }
  shopAccounts: ShopAccount[]
  warehouses: Record<string, Record<string, Record<string, string>>>
  freightTemplates: FreightTemplate[]
  productCategories: ProductCategory[]
  loadingStates: {
    shopAccounts: boolean
    warehouses: boolean
    freightTemplates: boolean
    productCategories: boolean
  }
  currentTemuShop: string
}>()

// Emits
const emit = defineEmits<{
  "update:basicForm": [value: typeof props.basicForm]
  "check-login": []
  "open-erp": []
  "load-shop-accounts": []
  "load-warehouses": []
  "load-freight-templates": []
  "load-product-categories": []
  "shop-account-change": [shopId: string]
  "category-change": [category: ProductCategory | null]
  "load-categories-by-parent": [parentId?: number]
  "save-settings": []
}>()

// 登录状态管理
const loginStatus = ref({
  isLoggedIn: false,
  message: '未检测',
  loading: false,
  userInfo: null,
  shopCount: 0
})
const tokenCheckLoading = ref(false)
const tokenAutoCheckInterval = ref<number | null>(null)

// 站点选项
const siteOptions = computed(() => getSiteOptions())

// 计算属性
const getWarehouseOptions = computed(() => {
  const options: Array<{
    value: string
    label: string
    shopId: string
    site: string
  }> = []

  Object.entries(props.warehouses).forEach(([shopId, shopWarehouses]) => {
    Object.entries(shopWarehouses).forEach(([site, siteWarehouses]) => {
      Object.entries(siteWarehouses).forEach(([warehouseId, warehouseName]) => {
        options.push({
          value: warehouseId,
          label: `${warehouseName} (店铺: ${shopId}, 站点: ${site})`,
          shopId,
          site,
        })
      })
    })
  })

  return options
})

// 检查授权是否即将过期（30天内）
const isExpiringSoon = (expireTime: string): boolean => {
  if (!expireTime) return false
  try {
    const expireDate = new Date(expireTime)
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 30 && diffDays > 0
  } catch {
    return false
  }
}

// 更新表单数据
const updateForm = (key: string, value: any) => {
  const newForm = { ...props.basicForm, [key]: value }
  emit("update:basicForm", newForm)
}

// 事件处理
const handleShopAccountChange = (shopId: string) => {
  updateForm("shopAccount", shopId)
  emit("shop-account-change", shopId)
}

// 级联选择器状态
const selectedCategoryPath = ref<number[]>([])
const selectedCategoryLabels = ref<string[]>([])

const handleCategoryChange = (category: ProductCategory | null) => {
  emit("category-change", category)
}

const handleLoadCategoriesByParent = async (
  parentId?: number,
): Promise<ProductCategory[]> => {
  emit("load-categories-by-parent", parentId)
  return []
}

// 级联选择器变化处理
interface CascaderOption {
  value: number
  label: string
  isLeaf?: boolean
  children?: CascaderOption[]
}

const handleCascaderChange = (
  value: number[],
  selectedOptions: CascaderOption[],
) => {
  console.info("[BasicSettings] 分类选择变化:", { value, selectedOptions })

  selectedCategoryPath.value = value
  selectedCategoryLabels.value = selectedOptions.map((option) => option.label)

  // 更新表单中的分类名称（使用最后一级的名称）
  const categoryName =
    selectedOptions.length > 0
      ? selectedOptions[selectedOptions.length - 1].label
      : ""

  console.info("[BasicSettings] 更新表单分类字段:", categoryName)
  updateForm("productCategory", categoryName)

  // 强制触发表单验证
  if (categoryName) {
    // 确保表单字段被正确设置
    console.info("[BasicSettings] 分类选择完成，表单字段已更新")
  }

  // 如果选择了完整的分类路径，显示商品配置按钮
  if (value.length > 0 && selectedOptions.length > 0) {
    const lastCategory = selectedOptions[selectedOptions.length - 1]
    if (lastCategory.isLeaf) {
      // 选择了叶子节点，可以进行商品配置
      console.info("[BasicSettings] 选择了完整分类路径，可以进行商品配置:", {
        categoryId: lastCategory.value,
        categoryName: lastCategory.label,
        fullPath: selectedCategoryLabels.value.join(" / "),
      })
    }
  }

  // 创建一个模拟的 ProductCategory 对象用于向上传递
  if (selectedOptions.length > 0) {
    const lastOption = selectedOptions[selectedOptions.length - 1]
    const mockCategory: ProductCategory = {
      id: lastOption.value,
      catId: lastOption.value,
      catName: lastOption.label,
      parentCatId:
        selectedOptions.length > 1
          ? selectedOptions[selectedOptions.length - 2].value
          : 0,
      catLevel: selectedOptions.length,
      isLeaf: lastOption.isLeaf || false,
      catType: 0,
      isHidden: false,
      hiddenType: 0,
      classType: -1,
      enabledModelType: -1,
      enabledModel: false,
      needGuideFile: false,
      hasUpdate: 1,
      deleted: 0,
      createTime: Date.now(),
      updateTime: Date.now(),
      siteId: null,
      nodePathId: null,
      nodePath: null,
      classId: null,
      parentClassId: null,
      relatedClassIds: null,
    }
    emit("category-change", mockCategory)
  } else {
    emit("category-change", null)
  }
}

const handleSave = () => {
  // 保存基础设置时，同时保存当前的登录状态到跨页面缓存
  if (loginStatus.value.isLoggedIn) {
    saveTokenToCache(loginStatus.value)
    console.info("[BasicSettings] 保存基础设置时，登录状态已保存到跨页面缓存")
    message.success("基础设置和登录状态已保存")
  }
  emit("save-settings")
}

// 智能推断登录状态（当其他API成功时）
const handleAPISuccess = (apiName: string) => {
  console.info(`[BasicSettings] 检测到${apiName}API成功，推断登录状态`)

  // 如果当前登录状态无效，但有API成功，说明实际是登录的
  if (!loginStatus.value.isLoggedIn) {
    loginStatus.value = {
      isLoggedIn: true,
      message: `基于${apiName}成功推断店小秘已登录`,
      loading: false,
      userInfo: null,
      shopCount: 0
    }
    console.info("[BasicSettings] 推断的登录状态已更新")
    message.success(`基于${apiName}成功推断店小秘已登录`)
  }
}

// 监听props变化，检测API成功情况
watch(
  () => props.dianxiaomiLoginStatus,
  (newStatus, oldStatus) => {
    if (newStatus.isLoggedIn && !oldStatus?.isLoggedIn) {
      console.info("[BasicSettings] 检测到登录状态变化，推断token有效")
      handleAPISuccess("登录检测")
    }
  },
  { deep: true },
)

// 监听运费模板变化，如果有数据说明API调用成功
watch(
  () => props.freightTemplates,
  (newTemplates, oldTemplates) => {
    if (
      newTemplates &&
      newTemplates.length > 0 &&
      (!oldTemplates || oldTemplates.length === 0)
    ) {
      console.info("[BasicSettings] 检测到运费模板数据，推断token有效")
      handleAPISuccess("运费模板")
    }
  },
  { deep: true },
)

// 监听店铺账号变化，如果有数据说明API调用成功
watch(
  () => props.shopAccounts,
  (newAccounts, oldAccounts) => {
    if (
      newAccounts &&
      newAccounts.length > 0 &&
      (!oldAccounts || oldAccounts.length === 0)
    ) {
      console.info("[BasicSettings] 检测到店铺账号数据，推断token有效")
      handleAPISuccess("店铺账号")
    }
  },
  { deep: true },
)

// 生命周期钩子
onMounted(async () => {
  console.info("[BasicSettings] 组件已挂载，开始初始化token检测")

  // 如果已经有数据，说明之前的API调用成功，推断登录状态
  if (props.freightTemplates && props.freightTemplates.length > 0) {
    console.info("[BasicSettings] 发现现有运费模板数据，推断token有效")
    handleAPISuccess("运费模板（初始化）")
  } else if (props.shopAccounts && props.shopAccounts.length > 0) {
    console.info("[BasicSettings] 发现现有店铺账号数据，推断token有效")
    handleAPISuccess("店铺账号（初始化）")
  } else {
    // 没有现有数据，进行正常检测
    await checkLoginStatus(false)
  }

  // 启动自动检测
  startAutoCheck()

  // 确保当前有效的登录状态被保存到跨页面缓存
  if (loginStatus.value.isLoggedIn) {
    saveTokenToCache(loginStatus.value)
    console.info("[BasicSettings] 初始化时，有效的登录状态已保存到跨页面缓存")
  }

  // 获取店小秘配置状态
  await getDxmConfigStatus()

  // 使用 nextTick 确保组件完全渲染后再加载配置数据
  await nextTick()
  console.info("[BasicSettings] 组件渲染完成，开始加载配置数据...")
  await loadSavedCategoryAndAttributes()
})

onUnmounted(() => {
  console.info("[BasicSettings] 组件即将卸载，清理资源")
  // 停止自动检测
  stopAutoCheck()
})

// 测试配置
const testConfiguration = async () => {
  try {
    message.info("开始测试配置...")

    // 检查配置完整性
    const configCheck = await configStorageService.isConfigComplete()

    if (configCheck.isComplete) {
      // 获取完整配置
      const fullConfig = await configStorageService.getFullConfig()

      console.info("[BasicSettings] 配置测试结果:", {
        isComplete: true,
        basicConfig: fullConfig.basic,
        productConfig: fullConfig.product,
      })

      message.success("配置测试通过！所有必填项都已配置完成。")
    } else {
      console.warn("[BasicSettings] 配置不完整:", configCheck.missingFields)
      message.warning(
        `配置不完整，缺少以下项目：${configCheck.missingFields.join(", ")}`,
      )
    }
  } catch (error) {
    console.error("[BasicSettings] 配置测试失败:", error)
    message.error(
      "配置测试失败: " + (error instanceof Error ? error.message : "未知错误"),
    )
  }
}

// 跳转到店小秘商品配置页面
const openProductConfig = () => {
  if (!props.basicForm.shopAccount) {
    message.error("请先选择店铺账号")
    return
  }

  if (selectedCategoryPath.value.length === 0) {
    message.error("请先选择商品分类")
    return
  }

  const categoryId =
    selectedCategoryPath.value[selectedCategoryPath.value.length - 1]
  const shopId = props.basicForm.shopAccount

  // 构建店小秘商品配置页面URL
  const configUrl = `https://www.dianxiaomi.com/userTemplate/popTemuAdd.htm?shopId=${shopId}&from=dmo&pushtype=popTemuProduct&categoryId=${categoryId}`

  console.info("[BasicSettings] 打开商品配置页面:", {
    url: configUrl,
    shopId,
    categoryId,
    categoryPath: selectedCategoryLabels.value.join(" / "),
  })

  // 打开新标签页
  window.open(configUrl, "_blank")

  message.info("已打开商品配置页面，请在新页面中完成配置并保存")
}

// 计算属性：是否可以进行商品配置
const canConfigProduct = computed(() => {
  return (
    props.basicForm.shopAccount &&
    selectedCategoryPath.value.length > 0 &&
    (loginStatus.value.isLoggedIn || props.dianxiaomiLoginStatus.isLoggedIn)
  )
})

// 商品配置状态
const productConfigStatus = ref<{
  hasConfig: boolean
  config?: any
  loading: boolean
}>({
  hasConfig: false,
  config: null,
  loading: false,
})

// 检查商品配置状态
const checkProductConfigStatus = async () => {
  if (!props.basicForm.shopAccount || selectedCategoryPath.value.length === 0) {
    productConfigStatus.value = {
      hasConfig: false,
      config: null,
      loading: false,
    }
    return
  }

  productConfigStatus.value.loading = true
  try {
    const categoryId =
      selectedCategoryPath.value[selectedCategoryPath.value.length - 1]
    const config = await productConfigService.getProductConfig(
      props.basicForm.shopAccount,
      categoryId,
    )

    productConfigStatus.value = {
      hasConfig: config !== null,
      config: config,
      loading: false,
    }

    console.info(
      "[BasicSettings] 商品配置状态检查完成:",
      productConfigStatus.value,
    )
  } catch (error) {
    console.error("[BasicSettings] 检查商品配置状态失败:", error)
    productConfigStatus.value = {
      hasConfig: false,
      config: null,
      loading: false,
    }
  }
}

// 监听分类选择变化，检查配置状态
watch(
  [() => props.basicForm.shopAccount, selectedCategoryPath],
  () => {
    checkProductConfigStatus()
  },
  { immediate: true },
)

// 监听 basicForm 的变化，如果是空的则尝试重新加载配置
watch(
  () => props.basicForm,
  (newForm) => {
    // 如果表单数据为空且组件已挂载，尝试重新加载配置
    if (newForm && !newForm.productCategory && selectedCategoryPath.value.length === 0) {
      console.info('[BasicSettings] 检测到表单数据为空，尝试重新加载配置...')
      nextTick(() => {
        loadSavedCategoryAndAttributes()
      })
    }
  },
  { deep: true, immediate: true }
)

// 测试分类API - 直接调用
const testCategoryAPI = async () => {
  try {
    if (!props.basicForm.shopAccount) {
      message.error("请先选择店铺账号")
      return
    }

    message.info("开始测试分类API...")
    console.info(
      "[BasicSettings] 开始直接测试分类API，店铺ID:",
      props.basicForm.shopAccount,
    )

    // 构建POST请求参数
    const formData = new URLSearchParams()
    formData.append("shopId", props.basicForm.shopAccount)
    formData.append("categoryParentId", "0")

    console.info("[BasicSettings] 请求参数:", {
      shopId: props.basicForm.shopAccount,
      categoryParentId: 0,
    })

    // 直接调用店小秘API
    const response = await fetch(
      "https://www.dianxiaomi.com/api/popTemuCategory/list.json",
      {
        method: "POST",
        credentials: "include",
        headers: {
          Accept: "*/*",
          "Content-Type": "application/x-www-form-urlencoded",
          "X-Requested-With": "XMLHttpRequest",
        },
        body: formData.toString(),
      },
    )

    console.info("[BasicSettings] API响应状态:", response.status)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.info("[BasicSettings] API响应数据:", data)

    if (data.code === 0) {
      message.success(
        `分类API测试成功！获取到 ${data.data?.length || 0} 个根分类`,
      )
      console.info("[BasicSettings] 根分类数据:", data.data)
    } else {
      message.error(`分类API测试失败: ${data.msg || "未知错误"}`)
    }
  } catch (error) {
    console.error("[BasicSettings] 分类API测试失败:", error)
    message.error(
      "分类API测试失败: " +
        (error instanceof Error ? error.message : "未知错误"),
    )
  }
}

// 登录状态检测相关方法
const checkLoginStatus = async (forceRefresh = false) => {
  try {
    tokenCheckLoading.value = true
    console.info("[BasicSettings] 开始检查店小秘登录状态...")

    if (forceRefresh) {
      message.info("正在刷新登录状态...")
    } else {
      message.info("正在检查登录状态...")
    }

    const result = await dianxiaomiApiService.checkLoginStatus()
    console.info("[BasicSettings] 登录状态检查结果:", result)

    loginStatus.value = {
      isLoggedIn: result.isLoggedIn,
      message: result.message,
      loading: false,
      userInfo: result.userInfo,
      shopCount: result.shopCount || 0
    }

    if (result.isLoggedIn) {
      message.success(result.message)
      // 触发登录状态更新事件
      emit("check-login")
    } else {
      message.error(result.message)
    }

    return result
  } catch (error) {
    console.error("[BasicSettings] 登录状态检查失败:", error)
    message.error(
      "登录状态检查失败: " +
        (error instanceof Error ? error.message : "未知错误"),
    )
    return null
  } finally {
    tokenCheckLoading.value = false
  }
}

// 显示Cookie详情
const showCookieDetails = () => {
  try {
    console.info("[BasicSettings] 开始获取Cookie详情...")

    // 使用新的Cookie辅助工具
    const cookieInfo = CookieHelper.getDetailedCookieInfo()
    const validation = CookieHelper.validateDianxiaomiCookies()

    console.info("[BasicSettings] Cookie详情:", cookieInfo)
    console.info("[BasicSettings] Cookie验证:", validation)

    if (cookieInfo.cookieStats.hasValidCookies) {
      const cookieList = Object.entries(cookieInfo.dxmCookies)
        .map(
          ([key, value]) =>
            `${key}: ${value.substring(0, 20)}${value.length > 20 ? "..." : ""}`,
        )
        .join("\n")

      message.info(
        `Cookie检测结果:\n域名: ${cookieInfo.domain}\n总Cookie数: ${cookieInfo.cookieStats.totalCount}\n店小秘Cookie数: ${cookieInfo.cookieStats.dxmCount}\n验证分数: ${validation.score}%\n\n店小秘Cookie:\n${cookieList}`,
      )
    } else {
      message.warning(
        `Cookie检测结果:\n域名: ${cookieInfo.domain}\n总Cookie数: ${cookieInfo.cookieStats.totalCount}\n店小秘Cookie数: 0\n\n未发现店小秘相关Cookie，请确保已在店小秘网站登录`,
      )
    }
  } catch (error) {
    console.error("[BasicSettings] 获取Cookie详情失败:", error)
    message.error(
      "获取Cookie详情失败: " +
        (error instanceof Error ? error.message : "未知错误"),
    )
  }
}

// 清除登录状态缓存
const clearLoginCache = () => {
  try {
    // 清除本地登录状态
    loginStatus.value = {
      isLoggedIn: false,
      message: '已清除缓存',
      loading: false,
      userInfo: null,
      shopCount: 0
    }
    message.success("登录状态缓存已清除")
    console.info("[BasicSettings] 登录状态缓存已清除")
  } catch (error) {
    console.error("[BasicSettings] 清除登录状态缓存失败:", error)
    message.error("清除登录状态缓存失败")
  }
}

// 手动保存登录状态到本地缓存
const saveLoginStatusToCache = () => {
  try {
    if (loginStatus.value.isLoggedIn) {
      // 保存到localStorage
      localStorage.setItem('dianxiaomi_login_status', JSON.stringify(loginStatus.value))
      message.success("登录状态已保存到本地缓存")
      console.info(
        "[BasicSettings] 手动保存登录状态到本地缓存:",
        loginStatus.value,
      )

      // 验证保存结果
      setTimeout(() => {
        const saved = localStorage.getItem("dianxiaomi_login_status")
        if (saved) {
          const data = JSON.parse(saved)
          console.info("[BasicSettings] 保存验证 - 登录状态数据:", data)
        }
      }, 100)
    } else {
      message.warning("当前登录状态无效，请先检测登录状态")
    }
  } catch (error) {
    console.error("[BasicSettings] 手动保存登录状态失败:", error)
    message.error("保存登录状态失败")
  }
}

// 查看本地缓存详情
const showLocalCacheDetails = () => {
  try {
    const cached = localStorage.getItem("dianxiaomi_login_status")
    if (cached) {
      const data = JSON.parse(cached)

      message.info(
        `本地缓存详情:\n登录状态: ${data.isLoggedIn ? '已登录' : '未登录'}\n消息: ${data.message}\n店铺数量: ${data.shopCount || 0}\n用户信息: ${data.userInfo ? '有' : '无'}`,
      )
      console.info("[BasicSettings] 本地缓存详情:", data)
    } else {
      message.warning("未找到跨页面缓存")
    }
  } catch (error) {
    console.error("[BasicSettings] 查看缓存详情失败:", error)
    message.error("查看缓存详情失败")
  }
}

// 启动自动检测
const startAutoCheck = () => {
  if (tokenAutoCheckInterval.value) {
    clearInterval(tokenAutoCheckInterval.value)
  }

  // 每30秒自动检查一次登录状态
  tokenAutoCheckInterval.value = window.setInterval(() => {
    checkLoginStatus(false)
  }, 30000)

  console.info("[BasicSettings] 已启动token自动检测")
}

// 停止自动检测
const stopAutoCheck = () => {
  if (tokenAutoCheckInterval.value) {
    clearInterval(tokenAutoCheckInterval.value)
    tokenAutoCheckInterval.value = null
    console.info("[BasicSettings] 已停止token自动检测")
  }
}



// 店小秘配置状态
const dxmConfigStatus = ref({
  hasConfig: false,
  lastUpdated: '',
  categoryName: '',
  shopId: '',
  attributesCount: 0
})

// 获取店小秘配置状态
const getDxmConfigStatus = async () => {
  try {
    const status = await configStorageService.getDxmConfigStatus()
    dxmConfigStatus.value = {
      hasConfig: status.hasConfig,
      lastUpdated: status.lastUpdated || '',
      categoryName: status.categoryName || '',
      shopId: status.shopId || '',
      attributesCount: status.attributesCount || 0
    }
    console.info('[BasicSettings] 店小秘配置状态:', status)
  } catch (error) {
    console.error('[BasicSettings] 获取店小秘配置状态失败:', error)
  }
}

// 同步店小秘配置
const syncDxmConfig = async () => {
  try {
    message.loading('正在同步店小秘配置...', 0)

    await configStorageService.syncDxmConfigToBasic()

    // 触发保存事件，让父组件重新加载配置
    emit('save-settings')
    await getDxmConfigStatus()

    message.destroy()
    message.success('店小秘配置同步成功')
  } catch (error) {
    message.destroy()
    console.error('[BasicSettings] 同步店小秘配置失败:', error)
    message.error('同步店小秘配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 清除店小秘配置
const clearDxmConfig = async () => {
  try {
    await chrome.storage.local.remove('dxm-config-data')
    await getDxmConfigStatus()
    message.success('店小秘配置已清除')
  } catch (error) {
    console.error('[BasicSettings] 清除店小秘配置失败:', error)
    message.error('清除店小秘配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 产品属性处理方法
const handleAddProductAttribute = async (attribute: Omit<ProductAttribute, 'id'>) => {
  try {
    await configStorageService.addProductAttribute(attribute)
    // 重新加载配置以更新界面
    const config = await configStorageService.getBasicConfig()
    updateForm('productAttributes', config.productAttributes)
    message.success(`产品属性 "${attribute.name}" 添加成功`)
  } catch (error) {
    console.error('[BasicSettings] 添加产品属性失败:', error)
    message.error('添加产品属性失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

const handleRemoveProductAttribute = async (attributeId: string) => {
  try {
    await configStorageService.removeProductAttribute(attributeId)
    // 重新加载配置以更新界面
    const config = await configStorageService.getBasicConfig()
    updateForm('productAttributes', config.productAttributes)
    message.success('产品属性删除成功')
  } catch (error) {
    console.error('[BasicSettings] 删除产品属性失败:', error)
    message.error('删除产品属性失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

const handleUpdateProductAttribute = async (attributeId: string, updates: Partial<ProductAttribute>) => {
  try {
    await configStorageService.updateProductAttribute(attributeId, updates)
    // 重新加载配置以更新界面
    const config = await configStorageService.getBasicConfig()
    updateForm('productAttributes', config.productAttributes)
    console.info('[BasicSettings] 产品属性更新成功:', { attributeId, updates })
  } catch (error) {
    console.error('[BasicSettings] 更新产品属性失败:', error)
    message.error('更新产品属性失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 加载保存的分类和属性信息
const loadSavedCategoryAndAttributes = async () => {
  try {
    console.info('[BasicSettings] 开始加载保存的分类和属性信息...')

    // 从 temu-extension-basic-config 中读取数据
    const result = await chrome.storage.local.get(['temu-extension-basic-config'])
    const basicConfig = result['temu-extension-basic-config']

    console.info('[BasicSettings] Chrome storage 查询结果:', result)
    console.info('[BasicSettings] 基础配置数据:', basicConfig)

    if (basicConfig) {
      console.info('[BasicSettings] 找到保存的配置，开始处理...')

      // 加载分类信息
      if (basicConfig.categoryInfo) {
        const categoryInfo = basicConfig.categoryInfo
        console.info('[BasicSettings] 处理分类信息:', categoryInfo)

        // 设置分类路径
        if (categoryInfo.categoryPath && Array.isArray(categoryInfo.categoryPath)) {
          const categoryIds = categoryInfo.categoryPath.map((item: any) => {
            const id = parseInt(item.id)
            console.info('[BasicSettings] 转换分类ID:', item.id, '->', id)
            return id
          })
          const categoryLabels = categoryInfo.categoryPath.map((item: any) => item.name)

          console.info('[BasicSettings] 设置分类数据:', {
            原始路径: categoryInfo.categoryPath,
            转换后ID: categoryIds,
            标签: categoryLabels,
            分类名称: categoryInfo.categoryName
          })

          selectedCategoryPath.value = categoryIds
          selectedCategoryLabels.value = categoryLabels

          // 更新表单中的分类名称
          updateForm('productCategory', categoryInfo.categoryName)

          console.info('[BasicSettings] 分类路径设置完成:', {
            selectedCategoryPath: selectedCategoryPath.value,
            selectedCategoryLabels: selectedCategoryLabels.value,
            表单分类字段: props.basicForm.productCategory
          })
        } else {
          console.warn('[BasicSettings] categoryPath 不存在或不是数组:', categoryInfo.categoryPath)
        }
      } else {
        console.warn('[BasicSettings] categoryInfo 不存在')
      }

      // 加载产品属性
      if (basicConfig.productAttributes) {
        const attributes = basicConfig.productAttributes
        console.info('[BasicSettings] 处理产品属性:', attributes)
        console.info('[BasicSettings] 属性数据类型:', typeof attributes)
        console.info('[BasicSettings] 属性键列表:', Object.keys(attributes))

        // 转换为组件需要的格式
        const convertedAttributes = Object.keys(attributes).map(key => {
          const attr = attributes[key]
          console.info(`[BasicSettings] 处理属性 ${key}:`, attr)

          const converted = {
            id: attr.id || key,
            name: attr.name || attr.propName,
            value: attr.value || attr.propValue || '',
            type: 'select' as const,
            options: attr.options ? Object.values(attr.options) : undefined,
            required: attr.required || false,
            // 保留原始数据用于后续处理
            originalData: attr
          }

          console.info(`[BasicSettings] 属性 ${key} 转换结果:`, converted)
          return converted
        })

        console.info('[BasicSettings] 所有属性转换完成:', convertedAttributes)

        // 更新表单中的产品属性
        updateForm('productAttributes', convertedAttributes)

        console.info('[BasicSettings] 产品属性设置完成:', {
          转换后属性: convertedAttributes,
          表单属性字段: props.basicForm.productAttributes
        })
      } else {
        console.warn('[BasicSettings] productAttributes 不存在')
      }

      console.info('[BasicSettings] 配置加载完成，当前状态:', {
        selectedCategoryPath: selectedCategoryPath.value,
        selectedCategoryLabels: selectedCategoryLabels.value,
        productAttributes: props.basicForm.productAttributes
      })

      message.success('已加载保存的分类和属性配置')
    } else {
      console.info('[BasicSettings] 未找到保存的配置')
      message.info('未找到保存的配置数据')
    }
  } catch (error) {
    console.error('[BasicSettings] 加载保存的分类和属性信息失败:', error)
    message.error('加载配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 重新加载配置
const reloadConfiguration = async () => {
  try {
    message.loading('正在重新加载配置...', 0)

    // 重新加载分类和属性信息
    await loadSavedCategoryAndAttributes()

    // 重新获取店小秘配置状态
    await getDxmConfigStatus()

    // 触发父组件重新加载数据
    emit('load-shop-accounts')
    emit('load-warehouses')
    emit('load-freight-templates')
    emit('load-product-categories')

    message.destroy()
    message.success('配置重新加载完成')
  } catch (error) {
    message.destroy()
    console.error('[BasicSettings] 重新加载配置失败:', error)
    message.error('重新加载配置失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}
</script>

<template>
  <div class="basic-settings-container">
    <a-form
      :model="basicForm"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      class="space-y-4"
      @finish="handleSave"
    >
      <!-- ERP平台 -->
      <a-form-item
        label="ERP平台"
        name="erpPlatform"
        :rules="[{ required: true, message: '请选择ERP平台' }]"
      >
        <a-select
          :value="basicForm.erpPlatform"
          placeholder="请选择ERP平台"
          @change="(value: string) => updateForm('erpPlatform', value)"
        >
          <a-select-option value="店小秘">店小秘</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 发布站点 -->
      <a-form-item label="发布站点">
        <a-input
          :value="currentTemuShop"
          readonly
          class="bg-gray-50"
        >
          <template #addonBefore>
            <a-tag
              :color="currentTemuShop.includes('半托') ? 'blue' : 'green'"
              size="small"
            >
              {{ currentTemuShop.includes("半托") ? "半托" : "全托" }}
            </a-tag>
          </template>
        </a-input>
      </a-form-item>

      <!-- 店小秘Token状态检测 - 增强版 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <a-card
          size="small"
          class="mb-4"
        >
          <template #title>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium">🔐 店小秘登录状态</span>
              <a-tag
                :color="loginStatus.isLoggedIn ? 'success' : 'error'"
                size="small"
              >
                {{ loginStatus.isLoggedIn ? "已登录" : "未登录" }}
              </a-tag>
            </div>
          </template>

          <template #extra>
            <a-space size="small">
              <a-button
                size="small"
                :loading="tokenCheckLoading"
                @click="checkLoginStatus(true)"
              >
                {{ tokenCheckLoading ? "检测中..." : "🔄 刷新" }}
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      key="cookie"
                      @click="showCookieDetails"
                    >
                      🍪 查看Cookie详情
                    </a-menu-item>
                    <a-menu-item
                      key="save-token"
                      @click="saveLoginStatusToCache"
                    >
                      💾 保存Token到缓存
                    </a-menu-item>
                    <a-menu-item
                      key="view-cache"
                      @click="showLocalCacheDetails"
                    >
                      📋 查看缓存详情
                    </a-menu-item>
                    <a-menu-item
                      key="clear"
                      @click="clearLoginCache"
                    >
                      🗑️ 清除缓存
                    </a-menu-item>
                    <a-menu-item
                      key="open-erp"
                      @click="emit('open-erp')"
                    >
                      🌐 打开店小秘ERP
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">⚙️ 更多</a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-card>
      </a-form-item>

      <!-- 兼容原有的登录状态检测（当未登录时显示） -->
      <a-form-item
        v-if="!loginStatus.isLoggedIn && !dianxiaomiLoginStatus.isLoggedIn"
        :wrapper-col="{ span: 24, offset: 0 }"
      >
        <a-alert
          type="warning"
          show-icon
          class="mb-4"
        >
          <template #message>
            <div class="flex items-center justify-between">
              <span>{{ dianxiaomiLoginStatus.message }}</span>
              <a-space>
                <a-button
                  type="primary"
                  size="small"
                  @click="emit('open-erp')"
                >
                  打开店小秘ERP
                </a-button>
                <a-button
                  size="small"
                  :loading="dianxiaomiLoginStatus.loading"
                  @click="emit('check-login')"
                >
                  {{ dianxiaomiLoginStatus.loading ? "检测中..." : "再次重试" }}
                </a-button>
              </a-space>
            </div>
          </template>
        </a-alert>
      </a-form-item>

      <!-- 店铺账号 -->
      <a-form-item
        v-if="loginStatus.isLoggedIn || dianxiaomiLoginStatus.isLoggedIn"
        name="shopAccount"
        :rules="[{ required: true, message: '请选择店铺账号' }]"
      >
        <template #label>
          <div class="flex items-center space-x-2">
            <span>店铺账号</span>
            <a-tag
              v-if="shopAccounts.length > 0"
              color="blue"
              size="small"
            >
              共 {{ shopAccounts.length }} 个
            </a-tag>
          </div>
        </template>

        <a-input-group compact>
          <a-select
            :value="basicForm.shopAccount"
            placeholder="请选择店铺账号"
            :loading="loadingStates.shopAccounts"
            style="width: calc(100% - 80px)"
            @change="handleShopAccountChange"
          >
            <a-select-option
              v-for="shop in shopAccounts"
              :key="shop.shopId"
              :value="shop.shopId"
            >
              {{ shop.shopName }} (ID: {{ shop.shopId }}) - {{ shop.currency }}
            </a-select-option>
          </a-select>
          <a-button
            :loading="loadingStates.shopAccounts"
            style="width: 80px"
            @click="emit('load-shop-accounts')"
          >
            {{ loadingStates.shopAccounts ? "同步中" : "同步" }}
          </a-button>
        </a-input-group>

        <!-- 显示选中店铺的详细信息 -->
        <div
          v-if="basicForm.shopAccount"
          class="mt-3"
        >
          <div
            v-for="shop in shopAccounts"
            :key="shop.shopId"
          >
            <a-card
              v-if="shop.shopId === basicForm.shopAccount"
              size="small"
              class="bg-blue-50 border-blue-200"
            >
              <template #title>
                <div class="flex items-center justify-between">
                  <span class="text-blue-900 text-sm">{{ shop.shopName }}</span>
                  <a-tag
                    color="success"
                    size="small"
                  >
                    已绑定
                  </a-tag>
                </div>
              </template>

              <a-descriptions
                :column="3"
                size="small"
              >
                <a-descriptions-item label="店铺ID">
                  <a-typography-text code>{{ shop.shopId }}</a-typography-text>
                </a-descriptions-item>
                <a-descriptions-item label="币种">
                  <a-tag color="blue">{{ shop.currency }}</a-tag>
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="shop.authTime"
                  label="授权时间"
                >
                  <span class="text-xs">{{ shop.authTime }}</span>
                </a-descriptions-item>
                <a-descriptions-item
                  v-if="shop.updateTime"
                  label="更新时间"
                  :span="3"
                >
                  <span class="text-xs">
                    {{ shop.updateTime }}
                  </span>
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </div>
        </div>
      </a-form-item>

      <!-- 发布状态 -->
      <a-form-item
        label="发布状态"
        name="publishStatus"
      >
        <a-radio-group
          :value="basicForm.publishStatus"
          @change="(e: any) => updateForm('publishStatus', e.target.value)"
        >
          <a-radio value="2">直接发布</a-radio>
          <a-radio value="3">移入DXM待发布</a-radio>
          <a-radio value="1">放置DXM草稿箱</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 经营站点 -->
      <a-form-item
        label="经营站点"
        name="businessSite"
        :rules="[{ required: true, message: '请选择经营站点' }]"
      >
        <a-select
          :value="basicForm.businessSite"
          placeholder="请选择经营站点"
          @change="(value: string) => updateForm('businessSite', value)"
        >
          <a-select-option
            v-for="site in siteOptions"
            :key="site.value"
            :value="site.value"
          >
            {{ site.label }} ({{ site.code }})
          </a-select-option>
        </a-select>

        <!-- 显示选中站点的详细信息 -->
        <div
          v-if="basicForm.businessSite"
          class="mt-2"
        >
          <div
            v-if="getSiteById(Number(basicForm.businessSite))"
            class="text-sm"
          >
            <a-space>
              <a-tag
                color="blue"
                size="small"
              >
                {{ getSiteById(Number(basicForm.businessSite))?.region }}
              </a-tag>
              <span class="text-gray-500 text-xs">
                货币：{{
                  getSiteById(Number(basicForm.businessSite))?.currency
                }}
              </span>
              <span class="text-gray-500 text-xs">
                语言：{{
                  getSiteById(Number(basicForm.businessSite))?.language
                }}
              </span>
            </a-space>
          </div>
        </div>
      </a-form-item>

      <!-- 发货仓库 -->
      <a-form-item
        v-if="loginStatus.isLoggedIn || dianxiaomiLoginStatus.isLoggedIn"
        label="发货仓库"
        name="warehouse"
        :rules="[{ required: true, message: '请选择发货仓库' }]"
      >
        <a-input-group compact>
          <a-select
            :value="basicForm.warehouse"
            placeholder="请选择发货仓库"
            :loading="loadingStates.warehouses"
            style="width: calc(100% - 80px)"
            @change="(value: string) => updateForm('warehouse', value)"
          >
            <a-select-option
              v-for="warehouse in getWarehouseOptions"
              :key="warehouse.value"
              :value="warehouse.value"
            >
              {{ warehouse.label }}
            </a-select-option>
          </a-select>
          <a-button
            :loading="loadingStates.warehouses"
            style="width: 80px"
            size="small"
            @click="emit('load-warehouses')"
          >
            {{ loadingStates.warehouses ? "同步中" : "同步" }}
          </a-button>
        </a-input-group>
      </a-form-item>

      <!-- 运费模板 -->
      <a-form-item
        v-if="loginStatus.isLoggedIn || dianxiaomiLoginStatus.isLoggedIn"
        label="运费模板"
        name="freightTemplate"
        :rules="[{ required: true, message: '请选择运费模板' }]"
      >
        <a-input-group compact>
          <a-select
            :value="basicForm.freightTemplate"
            placeholder="请选择运费模板"
            :loading="loadingStates.freightTemplates"
            style="width: calc(100% - 80px)"
            @change="(value: string) => updateForm('freightTemplate', value)"
          >
            <a-select-option
              v-for="template in freightTemplates"
              :key="template.id"
              :value="template.freightTemplateId"
            >
              {{ template.templateName }} (店铺: {{ template.shopId }}, 站点:
              {{ template.site }})
            </a-select-option>
          </a-select>
          <a-button
            :loading="loadingStates.freightTemplates"
            style="width: 80px"
            size="small"
            @click="emit('load-freight-templates')"
          >
            {{ loadingStates.freightTemplates ? "同步中" : "同步" }}
          </a-button>
        </a-input-group>
      </a-form-item>

      <!-- 发货时效 -->
      <a-form-item
        label="发货时效"
        name="shippingTime"
        :rules="[{ required: true, message: '请选择发货时效' }]"
      >
        <a-radio-group
          :value="basicForm.shippingTime"
          @change="(e: any) => updateForm('shippingTime', e.target.value)"
        >
          <a-radio value="86400">1个工作日内发货</a-radio>
          <a-radio value="172800">2个工作日内发货</a-radio>
          <a-radio value="777600">9个工作日内发货(Y2)</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 核销地区 -->
      <a-form-item
        label="核销地区"
        name="venue"
        :rules="[{ required: true, message: '请选择核销地区' }]"
      >
        <a-select
          :value="basicForm.venue"
          placeholder="请选择核销地区"
          @change="(value) => updateForm('venue', value)"
        >
          <a-select-option value="CN_GD">中国/广东省</a-select-option>
          <a-select-option value="CN_ZJ">中国/浙江省</a-select-option>
          <a-select-option value="CN_JS">中国/江苏省</a-select-option>
          <a-select-option value="CN_SH">中国/上海市</a-select-option>
          <a-select-option value="US">美国</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 商品分类 -->
      <a-form-item
        v-if="loginStatus.isLoggedIn || dianxiaomiLoginStatus.isLoggedIn"
        label="商品分类"
        name="productCategory"
        :rules="[{ required: true, message: '请选择商品分类' }]"
      >
        <div class="space-y-3">
          <!-- 显示已保存的分类信息 -->
          <div
            v-if="selectedCategoryPath.length > 0"
            class="bg-green-50 border border-green-200 rounded p-3 mb-3"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-green-700 font-medium text-sm">✅ 已选择分类</span>
              <a-tag
                color="green"
                size="small"
              >
                已配置
              </a-tag>
            </div>

            <div class="space-y-2">
              <div class="text-sm">
                <span class="text-gray-600">分类路径:</span>
                <div class="mt-1">
                  <a-breadcrumb separator=">">
                    <a-breadcrumb-item
                      v-for="(label, index) in selectedCategoryLabels"
                      :key="index"
                      class="text-blue-600"
                    >
                      {{ label }}
                    </a-breadcrumb-item>
                  </a-breadcrumb>
                </div>
              </div>

              <div class="text-sm">
                <span class="text-gray-600">分类ID:</span>
                <a-tag
                  color="blue"
                  size="small"
                  class="ml-1"
                >
                  {{ selectedCategoryPath[selectedCategoryPath.length - 1] }}
                </a-tag>
              </div>
            </div>
          </div>

          <CategoryCascader
            :value="selectedCategoryPath"
            :shop-id="basicForm.shopAccount"
            placeholder="请选择商品分类"
            @change="handleCascaderChange"
          />

          <!-- 显示选中的分类路径 -->
          <div v-if="selectedCategoryPath.length > 0">
            <a-tag
              color="blue"
              size="small"
            >
              {{ selectedCategoryLabels.join(" / ") }}
            </a-tag>
          </div>
        </div>
      </a-form-item>

      <!-- 商品配置 -->
      <a-form-item label="商品配置">
        <div class="space-y-3">
          <!-- 配置状态显示 -->
          <div
            v-if="productConfigStatus.loading"
            class="flex items-center space-x-2"
          >
            <a-spin size="small" />
            <span class="text-sm text-gray-500">检查配置状态中...</span>
          </div>

          <div
            v-else-if="productConfigStatus.hasConfig"
            class="bg-green-50 p-3 rounded border-l-4 border-green-400"
          >
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-green-800 mb-1">
                  ✅ 已有商品配置
                </div>
                <div class="text-xs text-green-600">
                  配置时间:
                  {{
                    productConfigStatus.config?.timestamp
                      ? new Date(
                        productConfigStatus.config.timestamp,
                      ).toLocaleString()
                      : "未知"
                  }}
                </div>
                <div class="text-xs text-green-600">
                  分类路径:
                  {{ productConfigStatus.config?.categoryPath || "未知" }}
                </div>
              </div>
              <a-button
                type="primary"
                size="small"
                :disabled="!canConfigProduct"
                @click="openProductConfig"
              >
                🔧 重新配置
              </a-button>
            </div>
          </div>
             <!-- 商品属性显示 -->
      <a-form-item
        v-if="basicForm.productAttributes && basicForm.productAttributes.length > 0"
        label="商品属性"
      >
        <div class="bg-blue-50 border border-blue-200 rounded p-3">
          <div class="flex items-center justify-between mb-2">
            <span class="text-blue-700 font-medium text-sm">✅ 已配置属性</span>
            <a-tag
              color="blue"
              size="small"
            >
              {{ basicForm.productAttributes.length }} 个属性
            </a-tag>
          </div>

          <div class="space-y-2">
            <div
              v-for="(attr, index) in basicForm.productAttributes"
              :key="index"
              class="flex items-center justify-between bg-white p-2 rounded border"
            >
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-800">
                  {{ attr.name || attr.propName }}
                </div>
                <div class="text-xs text-gray-600">
                  值: {{ attr.value || attr.propValue || '未设置' }}
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <a-tag
                  :color="attr.required ? 'red' : 'default'"
                  size="small"
                >
                  {{ attr.required ? '必填' : '可选' }}
                </a-tag>
                <a-tag
                  color="blue"
                  size="small"
                >
                  {{ attr.type || 'select' }}
                </a-tag>
              </div>
            </div>
          </div>
        </div>
      </a-form-item>

          <!-- 配置按钮 -->
          <div v-else>
            <a-button
              type="primary"
              :disabled="!canConfigProduct"
              size="default"
              class="mr-2"
              @click="openProductConfig"
            >
              🔧 配置商品属性
            </a-button>
            <a-tooltip
              v-if="!canConfigProduct"
              title="请先选择店铺账号和商品分类"
            >
              <a-button
                disabled
                size="small"
              >
                ❓ 需要完成前置配置
              </a-button>
            </a-tooltip>
          </div>

          <!-- 配置说明 -->
          <div
            class="text-sm text-gray-600 bg-blue-50 p-3 rounded border-l-4 border-blue-400"
          >
            <div class="font-medium text-blue-800 mb-1">📋 配置说明：</div>
            <ul class="list-disc list-inside space-y-1 text-xs">
              <li>选择商品分类后，点击"配置商品属性"按钮</li>
              <li>系统将打开店小秘商品配置页面</li>
              <li>在配置页面中设置商品属性、规格等信息</li>
              <li>点击"保存设置"后，配置将自动保存到插件本地缓存</li>
            </ul>
          </div>
        </div>
      </a-form-item>

   

      <!-- 保存按钮 -->
      <a-form-item :wrapper-col="{ span: 24, offset: 0 }">
        <div class="flex justify-center pt-4">
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            class="px-12 h-10"
          >
            💾 保存基础设置
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped>
/* ========================================
   基础设置组件样式 - 基于新设计系统
======================================== */

.basic-settings-container {
  width: 100%;
  min-height: auto; /* 自适应内容高度 */
  padding: 0; /* 父容器已设置padding */
  margin: 0 auto;
  background: transparent; /* 使用父容器背景 */
  border-radius: 0; /* 避免重复圆角 */
  overflow: visible; /* 让最外层容器处理滚动 */
}

/* 滚动条样式由父容器 step-panel 处理 */

.basic-settings-container :deep(.ant-form-item-label > label) {
  font-weight: 600;
  font-size: 14px;
}

.basic-settings-container :deep(.ant-form-item) {
  margin-bottom: var(--space-lg);
}

/* 表单布局优化 - 居中对齐 */
.basic-settings-container :deep(.ant-form) {
  max-width: 100%;
}

/* 卡片内容居中 */
.basic-settings-container :deep(.ant-card) {
  margin-bottom: var(--space-lg);
}

/* 表单项标签对齐 */
.basic-settings-container :deep(.ant-form-item-label) {
  text-align: left;
  padding-right: var(--space-md);
}

.basic-settings-container :deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #6b7280;
  font-size: 12px;
}

.basic-settings-container :deep(.ant-descriptions-item-content) {
  font-size: 12px;
}

.basic-settings-container :deep(.ant-select) {
  font-size: 14px;
}

.basic-settings-container :deep(.ant-input) {
  font-size: 14px;
}

.basic-settings-container :deep(.ant-radio-wrapper) {
  font-size: 14px;
}

/* 水平布局优化 */
.basic-settings-container :deep(.ant-form-horizontal .ant-form-item-label) {
  padding-right: 12px;
}

.basic-settings-container :deep(.ant-form-horizontal .ant-form-item-control) {
  flex: 1;
}

/* 保存按钮区域样式 */
.basic-settings-container :deep(.flex.justify-center) {
  margin-top: var(--space-xl);
  margin-bottom: var(--space-xl);
  padding: var(--space-lg) 0;
  border-top: 1px solid var(--border-color);
  background: var(--bg-container);
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 按钮样式优化 */
.basic-settings-container :deep(.ant-btn-primary) {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
  font-weight: var(--font-medium);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.basic-settings-container :deep(.ant-btn-primary:hover) {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.basic-settings-container :deep(.ant-btn-default) {
  border-color: var(--border-color);
  color: var(--text-secondary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.basic-settings-container :deep(.ant-btn-default:hover) {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 按钮组优化 */
.basic-settings-container :deep(.ant-input-group-addon) {
  padding: 0;
}
</style>
