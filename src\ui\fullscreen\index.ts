import { i18n } from "src/utils/i18n"
import { pinia } from "src/utils/pinia"
import { appRouter } from "src/utils/router"
import { createApp } from "vue"
import App from "./app.vue"

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import "./index.css"

// 添加全屏页面路由
appRouter.addRoute({
  path: "/",
  redirect: "/fullscreen/dashboard",
})

appRouter.addRoute({
  path: "/fullscreen",
  redirect: "/fullscreen/dashboard",
})

appRouter.addRoute({
  path: "/fullscreen/dashboard",
  component: () => import("./pages/dashboard.vue"),
})

appRouter.addRoute({
  path: "/fullscreen/product-center",
  component: () => import("./pages/product-center.vue"),
})

const app = createApp(App).use(i18n).use(Antd).use(pinia).use(appRouter)

app.mount("#app")

// 监听来自侧边栏的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'FULLSCREEN_NAVIGATE') {
    console.log('[Fullscreen] 收到导航请求:', message.route)
    appRouter.push(message.route).then(() => {
      console.log('[Fullscreen] 导航成功:', message.route)
      sendResponse({ success: true })
    }).catch(error => {
      console.error('[Fullscreen] 导航失败:', error)
      sendResponse({ success: false, error: error.message })
    })
    return true // 保持消息通道开放
  }
})
