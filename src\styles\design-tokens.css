/* 胡建大卖家扩展 - 现代化设计系统 */
/* 基于 Ant Design Vue 的高端商务设计规范 */

:root {
  /* === 品牌色彩系统 === */
  /* 主品牌色 - 科技蓝 */
  --brand-primary: #1677ff;
  --brand-primary-light: #4096ff;
  --brand-primary-dark: #0958d9;
  --brand-primary-extra-light: #e6f4ff;
  
  /* 辅助品牌色 - 商务金 */
  --brand-secondary: #fa8c16;
  --brand-secondary-light: #ffa940;
  --brand-secondary-dark: #d46b08;
  --brand-secondary-extra-light: #fff7e6;

  /* 功能色系 */
  --success-color: #52c41a;
  --success-light: #73d13d;
  --success-dark: #389e0d;
  --success-bg: #f6ffed;
  
  --warning-color: #faad14;
  --warning-light: #ffc53d;
  --warning-dark: #d48806;
  --warning-bg: #fffbe6;
  
  --error-color: #ff4d4f;
  --error-light: #ff7875;
  --error-dark: #cf1322;
  --error-bg: #fff2f0;
  
  --info-color: #1677ff;
  --info-light: #4096ff;
  --info-dark: #0958d9;
  --info-bg: #e6f4ff;

  /* === 中性色系统 === */
  /* 文本色彩层次 */
  --text-primary: #262626;        /* 主要文本 - 深灰 */
  --text-secondary: #595959;      /* 次要文本 - 中灰 */
  --text-tertiary: #8c8c8c;       /* 辅助文本 - 浅灰 */
  --text-quaternary: #bfbfbf;     /* 占位文本 - 极浅灰 */
  --text-disabled: #d9d9d9;       /* 禁用文本 */
  --text-inverse: #ffffff;        /* 反色文本 - 白色 */

  /* 背景色系统 */
  --bg-container: #ffffff;        /* 主容器背景 - 纯白 */
  --bg-layout: #f5f5f5;          /* 布局背景 - 浅灰 */
  --bg-elevated: #fafafa;        /* 卡片背景 - 微灰 */
  --bg-mask: rgba(0, 0, 0, 0.45); /* 遮罩背景 */
  --bg-spotlight: #f0f9ff;       /* 高亮背景 */

  /* 边框色系统 */
  --border-color: #d9d9d9;       /* 默认边框 */
  --border-color-split: #f0f0f0; /* 分割线 */
  --border-color-light: #e8e8e8; /* 浅边框 */
  --border-color-hover: #40a9ff;  /* 悬停边框 */

  /* === 阴影系统 === */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.06), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.12);
  
  /* 特殊阴影 */
  --shadow-brand: 0 4px 12px rgba(22, 119, 255, 0.15);
  --shadow-success: 0 4px 12px rgba(82, 196, 26, 0.15);
  --shadow-warning: 0 4px 12px rgba(250, 173, 20, 0.15);
  --shadow-error: 0 4px 12px rgba(255, 77, 79, 0.15);

  /* === 圆角系统 === */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-full: 9999px;

  /* === 间距系统 === */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  --space-3xl: 32px;
  --space-4xl: 40px;
  --space-5xl: 48px;

  /* === 字体系统 === */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  
  /* 字体大小 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  
  /* 字体粗细 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* === 过渡动画 === */
  --transition-fast: 0.15s ease-out;
  --transition-base: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
  --transition-all: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  /* === Z-index 层级 === */
  --z-dropdown: 1000;
  --z-sticky: 1010;
  --z-fixed: 1020;
  --z-modal: 1030;
  --z-popover: 1040;
  --z-tooltip: 1050;
  --z-toast: 1060;
}

/* === 现代化组件样式 === */

/* 卡片组件系列 */
.card-primary {
  background: var(--bg-container);
  border: 1px solid var(--border-color-split);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--space-xl);
  transition: var(--transition-all);
}

.card-primary:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-color-hover);
}

.card-elevated {
  background: var(--bg-container);
  border: none;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-2xl);
  transition: var(--transition-all);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.card-brand {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-light) 100%);
  border: none;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-brand);
  color: var(--text-inverse);
  padding: var(--space-2xl);
  transition: var(--transition-all);
}

.card-brand:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-1px);
}

/* 按钮增强样式 */
.btn-gradient-primary {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-light) 100%);
  border: none;
  box-shadow: var(--shadow-brand);
  transition: var(--transition-all);
}

.btn-gradient-primary:hover {
  background: linear-gradient(135deg, var(--brand-primary-light) 0%, var(--brand-primary) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-gradient-secondary {
  background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-secondary-light) 100%);
  border: none;
  box-shadow: var(--shadow-warning);
  color: var(--text-inverse);
  transition: var(--transition-all);
}

.btn-gradient-secondary:hover {
  background: linear-gradient(135deg, var(--brand-secondary-light) 0%, var(--brand-secondary) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* 文本工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }
.text-inverse { color: var(--text-inverse); }

.text-gradient {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-bold);
}

/* 状态颜色 */
.status-success { color: var(--success-color); }
.status-warning { color: var(--warning-color); }
.status-error { color: var(--error-color); }
.status-info { color: var(--info-color); }

.bg-success { background-color: var(--success-bg); }
.bg-warning { background-color: var(--warning-bg); }
.bg-error { background-color: var(--error-bg); }
.bg-info { background-color: var(--info-bg); }

/* 交互效果 */
.hover-lift {
  transition: var(--transition-all);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: var(--transition-all);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: var(--transition-all);
}

.hover-glow:hover {
  box-shadow: var(--shadow-brand);
}

/* 布局工具类 */
.glass-morphism {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
}

.gradient-border {
  position: relative;
  background: var(--bg-container);
  border-radius: var(--radius-lg);
}

.gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn var(--transition-base) ease-out;
}

.animate-slide-up {
  animation: slideUp var(--transition-base) ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 响应式工具类 */
.container-responsive {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

@media (max-width: 768px) {
  .container-responsive {
    padding: 0 var(--space-md);
  }
}

/* 特殊效果 */
.neon-glow {
  text-shadow: 0 0 5px var(--brand-primary), 0 0 10px var(--brand-primary), 0 0 15px var(--brand-primary);
}

.brand-shadow {
  box-shadow: var(--shadow-brand);
}

.success-shadow {
  box-shadow: var(--shadow-success);
}

.warning-shadow {
  box-shadow: var(--shadow-warning);
}

.error-shadow {
  box-shadow: var(--shadow-error);
}
