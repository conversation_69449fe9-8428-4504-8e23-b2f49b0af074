<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useShopBinding } from '../../../composables/useShopBinding'
import { useNotification } from '../../../composables/useNotification'
import dianxiaomiDetectionService from '../../../services/dxm/dianxiaomiDetectionService'
import configStorageService from '../../../services/configStorageService'
import type { ShopAccount, FreightTemplate, ProductCategory } from '../../../services/dxm/dianxiaomiDetectionService'
import type { BasicConfig, ProductConfig as ProductConfigType } from '../../../services/configStorageService'
import { getSiteOptions } from '../../../config/temuSites'

// 使用组合式函数
const { shopBinding } = useShopBinding()
const { success, error } = useNotification()

// 当前步骤
const activeStep = ref(0)

// 步骤定义
const steps = [
  {
    title: '基础设置',
    description: '配置ERP平台、店铺信息等基础设置',
    icon: '⚙️'
  },
  {
    title: '上品配置',
    description: '配置库存、价格、标题等上品参数',
    icon: '📦'
  }
]

// 店小秘登录状态
const dianxiaomiLoginStatus = ref({
  isLoggedIn: false,
  message: '请先登录店小秘ERP系统',
  loading: false
})

// 数据状态
const shopAccounts = ref<ShopAccount[]>([])
const warehouses = ref<Record<string, Record<string, Record<string, string>>>>({})
const freightTemplates = ref<FreightTemplate[]>([])
const productCategories = ref<ProductCategory[]>([])

// 加载状态
const loadingStates = ref({
  shopAccounts: false,
  warehouses: false,
  freightTemplates: false,
  productCategories: false
})

// 基础设置表单
const basicForm = ref({
  erpPlatform: '店小秘',
  publishSite: '',
  shopAccount: '',
  publishStatus: '2',
  businessSite: '',
  warehouse: '',
  freightTemplate: '',
  shippingTime: '86400',
  venue: '',
  productCategory: '',
  productAttributes: [],
  publishStatusOptions: [],
  shippingTimeOptions: [],
  venueOptions: [],
  customSettings: {}
})

// 上品配置表单
const configForm = ref({
  minStock: 12,
  fixedStock: null,
  enableDeduplication: true,
  titlePrefix: '',
  titleSuffix: '',
  uploadInterval: 0,
  priceMultiplier: 4,
  collectDetails: ['title', 'img'],
  collectSku: true,
  externalLink: false,
  defaultSize: {
    length: 10,
    width: 10,
    height: 10,
    weight: 20
  },
  filterProhibited: true,
  prohibitedWords: '',
  enableTranslation: false,
  translationService: 'google'
})

// 计算属性
const currentTemuShop = computed(() => {
  if (shopBinding.temuSiteInfo) {
    return `${shopBinding.temuSiteInfo.mallName} (${shopBinding.temuSiteInfo.isSemiManagedMall ? '半托' : '全托'})`
  }
  return 'Temu'
})

// 切换步骤
const switchStep = (step: number) => {
  activeStep.value = step
}

// 检查店小秘登录状态
const checkDianxiaomiLogin = async () => {
  dianxiaomiLoginStatus.value.loading = true
  try {
    const result = await dianxiaomiDetectionService.checkLoginStatus()
    dianxiaomiLoginStatus.value = {
      isLoggedIn: result.isLoggedIn,
      message: result.message,
      loading: false
    }

    if (result.isLoggedIn) {
      // 如果已登录，自动获取数据
      await Promise.all([
        loadShopAccounts(),
        loadWarehouses(),
        loadFreightTemplates(),
        loadProductCategories()
      ])
    }
  } catch (err) {
    console.error('[ProductCenter] 检测店小秘登录失败:', err)
    dianxiaomiLoginStatus.value = {
      isLoggedIn: false,
      message: '检测失败，请重试',
      loading: false
    }
  }
}

const openDianxiaomiERP = () => {
  window.open('https://www.dianxiaomi.com', '_blank')
}

const loadShopAccounts = async () => {
  loadingStates.value.shopAccounts = true
  try {
    const result = await dianxiaomiDetectionService.getShopAccounts()
    if (result.success && result.data) {
      shopAccounts.value = result.data
      success('店铺账号同步成功', `成功获取 ${result.data.length} 个店铺账号`)
    } else {
      error('获取店铺账号失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载店铺账号失败:', err)
    error('加载店铺账号失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.shopAccounts = false
  }
}

const loadWarehouses = async () => {
  loadingStates.value.warehouses = true
  try {
    const result = await dianxiaomiDetectionService.getWarehouses()
    if (result.success && result.data) {
      warehouses.value = result.data
      let warehouseCount = 0
      Object.values(result.data).forEach(shopWarehouses => {
        Object.values(shopWarehouses).forEach(siteWarehouses => {
          warehouseCount += Object.keys(siteWarehouses).length
        })
      })
      success('发货仓库同步成功', `成功获取 ${warehouseCount} 个发货仓库`)
    } else {
      error('获取发货仓库失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载发货仓库失败:', err)
    error('加载发货仓库失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.warehouses = false
  }
}

const loadFreightTemplates = async () => {
  loadingStates.value.freightTemplates = true
  try {
    const result = await dianxiaomiDetectionService.getFreightTemplates()
    if (result.success && result.data) {
      freightTemplates.value = result.data
      success('运费模板同步成功', `成功获取 ${result.data.length} 个运费模板`)
    } else {
      error('获取运费模板失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载运费模板失败:', err)
    error('加载运费模板失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.freightTemplates = false
  }
}

const loadProductCategories = async () => {
  loadingStates.value.productCategories = true
  try {
    const result = await dianxiaomiDetectionService.getProductCategories()
    if (result.success && result.data) {
      productCategories.value = result.data
    } else {
      error('获取商品分类失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载商品分类失败:', err)
    error('加载商品分类失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.productCategories = false
  }
}

// 保存方法
const saveBasicSettings = async () => {
  try {
    console.info('保存基础设置:', basicForm.value)
    await configStorageService.saveBasicConfig(basicForm.value as BasicConfig)
    success('基础设置保存成功！', '配置已保存到本地缓存')
  } catch (err) {
    console.error('保存基础设置失败:', err)
    error('保存基础设置失败', err instanceof Error ? err.message : '未知错误')
  }
}

const saveConfig = async () => {
  try {
    console.info('保存上品配置:', configForm.value)
    await configStorageService.saveProductConfig(configForm.value as ProductConfigType)
    success('上品配置保存成功！', '配置已保存到本地缓存')
  } catch (err) {
    console.error('保存上品配置失败:', err)
    error('保存上品配置失败', err instanceof Error ? err.message : '未知错误')
  }
}

// 加载配置
const loadConfigs = async () => {
  try {
    console.info('[ProductCenter] 开始加载本地配置...')
    
    // 加载基础设置配置
    const basicConfig = await configStorageService.getBasicConfig()
    Object.assign(basicForm.value, basicConfig)
    console.info('[ProductCenter] 基础设置配置已加载:', basicConfig)

    // 加载上品配置
    const productConfig = await configStorageService.getProductConfig()
    Object.assign(configForm.value, productConfig)
    console.info('[ProductCenter] 上品配置已加载:', productConfig)

  } catch (err) {
    console.error('[ProductCenter] 加载配置失败:', err)
    error('加载配置失败', err instanceof Error ? err.message : '未知错误')
  }
}

// 生命周期
onMounted(async () => {
  // 并行执行配置加载和登录检查
  await Promise.all([
    loadConfigs(),
    checkDianxiaomiLogin()
  ])
})
</script>

<template>
  <div class="fullscreen-dashboard">
    <!-- 工具栏 -->
    <div class="fullscreen-toolbar">
      <div class="toolbar-left">
        <h1 style="margin: 0; font-size: 24px; color: #333;">
          <span>🏭</span>
          <span>上品中心</span>
        </h1>
        <a-tag color="blue">配置ERP平台连接和商品上传参数</a-tag>
      </div>

      <div class="toolbar-right">
        <!-- 当前店铺信息 -->
        <div class="shop-info">
          <span style="color: #666; margin-right: 8px;">当前店铺:</span>
          <a-tag color="green">{{ currentTemuShop }}</a-tag>
        </div>
      </div>
    </div>

    <!-- 主内容卡片 -->
    <div class="fullscreen-card">
      <!-- 步骤导航 -->
      <div class="steps-header">
        <a-steps
          :current="activeStep"
          class="fullscreen-steps"
          @change="switchStep"
        >
          <a-step
            v-for="(step, index) in steps"
            :key="index"
            :title="step.title"
            :description="step.description"
          >
            <template #icon>
              <span style="font-size: 20px;">{{ step.icon }}</span>
            </template>
          </a-step>
        </a-steps>
      </div>

      <!-- 内容区域 -->
      <div class="step-content-container">
        <!-- 基础设置 -->
        <div v-if="activeStep === 0" class="step-content">
          <div class="content-header">
            <h2>⚙️ 基础设置</h2>
            <p>配置ERP平台连接、店铺信息和基础参数</p>
          </div>

          <!-- ERP连接状态 -->
          <div class="erp-status-card">
            <div class="status-header">
              <h3>🔗 ERP平台连接状态</h3>
              <a-button
                type="primary"
                :loading="dianxiaomiLoginStatus.loading"
                @click="checkDianxiaomiLogin"
              >
                检测连接
              </a-button>
            </div>
            
            <div class="status-content">
              <a-result
                :status="dianxiaomiLoginStatus.isLoggedIn ? 'success' : 'warning'"
                :title="dianxiaomiLoginStatus.isLoggedIn ? '店小秘ERP已连接' : '店小秘ERP未连接'"
                :sub-title="dianxiaomiLoginStatus.message"
              >
                <template #extra>
                  <a-button
                    v-if="!dianxiaomiLoginStatus.isLoggedIn"
                    type="primary"
                    @click="openDianxiaomiERP"
                  >
                    打开店小秘ERP
                  </a-button>
                </template>
              </a-result>
            </div>
          </div>

          <!-- 基础配置表单 -->
          <div v-if="dianxiaomiLoginStatus.isLoggedIn" class="config-form-card">
            <div class="form-header">
              <h3>📋 基础配置</h3>
            </div>
            
            <a-form
              :model="basicForm"
              layout="vertical"
              class="fullscreen-form"
            >
              <a-row :gutter="24">
                <a-col :span="8">
                  <a-form-item label="ERP平台">
                    <a-select v-model:value="basicForm.erpPlatform" disabled>
                      <a-select-option value="店小秘">店小秘</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                
                <a-col :span="8">
                  <a-form-item label="发布站点">
                    <a-select
                      v-model:value="basicForm.publishSite"
                      placeholder="选择发布站点"
                    >
                      <a-select-option
                        v-for="site in getSiteOptions()"
                        :key="site.code"
                        :value="site.code"
                      >
                        {{ site.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="店铺账号">
                    <a-select
                      v-model:value="basicForm.shopAccount"
                      placeholder="选择店铺账号"
                      :loading="loadingStates.shopAccounts"
                    >
                      <a-select-option
                        v-for="account in shopAccounts"
                        :key="account.shopId"
                        :value="account.shopId"
                      >
                        {{ account.shopName }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="24">
                <a-col :span="8">
                  <a-form-item label="发布状态">
                    <a-select
                      v-model:value="basicForm.publishStatus"
                      placeholder="选择发布状态"
                    >
                      <a-select-option value="1">立即发布</a-select-option>
                      <a-select-option value="2">保存草稿</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="发货仓库">
                    <a-select
                      v-model:value="basicForm.warehouse"
                      placeholder="选择发货仓库"
                      :loading="loadingStates.warehouses"
                    >
                      <!-- 仓库选项会根据选择的店铺和站点动态加载 -->
                    </a-select>
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="运费模板">
                    <a-select
                      v-model:value="basicForm.freightTemplate"
                      placeholder="选择运费模板"
                      :loading="loadingStates.freightTemplates"
                    >
                      <a-select-option
                        v-for="template in freightTemplates"
                        :key="template.templateId"
                        :value="template.templateId"
                      >
                        {{ template.templateName }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <div class="form-actions">
                <a-button type="primary" size="large" @click="saveBasicSettings">
                  💾 保存基础设置
                </a-button>
              </div>
            </a-form>
          </div>
        </div>

        <!-- 上品配置 -->
        <div v-else-if="activeStep === 1" class="step-content">
          <div class="content-header">
            <h2>📦 上品配置</h2>
            <p>配置库存、价格、标题等商品上传参数</p>
          </div>

          <div class="config-form-card">
            <a-form
              :model="configForm"
              layout="vertical"
              class="fullscreen-form"
            >
              <a-row :gutter="24">
                <a-col :span="8">
                  <a-form-item label="最小库存">
                    <a-input-number
                      v-model:value="configForm.minStock"
                      :min="1"
                      :max="999"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="价格倍数">
                    <a-input-number
                      v-model:value="configForm.priceMultiplier"
                      :min="1"
                      :max="10"
                      :step="0.1"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="上传间隔(秒)">
                    <a-input-number
                      v-model:value="configForm.uploadInterval"
                      :min="0"
                      :max="3600"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="标题前缀">
                    <a-input
                      v-model:value="configForm.titlePrefix"
                      placeholder="商品标题前缀"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="标题后缀">
                    <a-input
                      v-model:value="configForm.titleSuffix"
                      placeholder="商品标题后缀"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="24">
                <a-col :span="24">
                  <a-form-item label="功能开关">
                    <a-space direction="vertical" style="width: 100%">
                      <a-checkbox v-model:checked="configForm.enableDeduplication">
                        启用去重功能
                      </a-checkbox>
                      <a-checkbox v-model:checked="configForm.collectSku">
                        采集SKU信息
                      </a-checkbox>
                      <a-checkbox v-model:checked="configForm.externalLink">
                        保留外部链接
                      </a-checkbox>
                      <a-checkbox v-model:checked="configForm.filterProhibited">
                        过滤违禁词
                      </a-checkbox>
                      <a-checkbox v-model:checked="configForm.enableTranslation">
                        启用翻译功能
                      </a-checkbox>
                    </a-space>
                  </a-form-item>
                </a-col>
              </a-row>

              <div class="form-actions">
                <a-button type="primary" size="large" @click="saveConfig">
                  💾 保存上品配置
                </a-button>
              </div>
            </a-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 步骤导航 */
.steps-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 24px;
  border-bottom: 1px solid #dee2e6;
}

.fullscreen-steps {
  max-width: 800px;
  margin: 0 auto;
}

.fullscreen-steps :deep(.ant-steps-item-title) {
  font-size: 16px;
  font-weight: 600;
}

.fullscreen-steps :deep(.ant-steps-item-description) {
  font-size: 14px;
  color: #666;
}

/* 内容区域 */
.step-content-container {
  padding: 32px;
  background: #f8f9fa;
  min-height: calc(100vh - 300px);
}

.step-content {
  max-width: 1200px;
  margin: 0 auto;
}

.content-header {
  text-align: center;
  margin-bottom: 32px;
}

.content-header h2 {
  font-size: 28px;
  margin-bottom: 8px;
  color: #333;
}

.content-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* ERP状态卡片 */
.erp-status-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  overflow: hidden;
}

.status-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.status-content {
  padding: 24px;
}

/* 配置表单卡片 */
.config-form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* 表单样式 */
.fullscreen-form {
  padding: 32px;
}

.fullscreen-form :deep(.ant-form-item-label) {
  font-weight: 600;
  color: #333;
}

.fullscreen-form :deep(.ant-form-item) {
  margin-bottom: 24px;
}

.fullscreen-form :deep(.ant-select),
.fullscreen-form :deep(.ant-input),
.fullscreen-form :deep(.ant-input-number) {
  border-radius: 6px;
}

.fullscreen-form :deep(.ant-select:hover),
.fullscreen-form :deep(.ant-input:hover),
.fullscreen-form :deep(.ant-input-number:hover) {
  border-color: #40a9ff;
}

.fullscreen-form :deep(.ant-select-focused),
.fullscreen-form :deep(.ant-input:focus),
.fullscreen-form :deep(.ant-input-number:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表单操作区域 */
.form-actions {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
  margin-top: 24px;
}

.form-actions .ant-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

/* 店铺信息 */
.shop-info {
  display: flex;
  align-items: center;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .step-content-container {
    padding: 24px 16px;
  }

  .fullscreen-form {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .steps-header {
    padding: 16px;
  }

  .step-content-container {
    padding: 16px;
  }

  .fullscreen-form {
    padding: 16px;
  }

  .status-header,
  .form-header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .content-header h2 {
    font-size: 24px;
  }

  .content-header p {
    font-size: 14px;
  }
}

/* 功能开关样式 */
.fullscreen-form :deep(.ant-checkbox-wrapper) {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.fullscreen-form :deep(.ant-checkbox-wrapper:hover) {
  border-color: #40a9ff;
  background: #f6ffed;
}

.fullscreen-form :deep(.ant-checkbox-wrapper-checked) {
  border-color: #52c41a;
  background: #f6ffed;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 结果页面样式 */
.status-content :deep(.ant-result) {
  padding: 24px 0;
}

.status-content :deep(.ant-result-title) {
  font-size: 20px;
  font-weight: 600;
}

.status-content :deep(.ant-result-subtitle) {
  font-size: 14px;
  color: #666;
}
</style>
