<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useShopBinding } from '../../../composables/useShopBinding'
import { useNotification } from '../../../composables/useNotification'
import { dianxiaomiApiService } from '../../../services/dxm/apiService'
import type { ShopAccount, FreightTemplate, ProductCategory } from '../../../services/dxm/apiService'
import configStorageService from '../../../services/configStorageService'
import type { BasicConfig, ProductConfig as ProductConfigType } from '../../../services/configStorageService'
import BasicSettings from '../components/BasicSettings.vue'
import ProductConfig from '../components/ProductConfig.vue'

// 使用组合式函数
const { shopBinding } = useShopBinding()
const { success, error } = useNotification()

// 步骤定义
const steps = [
  {
    title: '基础设置',
    description: '配置ERP平台、店铺信息等基础设置'
  },
  {
    title: '上品配置',
    description: '配置库存、价格、标题等上品参数'
  }
]

// 当前步骤
const activeStep = ref(0)

// 切换步骤
const switchStep = (step: number) => {
  activeStep.value = step
}

// 店小秘登录状态
const dianxiaomiLoginStatus = ref({
  isLoggedIn: false,
  message: '请先登录店小秘ERP系统',
  loading: false
})

// 数据状态
const shopAccounts = ref<ShopAccount[]>([])
const warehouses = ref<Record<string, Record<string, Record<string, string>>>>({})
const freightTemplates = ref<FreightTemplate[]>([])
const productCategories = ref<ProductCategory[]>([])

// 加载状态
const loadingStates = ref({
  shopAccounts: false,
  warehouses: false,
  freightTemplates: false,
  productCategories: false
})

// 基础设置表单
const basicForm = ref({
  erpPlatform: '店小秘',
  publishSite: '',
  shopAccount: '',
  publishStatus: '2',
  businessSite: '',
  warehouse: '',
  freightTemplate: '',
  shippingTime: '86400',
  venue: '',
  productCategory: '',
  productAttributes: [],
  publishStatusOptions: [],
  shippingTimeOptions: [],
  venueOptions: [],
  customSettings: {}
})

// 上品配置表单
const configForm = ref({
  minStock: 12,
  fixedStock: null,
  enableDeduplication: true,
  titlePrefix: '',
  titleSuffix: '',
  uploadInterval: 0,
  priceMultiplier: 4,
  collectDetails: ['title', 'img'],
  collectSku: true,
  externalLink: false,
  defaultSize: {
    length: 10,
    width: 10,
    height: 10,
    weight: 20
  },
  filterProhibited: true,
  prohibitedWords: '',
  enableTranslation: false,
  translationService: 'google'
})

// 移除了货盘采集表单，因为该功能暂时不显示

// 计算属性
const currentTemuShop = computed(() => {
  if (shopBinding.temuSiteInfo) {
    return `${shopBinding.temuSiteInfo.mallName} (${shopBinding.temuSiteInfo.isSemiManagedMall ? '半托' : '全托'})`
  }
  return 'Temu'
})

// 仓库选项
const getWarehouseOptions = computed(() => {
  const options: Array<{ value: string; label: string; shopId: string; site: string }> = []

  Object.entries(warehouses.value).forEach(([shopId, shopWarehouses]) => {
    Object.entries(shopWarehouses).forEach(([site, siteWarehouses]) => {
      Object.entries(siteWarehouses).forEach(([warehouseId, warehouseName]) => {
        options.push({
          value: warehouseId,
          label: `${warehouseName} (店铺: ${shopId}, 站点: ${site})`,
          shopId,
          site
        })
      })
    })
  })

  return options
})

// 检查授权是否即将过期（30天内）
const isExpiringSoon = (expireTime: string): boolean => {
  if (!expireTime) return false
  try {
    const expireDate = new Date(expireTime)
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 30 && diffDays > 0
  } catch {
    return false
  }
}

// 方法
const checkDianxiaomiLogin = async () => {
  dianxiaomiLoginStatus.value.loading = true
  try {
    const result = await dianxiaomiApiService.checkLoginStatus()
    dianxiaomiLoginStatus.value = {
      isLoggedIn: result.isLoggedIn,
      message: result.message,
      loading: false
    }

    if (result.isLoggedIn) {
      // 如果已登录，自动获取数据
      await Promise.all([
        loadShopAccounts(),
        loadWarehouses(),
        loadFreightTemplates(),
        loadProductCategories()
      ])
    }
  } catch (err) {
    console.error('[ProductCenter] 检测店小秘登录失败:', err)
    dianxiaomiLoginStatus.value = {
      isLoggedIn: false,
      message: '检测失败，请重试',
      loading: false
    }
  }
}

const openDianxiaomiERP = () => {
  window.open('https://www.dianxiaomi.com', '_blank')
}

const loadShopAccounts = async () => {
  console.info('[ProductCenter] 开始加载店铺账号...')
  loadingStates.value.shopAccounts = true
  try {
    console.info('[ProductCenter] 调用 dianxiaomiApiService.getShopAccounts()')
    const result = await dianxiaomiApiService.getShopAccounts()
    console.info('[ProductCenter] 店铺账号API返回结果:', result)

    if (result.success && result.data) {
      shopAccounts.value = result.data
      console.info('[ProductCenter] 成功设置店铺账号数据:', result.data)

      // 优先使用session中保存的选中店铺，否则选择第一个
      const selectedShopId = await dianxiaomiApiService.getSelectedShopId()
      if (selectedShopId && result.data.some(shop => shop.shopId === selectedShopId)) {
        basicForm.value.shopAccount = selectedShopId
        console.info('[ProductCenter] 使用session中的选中店铺:', selectedShopId)
      } else if (result.data.length > 0 && !basicForm.value.shopAccount) {
        basicForm.value.shopAccount = result.data[0].shopId
        console.info('[ProductCenter] 默认选择第一个店铺:', result.data[0])
        // 保存到session
        await dianxiaomiApiService.setSelectedShopId(result.data[0].shopId)
      }

      // 显示成功提示
      success('店铺账号同步成功', `成功获取 ${result.data.length} 个店铺账号`)
    } else {
      console.error('[ProductCenter] 获取店铺账号失败:', result.error)
      error('获取店铺账号失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载店铺账号异常:', err)
    error('加载店铺账号失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.shopAccounts = false
    console.info('[ProductCenter] 店铺账号加载完成')
  }
}

const loadWarehouses = async () => {
  loadingStates.value.warehouses = true
  try {
    const result = await dianxiaomiApiService.getWarehouses()
    if (result.success && result.data) {
      warehouses.value = result.data

      // 计算仓库总数
      let warehouseCount = 0
      Object.values(result.data).forEach(shopWarehouses => {
        Object.values(shopWarehouses).forEach(siteWarehouses => {
          warehouseCount += Object.keys(siteWarehouses).length
        })
      })

      // 显示成功提示
      success('发货仓库同步成功', `成功获取 ${warehouseCount} 个发货仓库`)
    } else {
      error('获取发货仓库失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载发货仓库失败:', err)
    error('加载发货仓库失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.warehouses = false
  }
}

const loadFreightTemplates = async () => {
  loadingStates.value.freightTemplates = true
  try {
    const result = await dianxiaomiApiService.getFreightTemplates()
    if (result.success && result.data) {
      freightTemplates.value = result.data
      // 如果有运费模板，默认选择第一个
      if (result.data.length > 0 && !basicForm.value.freightTemplate) {
        basicForm.value.freightTemplate = result.data[0].freightTemplateId
      }

      // 显示成功提示
      success('运费模板同步成功', `成功获取 ${result.data.length} 个运费模板`)
    } else {
      error('获取运费模板失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载运费模板失败:', err)
    error('加载运费模板失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.freightTemplates = false
  }
}

const loadProductCategories = async () => {
  loadingStates.value.productCategories = true
  try {
    const result = await dianxiaomiApiService.getProductCategories()
    if (result.success && result.data) {
      productCategories.value = result.data
    } else {
      error('获取商品分类失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载商品分类失败:', err)
    error('加载商品分类失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.productCategories = false
  }
}

const onShopAccountChange = async (shopId: string) => {
  if (shopId) {
    console.info('[ProductCenter] 店铺选择变化:', shopId)
    await dianxiaomiApiService.setSelectedShopId(shopId)

    // 重新加载依赖店铺的数据
    await Promise.all([
      loadWarehouses(),
      loadFreightTemplates()
    ])
  }
}

const onCategoryChange = (category: ProductCategory | null) => {
  console.info('[ProductCenter] 分类变更:', category)
  if (category) {
    basicForm.value.productCategory = category.catName
  }
}

const loadCategoriesByParent = async (parentId?: number): Promise<ProductCategory[]> => {
  try {
    return await dianxiaomiApiService.getCategoriesByParent(parentId)
  } catch (err) {
    console.error('[ProductCenter] 加载子分类失败:', err)
    return []
  }
}

// 保存方法
const saveBasicSettings = async () => {
  try {
    console.info('保存基础设置:', basicForm.value)
    await configStorageService.saveBasicConfig(basicForm.value as BasicConfig)
    success('基础设置保存成功！', '配置已保存到本地缓存')
  } catch (err) {
    console.error('保存基础设置失败:', err)
    error('保存基础设置失败', err instanceof Error ? err.message : '未知错误')
  }
}

const saveConfig = async () => {
  try {
    console.info('保存上品配置:', configForm.value)
    await configStorageService.saveProductConfig(configForm.value as ProductConfigType)
    success('上品配置保存成功！', '配置已保存到本地缓存')
  } catch (err) {
    console.error('保存上品配置失败:', err)
    error('保存上品配置失败', err instanceof Error ? err.message : '未知错误')
  }
}

// 加载配置
const loadConfigs = async () => {
  try {
    console.info('[ProductCenter] 开始加载本地配置...')

    // 加载基础设置配置
    const basicConfig = await configStorageService.getBasicConfig()
    Object.assign(basicForm.value, basicConfig)
    console.info('[ProductCenter] 基础设置配置已加载:', basicConfig)

    // 加载上品配置
    const productConfig = await configStorageService.getProductConfig()
    Object.assign(configForm.value, productConfig)
    console.info('[ProductCenter] 上品配置已加载:', productConfig)

  } catch (err) {
    console.error('[ProductCenter] 加载配置失败:', err)
    error('加载配置失败', err instanceof Error ? err.message : '未知错误')
  }
}



// 生命周期
onMounted(async () => {
  // 并行执行配置加载和登录检查
  await Promise.all([
    loadConfigs(),
    checkDianxiaomiLogin()
  ])
})
</script>

<template>
  <div class="extension-container custom-scrollbar">
    <div class="extension-content">
      <!-- 主内容卡片 -->
      <a-card class="main-content-card hover-lift">
        <!-- 步骤导航 -->
        <div class="steps-wrapper">
          <a-steps
            :current="activeStep"
            class="product-steps"
            @change="switchStep"
          >
            <a-step
              v-for="(step, index) in steps"
              :key="index"
              :title="step.title"
              :description="step.description"
            />
          </a-steps>
        </div>

        <!-- 内容区域 -->
        <div class="step-content">
          <!-- 基础设置 -->
          <div
            v-if="activeStep === 0"
            class="step-panel"
          >
            <BasicSettings
              v-model:basic-form="basicForm"
              :dianxiaomi-login-status="dianxiaomiLoginStatus"
              :shop-accounts="shopAccounts"
              :warehouses="warehouses"
              :freight-templates="freightTemplates"
              :product-categories="productCategories"
              :loading-states="loadingStates"
              :current-temu-shop="currentTemuShop"
              @check-login="checkDianxiaomiLogin"
              @open-erp="openDianxiaomiERP"
              @load-shop-accounts="loadShopAccounts"
              @load-warehouses="loadWarehouses"
              @load-freight-templates="loadFreightTemplates"
              @load-product-categories="loadProductCategories"
              @shop-account-change="onShopAccountChange"
              @category-change="onCategoryChange"
              @load-categories-by-parent="loadCategoriesByParent"
              @save-settings="saveBasicSettings"
            />
          </div>

          <!-- 上品配置 -->
          <div
            v-else-if="activeStep === 1"
            class="step-panel"
          >
            <ProductConfig
              v-model:config-form="configForm"
              @save-config="saveConfig"
            />
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped>
/* ========================================
   上品中心页面专用样式 - 基于新设计系统
======================================== */

/* 页面标题区域 */
.page-header {
  margin-bottom: var(--space-xl);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-lg);
}

.title-section {
  text-align: center;
  flex: 1;
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  margin: 0 0 var(--space-sm) 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
}

.page-icon {
  font-size: var(--text-4xl);
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: var(--text-base);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
}



/* 主内容卡片 */
.main-content-card {
  margin-bottom: var(--space-xl);
}

/* 步骤导航 */
.steps-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-2xl);
  padding: 0 var(--space-lg);
}

.product-steps {
  width: 100%;
  max-width: 800px;
}

/* 步骤内容区域 - 最外层滚动 */
.step-content {
  width: 100%;
  height: calc(100vh - 280px); /* 减去页面标题和步骤导航的高度 */
  overflow-y: auto;
  overflow-x: hidden;
  /* 最外层滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.step-panel {
  width: 100%;
  max-width: 1000px; /* 限制最大宽度，居中显示 */
  min-height: 100%;
  padding: var(--space-lg);
  margin: 0 auto; /* 水平居中 */
  overflow: visible; /* 不处理滚动，由父容器处理 */
}

/* Webkit 浏览器滚动条样式 - 最外层滚动条 */
.step-content::-webkit-scrollbar {
  width: 6px;
}

.step-content::-webkit-scrollbar-track {
  background: transparent;
}

.step-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-sm);
  transition: background-color 0.2s ease;
}

.step-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Ant Design Steps 组件定制 */
:deep(.product-steps .ant-steps-item) {
  flex: 1;
}

:deep(.product-steps .ant-steps-item-title) {
  font-weight: var(--font-semibold);
  font-size: var(--text-base);
  color: var(--text-primary);
  text-align: center;
}

:deep(.product-steps .ant-steps-item-description) {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  text-align: center;
  margin-top: var(--space-xs);
}

:deep(.product-steps .ant-steps-item-content) {
  text-align: center;
}

:deep(.product-steps .ant-steps-item-process .ant-steps-item-icon) {
  background: var(--brand-primary);
  border-color: var(--brand-primary);
}

:deep(.product-steps .ant-steps-item-finish .ant-steps-item-icon) {
  background: var(--success-color);
  border-color: var(--success-color);
}

:deep(.product-steps .ant-steps-item-wait .ant-steps-item-icon) {
  background: var(--bg-container);
  border-color: var(--border-light);
}

/* 响应式设计 - Chrome Extension Side Panel 优化 */
@media (max-width: 768px) {
  .page-title {
    font-size: var(--text-2xl);
    flex-direction: column;
    gap: var(--space-sm);
  }

  .page-icon {
    font-size: var(--text-3xl);
  }

  .steps-wrapper {
    padding: 0 var(--space-sm);
  }

  .step-content {
    height: calc(100vh - 240px); /* 移动端调整高度 */
  }

  .step-panel {
    max-width: 100%; /* 移动端使用全宽 */
    padding: var(--space-md);
  }

  /* 移动端表单宽度调整 */
  :deep(.basic-settings-container),
  :deep(.product-config-container) {
    max-width: 100%;
    padding: 0 var(--space-sm);
  }

  /* 移动端保存按钮优化 */
  :deep(.basic-settings-container .flex.justify-center),
  :deep(.product-config-container .flex.justify-center) {
    padding: var(--space-md) 0;
    margin-bottom: var(--space-sm);
  }

  :deep(.product-steps .ant-steps-item-title) {
    font-size: var(--text-sm);
  }

  :deep(.product-steps .ant-steps-item-description) {
    font-size: var(--text-xs);
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: var(--text-xl);
  }

  .steps-wrapper {
    margin-bottom: var(--space-lg);
  }

  .step-content {
    height: calc(100vh - 200px); /* 小屏幕进一步调整高度 */
  }

  .step-panel {
    max-width: 100%; /* 小屏幕使用全宽 */
    padding: var(--space-sm);
  }

  /* 小屏幕表单布局优化 */
  :deep(.basic-settings-container),
  :deep(.product-config-container) {
    max-width: 100%;
    padding: 0;
  }

  :deep(.product-steps) {
    direction: ltr;
  }

  :deep(.product-steps .ant-steps-item-description) {
    display: none;
  }

  /* 小屏幕下保存按钮优化 */
  :deep(.basic-settings-container .flex.justify-center),
  :deep(.product-config-container .flex.justify-center) {
    padding: var(--space-md) 0;
    margin-bottom: var(--space-sm);
    flex-direction: column;
    gap: var(--space-sm);
  }
}

/* 确保子组件内容居中排列 */
:deep(.basic-settings-container),
:deep(.product-config-container) {
  min-height: auto; /* 自适应高度 */
  max-width: 600px; /* 限制表单最大宽度 */
  margin: 0 auto; /* 水平居中 */
  padding-bottom: var(--space-2xl);
  overflow: visible; /* 不处理滚动 */
}

/* 表单布局优化 */
:deep(.basic-settings-container .ant-form),
:deep(.product-config-container .ant-form) {
  max-width: 100%;
}

/* 表单项布局调整 */
:deep(.basic-settings-container .ant-form-item),
:deep(.product-config-container .ant-form-item) {
  margin-bottom: var(--space-lg);
}

/* 卡片组件居中 */
:deep(.basic-settings-container .ant-card),
:deep(.product-config-container .ant-card) {
  margin-bottom: var(--space-lg);
}

/* 保存按钮区域样式 - 居中排列 */
:deep(.basic-settings-container .flex.justify-center),
:deep(.product-config-container .flex.justify-center) {
  margin-top: var(--space-xl);
  margin-bottom: var(--space-lg);
  padding: var(--space-lg) 0;
  border-top: 1px solid var(--border-color);
  background: var(--bg-container);
  text-align: center;
  display: flex !important;
  justify-content: center !important;
  align-items: center;
  gap: var(--space-md);
}
</style>