/* 全屏页面样式 */
@import url('../../ui/side-panel/index.css');

/* 全屏特定样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 全屏布局 */
.fullscreen-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.fullscreen-header {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.fullscreen-content {
  flex: 1;
  overflow: auto;
  background: var(--bg-secondary);
}

/* 头部样式 */
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: bold;
}

.header-nav {
  display: flex;
  gap: 24px;
}

.nav-item {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-item:hover,
.nav-item.active {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .fullscreen-header {
    padding: 0 16px;
  }
  
  .header-nav {
    gap: 16px;
  }
  
  .nav-item {
    padding: 6px 12px;
    font-size: 14px;
  }
}

/* 内容区域优化 */
.fullscreen-dashboard {
  padding: 24px;
  max-width: none;
  height: calc(100vh - 60px);
  overflow: auto;
}

/* 卡片样式优化 */
.fullscreen-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  overflow: hidden;
}

/* 表格优化 */
.fullscreen-table {
  width: 100%;
}

.fullscreen-table .ant-table {
  font-size: 14px;
}

.fullscreen-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
}

/* 工具栏优化 */
.fullscreen-toolbar {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 搜索表单优化 */
.fullscreen-search {
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.search-form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

.search-form-item label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 按钮组优化 */
.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.status-error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 滚动条样式 */
.fullscreen-content::-webkit-scrollbar {
  width: 8px;
}

.fullscreen-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.fullscreen-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.fullscreen-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
