import { useBrowserLocalStorage } from '../composables/useBrowserStorage'

// 发布状态选项
export interface PublishStatusOption {
  value: string
  label: string
  description?: string
}

// 发货时效选项
export interface ShippingTimeOption {
  value: string
  label: string
  days: number
}

// 核销地区选项
export interface VenueOption {
  value: string
  label: string
  country: string
  province?: string
}

// 产品属性配置（符合店小秘API格式）
export interface ProductAttribute {
  id: string
  name: string
  value: string
  type: 'text' | 'select' | 'number' | 'boolean'
  options?: string[]
  required?: boolean
  // 店小秘API字段
  propName: string
  propValue: string
  refPid: number
  vid: string
  pid: number
  templatePid: number
  numberInputValue: string
  valueUnit: string
}

// API配置
export interface ApiConfig {
  erp: string
  imageSize: number
  platform: string
  pointKey: string
  shippingFrom: Record<string, string[]>
  txt: string
  url: string
}

// 店铺信息配置
export interface ShopInfo {
  dXmId: string
  fromPlat: string
  ideaCurrency: string
  shopId: number
  shopName: string
}

// 站点配置
export interface SiteConfig {
  currentSite: string
  erp: string
}

// 基础设置配置类型
export interface BasicConfig {
  erpPlatform: string
  publishSite: string
  shopAccount: string
  publishStatus: string
  businessSite: string
  warehouse: string
  freightTemplate: string
  shippingTime: string
  venue: string
  productCategory: string
  productAttributes: ProductAttribute[]
  // 新增字段
  publishStatusOptions: PublishStatusOption[]
  shippingTimeOptions: ShippingTimeOption[]
  venueOptions: VenueOption[]
  // 店小秘API相关配置
  apiConfig: ApiConfig
  shopInfo: ShopInfo
  siteConfig: SiteConfig
  // 扩展配置
  customSettings: Record<string, any>
}

// 上品配置类型
export interface ProductConfig {
  minStock: number
  fixedStock: number | null
  enableDeduplication: boolean
  titlePrefix: string
  titleSuffix: string
  uploadInterval: number
  priceMultiplier: number
  collectDetails: string[]
  collectSku: boolean
  externalLink: boolean
  defaultSize: {
    length: number
    width: number
    height: number
    weight: number
  }
  filterProhibited: boolean
  prohibitedWords: string
  enableTranslation: boolean
  translationService: string
}

// 默认发布状态选项
const defaultPublishStatusOptions: PublishStatusOption[] = [
  { value: '1', label: '直接发布', description: '商品采集后直接发布到平台' },
  { value: '2', label: '移入DXM待发布', description: '商品采集后移入店小秘待发布列表' },
  { value: '3', label: '放到DXM草稿箱', description: '商品采集后保存到店小秘草稿箱' }
]

// 默认发货时效选项
const defaultShippingTimeOptions: ShippingTimeOption[] = [
  { value: '86400', label: '1个工作日内发货', days: 1 },
  { value: '172800', label: '2个工作日内发货', days: 2 },
  { value: '259200', label: '3个工作日内发货', days: 3 },
  { value: '777600', label: '9个工作日内发货(Y2)', days: 9 }
]

// 默认核销地区选项
const defaultVenueOptions: VenueOption[] = [
  { value: 'CN_GD', label: '中国/广东省', country: '中国', province: '广东省' },
  { value: 'CN_ZJ', label: '中国/浙江省', country: '中国', province: '浙江省' },
  { value: 'CN_JS', label: '中国/江苏省', country: '中国', province: '江苏省' },
  { value: 'CN_SH', label: '中国/上海市', country: '中国', province: '上海市' },
  { value: 'US', label: '美国', country: '美国' }
]



// 默认API配置
const defaultApiConfig: ApiConfig = {
  erp: 'dianXiaoMi',
  imageSize: 800,
  platform: 'temu',
  pointKey: 'popTemuSave',
  shippingFrom: {
    '100': ['us'],
    '101': [],
    '102': ['gb', 'de', 'uk'],
    '118': ['jp'],
    '103,104': [],
    '105,106,107,109': []
  },
  txt: 'popTemuSave.txt',
  url: 'api/popTemuProduct/add.json'
}

// 默认店铺信息
const defaultShopInfo: ShopInfo = {
  dXmId: '',
  fromPlat: 'temu',
  ideaCurrency: 'CNY',
  shopId: 0,
  shopName: ''
}

// 默认站点配置
const defaultSiteConfig: SiteConfig = {
  currentSite: 'ShangTemuView',
  erp: 'dianXiaoMi'
}

// 默认基础配置
const defaultBasicConfig: BasicConfig = {
  erpPlatform: '店小秘',
  publishSite: '',
  shopAccount: '',
  publishStatus: '2',
  businessSite: '',
  warehouse: '',
  freightTemplate: '',
  shippingTime: '86400',
  venue: '',
  productCategory: '',
  productAttributes: [],
  publishStatusOptions: [...defaultPublishStatusOptions],
  shippingTimeOptions: [...defaultShippingTimeOptions],
  venueOptions: [...defaultVenueOptions],
  apiConfig: { ...defaultApiConfig },
  shopInfo: { ...defaultShopInfo },
  siteConfig: { ...defaultSiteConfig },
  customSettings: {}
}

// 默认上品配置
const defaultProductConfig: ProductConfig = {
  minStock: 12,
  fixedStock: null,
  enableDeduplication: true,
  titlePrefix: '',
  titleSuffix: '',
  uploadInterval: 0,
  priceMultiplier: 4,
  collectDetails: ['title', 'img'],
  collectSku: true,
  externalLink: false,
  defaultSize: {
    length: 10,
    width: 10,
    height: 10,
    weight: 20
  },
  filterProhibited: true,
  prohibitedWords: '',
  enableTranslation: false,
  translationService: 'google'
}

class ConfigStorageService {
  private basicConfigStorage = useBrowserLocalStorage('temu-extension-basic-config', defaultBasicConfig)
  private productConfigStorage = useBrowserLocalStorage('temu-extension-product-config', defaultProductConfig)

  /**
   * 保存基础设置配置
   */
  async saveBasicConfig(config: BasicConfig): Promise<void> {
    try {
      this.basicConfigStorage.data.value = { ...config }
      console.info('[ConfigStorage] 基础设置配置已保存:', config)
    } catch (error) {
      console.error('[ConfigStorage] 保存基础设置配置失败:', error)
      throw error
    }
  }

  /**
   * 获取基础设置配置
   */
  async getBasicConfig(): Promise<BasicConfig> {
    try {
      await this.basicConfigStorage.promise
      const config = { ...this.basicConfigStorage.data.value }
      console.info('[ConfigStorage] 获取基础设置配置:', config)
      return config
    } catch (error) {
      console.error('[ConfigStorage] 获取基础设置配置失败:', error)
      return { ...defaultBasicConfig }
    }
  }

  /**
   * 保存上品配置
   */
  async saveProductConfig(config: ProductConfig): Promise<void> {
    try {
      this.productConfigStorage.data.value = { ...config }
      console.info('[ConfigStorage] 上品配置已保存:', config)
    } catch (error) {
      console.error('[ConfigStorage] 保存上品配置失败:', error)
      throw error
    }
  }

  /**
   * 获取上品配置
   */
  async getProductConfig(): Promise<ProductConfig> {
    try {
      await this.productConfigStorage.promise
      const config = { ...this.productConfigStorage.data.value }
      console.info('[ConfigStorage] 获取上品配置:', config)
      return config
    } catch (error) {
      console.error('[ConfigStorage] 获取上品配置失败:', error)
      return { ...defaultProductConfig }
    }
  }

  /**
   * 重置基础设置配置为默认值
   */
  async resetBasicConfig(): Promise<void> {
    try {
      this.basicConfigStorage.data.value = { ...defaultBasicConfig }
      console.info('[ConfigStorage] 基础设置配置已重置为默认值')
    } catch (error) {
      console.error('[ConfigStorage] 重置基础设置配置失败:', error)
      throw error
    }
  }

  /**
   * 重置上品配置为默认值
   */
  async resetProductConfig(): Promise<void> {
    try {
      this.productConfigStorage.data.value = { ...defaultProductConfig }
      console.info('[ConfigStorage] 上品配置已重置为默认值')
    } catch (error) {
      console.error('[ConfigStorage] 重置上品配置失败:', error)
      throw error
    }
  }

  /**
   * 获取完整配置（用于Amazon数据采集时调用）
   */
  async getFullConfig(): Promise<{ basic: BasicConfig; product: ProductConfig }> {
    try {
      const [basic, product] = await Promise.all([
        this.getBasicConfig(),
        this.getProductConfig()
      ])
      
      const fullConfig = { basic, product }
      console.info('[ConfigStorage] 获取完整配置:', fullConfig)
      return fullConfig
    } catch (error) {
      console.error('[ConfigStorage] 获取完整配置失败:', error)
      throw error
    }
  }

  /**
   * 检查配置是否完整（用于验证是否可以进行数据采集）
   */
  async isConfigComplete(): Promise<{ isComplete: boolean; missingFields: string[] }> {
    try {
      const { basic, product } = await this.getFullConfig()
      const missingFields: string[] = []

      // 检查基础设置必填字段
      if (!basic.shopAccount) missingFields.push('店铺账号')
      if (!basic.businessSite) missingFields.push('经营站点')
      if (!basic.warehouse) missingFields.push('发货仓库')
      if (!basic.freightTemplate) missingFields.push('运费模板')
      if (!basic.productCategory) missingFields.push('商品分类')
      if (!basic.publishStatus) missingFields.push('发布状态')
      if (!basic.shippingTime) missingFields.push('发货时效')
      if (!basic.venue) missingFields.push('核销地区')

      // 检查上品配置必填字段
      if (!product.minStock || product.minStock <= 0) missingFields.push('最小库存')
      if (!product.priceMultiplier || product.priceMultiplier <= 0) missingFields.push('价格倍数')

      const isComplete = missingFields.length === 0

      console.info('[ConfigStorage] 配置完整性检查:', { isComplete, missingFields })
      return { isComplete, missingFields }
    } catch (error) {
      console.error('[ConfigStorage] 配置完整性检查失败:', error)
      return { isComplete: false, missingFields: ['配置检查失败'] }
    }
  }

  /**
   * 获取发布状态选项
   */
  getPublishStatusOptions(): PublishStatusOption[] {
    return [...defaultPublishStatusOptions]
  }

  /**
   * 获取发货时效选项
   */
  getShippingTimeOptions(): ShippingTimeOption[] {
    return [...defaultShippingTimeOptions]
  }

  /**
   * 获取核销地区选项
   */
  getVenueOptions(): VenueOption[] {
    return [...defaultVenueOptions]
  }

  /**
   * 添加自定义产品属性
   */
  async addProductAttribute(attribute: Omit<ProductAttribute, 'id'>): Promise<void> {
    try {
      const config = await this.getBasicConfig()
      const newAttribute: ProductAttribute = {
        ...attribute,
        id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }

      config.productAttributes.push(newAttribute)
      await this.saveBasicConfig(config)

      console.info('[ConfigStorage] 添加产品属性成功:', newAttribute)
    } catch (error) {
      console.error('[ConfigStorage] 添加产品属性失败:', error)
      throw error
    }
  }

  /**
   * 删除产品属性
   */
  async removeProductAttribute(attributeId: string): Promise<void> {
    try {
      const config = await this.getBasicConfig()
      config.productAttributes = config.productAttributes.filter(attr => attr.id !== attributeId)
      await this.saveBasicConfig(config)

      console.info('[ConfigStorage] 删除产品属性成功:', attributeId)
    } catch (error) {
      console.error('[ConfigStorage] 删除产品属性失败:', error)
      throw error
    }
  }

  /**
   * 更新产品属性
   */
  async updateProductAttribute(attributeId: string, updates: Partial<ProductAttribute>): Promise<void> {
    try {
      const config = await this.getBasicConfig()
      const index = config.productAttributes.findIndex(attr => attr.id === attributeId)

      if (index === -1) {
        throw new Error(`产品属性 ${attributeId} 不存在`)
      }

      config.productAttributes[index] = { ...config.productAttributes[index], ...updates }
      await this.saveBasicConfig(config)

      console.info('[ConfigStorage] 更新产品属性成功:', { attributeId, updates })
    } catch (error) {
      console.error('[ConfigStorage] 更新产品属性失败:', error)
      throw error
    }
  }

  /**
   * 获取用于亚马逊采集的完整配置数据
   */
  async getAmazonCollectionConfig(): Promise<{
    basic: BasicConfig
    product: ProductConfig
    isComplete: boolean
    missingFields: string[]
  }> {
    try {
      const [fullConfig, completenessCheck] = await Promise.all([
        this.getFullConfig(),
        this.isConfigComplete()
      ])

      return {
        ...fullConfig,
        ...completenessCheck
      }
    } catch (error) {
      console.error('[ConfigStorage] 获取亚马逊采集配置失败:', error)
      throw error
    }
  }

  /**
   * 获取店小秘配置数据
   */
  async getDxmConfigData(): Promise<any> {
    try {
      const result = await chrome.storage.local.get('dxm-config-data')
      return result['dxm-config-data'] || null
    } catch (error) {
      console.error('[ConfigStorage] 获取店小秘配置数据失败:', error)
      return null
    }
  }

  /**
   * 保存店小秘配置数据
   */
  async saveDxmConfigData(configData: any): Promise<void> {
    try {
      const storageData = {
        ...configData,
        lastUpdated: new Date().toISOString(),
        source: 'manual-save'
      }

      await chrome.storage.local.set({ 'dxm-config-data': storageData })
      console.info('[ConfigStorage] 店小秘配置数据保存成功:', storageData)
    } catch (error) {
      console.error('[ConfigStorage] 保存店小秘配置数据失败:', error)
      throw error
    }
  }

  /**
   * 同步店小秘配置到基础配置
   */
  async syncDxmConfigToBasic(): Promise<void> {
    try {
      const dxmConfig = await this.getDxmConfigData()
      if (!dxmConfig) {
        console.warn('[ConfigStorage] 没有找到店小秘配置数据')
        return
      }

      const basicConfig = await this.getBasicConfig()

      // 同步分类信息
      if (dxmConfig.categoryId) {
        basicConfig.productCategory = dxmConfig.categoryId
      }

      // 同步店铺信息
      if (dxmConfig.shopId) {
        basicConfig.shopAccount = dxmConfig.shopId
      }

      // 同步运费模板
      if (dxmConfig.freightTemplateId) {
        basicConfig.freightTemplate = dxmConfig.freightTemplateId
      }

      // 同步仓库信息
      if (dxmConfig.warehouseId) {
        basicConfig.warehouse = dxmConfig.warehouseId
      }

      // 同步经营站点
      if (dxmConfig.businessSite) {
        basicConfig.businessSite = dxmConfig.businessSite
      }

      // 同步产品属性
      if (dxmConfig.attributes) {
        try {
          const attributesArray = JSON.parse(dxmConfig.attributes)
          if (Array.isArray(attributesArray)) {
            // 转换为我们的产品属性格式
            const convertedAttributes = attributesArray.map((attr, index) => ({
              id: `dxm_${attr.refPid || index}`,
              name: attr.propName || '',
              value: attr.propValue || '',
              type: 'text' as const,
              required: false,
              propName: attr.propName || '',
              propValue: attr.propValue || '',
              refPid: attr.refPid || 0,
              vid: attr.vid || '',
              pid: attr.pid || 0,
              templatePid: attr.templatePid || 0,
              numberInputValue: attr.numberInputValue || '',
              valueUnit: attr.valueUnit || ''
            }))

            basicConfig.productAttributes = convertedAttributes
          }
        } catch (error) {
          console.error('[ConfigStorage] 解析产品属性失败:', error)
        }
      }

      await this.saveBasicConfig(basicConfig)
      console.info('[ConfigStorage] 店小秘配置同步到基础配置成功')

    } catch (error) {
      console.error('[ConfigStorage] 同步店小秘配置失败:', error)
      throw error
    }
  }

  /**
   * 获取店小秘配置状态
   */
  async getDxmConfigStatus(): Promise<{
    hasConfig: boolean
    lastUpdated?: string
    categoryName?: string
    shopId?: string
    attributesCount?: number
  }> {
    try {
      const dxmConfig = await this.getDxmConfigData()

      if (!dxmConfig) {
        return { hasConfig: false }
      }

      let attributesCount = 0
      if (dxmConfig.attributes) {
        try {
          const attributesArray = JSON.parse(dxmConfig.attributes)
          attributesCount = Array.isArray(attributesArray) ? attributesArray.length : 0
        } catch (error) {
          console.warn('[ConfigStorage] 解析属性数量失败:', error)
        }
      }

      return {
        hasConfig: true,
        lastUpdated: dxmConfig.lastUpdated,
        categoryName: dxmConfig.categoryName,
        shopId: dxmConfig.shopId,
        attributesCount
      }
    } catch (error) {
      console.error('[ConfigStorage] 获取店小秘配置状态失败:', error)
      return { hasConfig: false }
    }
  }

  /**
   * 生成符合店小秘API格式的配置数据
   */
  async getDianxiaomiApiConfig(spuData: any, skuDataList: any[]): Promise<{
    apiConfig: ApiConfig
    data: any
    form: any
    siteConfig: SiteConfig
    userInfo?: any
  }> {
    try {
      const { basic, product } = await this.getFullConfig()

      // 构建属性数据
      const buildAttributes = () => {
        const attributes = []
        let index = 0

        // 添加配置中心的产品属性
        basic.productAttributes.forEach((attr) => {
          if (attr.value && attr.value.trim()) {
            attributes.push({
              valueUnit: attr.valueUnit || '',
              propValue: attr.value,
              propName: attr.propName || attr.name,
              refPid: attr.refPid || (4000 + index),
              vid: attr.vid || (67000 + index).toString(),
              pid: attr.pid || (1700 + index),
              templatePid: attr.templatePid || (1200000 + index),
              numberInputValue: attr.type === 'number' ? attr.value : ''
            })
            index++
          }
        })

        return JSON.stringify(attributes)
      }

      // 构建产品仓库路由
      const productWarehouseRouteReq = [{
        siteIdList: [parseInt(basic.businessSite) || 100],
        warehouseId: basic.warehouse
      }]

      const attributes = buildAttributes()
      const categoryId = parseInt(basic.productCategory) || 0

      const data = {
        attributes,
        categoryId,
        categoryType: 0,
        draftImgUrl: '',
        dxmPdfUrl: '',
        dxmState: basic.publishStatus === '2' ? 'online' : '',
        freightTemplateId: basic.freightTemplate,
        fullCid: '',
        goodsModel: '',
        id: null,
        instructionsId: '',
        instructionsName: '',
        instructionsTranslateId: '',
        op: 3,
        optionValue: '"[]"',
        outerGoodsUrl: spuData?.sourceUrl || '',
        packageImages: '',
        packageShape: '',
        packageType: '',
        productId: null,
        productOrigin: 'CN',
        productSemiManagedReq: '100',
        productWarehouseRouteReq,
        qualifiedEn: '',
        region2Id: basic.businessSite,
        sensitiveAttr: '',
        shipmentLimitSecond: basic.shippingTime,
        shopId: basic.shopAccount,
        shopInfo: {
          dXmId: basic.shopAccount,
          fromPlat: basic.siteConfig.currentSite === 'ShangTemuView' ? 'temu' : basic.siteConfig.currentSite,
          ideaCurrency: 'CNY',
          shopId: parseInt(basic.shopAccount) || 0,
          shopName: basic.shopInfo.shopName || ''
        },
        sizeTemplateIds: ''
      }

      const form = {
        attributes,
        categoryId,
        categoryName: basic.productCategory,
        categoryType: 0,
        certificationInfo: {},
        freightTemplateId: basic.freightTemplate,
        op: 3,
        shipmentLimitSecond: basic.shippingTime,
        shopId: basic.shopAccount,
        siteId: basic.businessSite,
        siteIds: [],
        warehouseId: []
      }

      return {
        apiConfig: basic.apiConfig,
        data,
        form,
        siteConfig: basic.siteConfig,
        userInfo: undefined // 用户信息需要从其他地方获取
      }
    } catch (error) {
      console.error('[ConfigStorage] 生成店小秘API配置失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const configStorageService = new ConfigStorageService()
export default configStorageService
