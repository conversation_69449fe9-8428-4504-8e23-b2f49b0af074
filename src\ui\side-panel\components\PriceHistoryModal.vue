<template>
  <div class="price-history-modal">
    <div class="product-summary">
      <div class="product-info">
        <a-image
          :src="product.image"
          :alt="product.title"
          :width="60"
          :height="60"
          class="product-image"
        />
        <div class="product-details">
          <div class="product-title">{{ product.title }}</div>
          <div class="product-meta">
            <span>货号: {{ product.sku.itemNo }}</span>
            <span>站点: {{ product.site }}</span>
          </div>
        </div>
      </div>
    </div>

    <a-divider />

    <div class="history-content">
      <div class="history-header">
        <h4>价格变更历史</h4>
        <a-button
          size="small"
          :loading="loading"
          @click="refreshHistory"
        >
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </div>

      <div class="current-status">
        <a-alert
          :message="`当前状态: ${getCurrentStatusText()}`"
          :type="getCurrentStatusType()"
          show-icon
          style="margin-bottom: 16px"
        />
      </div>

      <a-timeline>
        <a-timeline-item
          v-for="(item, index) in priceHistory"
          :key="index"
          :color="getTimelineColor(item.type)"
        >
          <template #dot>
            <component :is="getTimelineIcon(item.type)" />
          </template>
          <div class="timeline-content">
            <div class="timeline-header">
              <span class="timeline-title">{{ item.title }}</span>
              <span class="timeline-time">{{ formatTime(item.timestamp) }}</span>
            </div>
            <div class="timeline-details">
              <div
                v-if="item.price"
                class="price-info"
              >
                <span class="price-label">价格:</span>
                <span class="price-value">{{ item.price }}</span>
              </div>
              <div
                v-if="item.operator"
                class="operator-info"
              >
                <span class="operator-label">操作人:</span>
                <span class="operator-value">{{ item.operator }}</span>
              </div>
              <div
                v-if="item.reason"
                class="reason-info"
              >
                <span class="reason-label">原因:</span>
                <span class="reason-value">{{ item.reason }}</span>
              </div>
              <div
                v-if="item.description"
                class="description-info"
              >
                {{ item.description }}
              </div>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>

      <div
        v-if="priceHistory.length === 0 && !loading"
        class="empty-state"
      >
        <a-empty description="暂无价格历史记录" />
      </div>
    </div>

    <div class="modal-actions">
      <a-space>
        <a-button @click="$emit('close')">
          关闭
        </a-button>
        <a-button
          type="primary"
          @click="exportHistory"
        >
          <template #icon><DownloadOutlined /></template>
          导出历史
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  DownloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  product: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const loading = ref(false)
const priceHistory = ref([
  {
    type: 'created',
    title: '商品创建',
    timestamp: new Date(props.product.createTime).getTime(),
    price: props.product.priceInfo.supplierPriceText,
    operator: '系统',
    description: '商品首次创建，设置初始申报价格'
  },
  {
    type: 'submitted',
    title: '价格申报',
    timestamp: Date.now() - 86400000, // 1天前
    price: props.product.priceInfo.supplierPriceText,
    operator: '供应商',
    description: '供应商提交价格申报'
  },
  {
    type: 'reviewing',
    title: '价格审核中',
    timestamp: Date.now() - 43200000, // 12小时前
    operator: '系统',
    description: '价格进入审核流程'
  },
  {
    type: 'pending',
    title: '等待确认',
    timestamp: Date.now() - 3600000, // 1小时前
    operator: '系统',
    description: '价格审核通过，等待最终确认'
  }
])

// 方法
const refreshHistory = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取真实的价格历史数据
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    message.success('价格历史已刷新')
  } catch (error) {
    console.error('刷新价格历史失败:', error)
    message.error('刷新价格历史失败')
  } finally {
    loading.value = false
  }
}

const getCurrentStatusText = () => {
  const statusMap: Record<number, string> = {
    0: '待审核',
    1: '审核中',
    2: '待确认',
    3: '已拒绝'
  }
  return statusMap[props.product.priceInfo.priceReviewStatus] || '未知状态'
}

const getCurrentStatusType = () => {
  const typeMap: Record<number, string> = {
    0: 'info',
    1: 'warning',
    2: 'warning',
    3: 'error'
  }
  return typeMap[props.product.priceInfo.priceReviewStatus] || 'info'
}

const getTimelineColor = (type: string) => {
  const colorMap: Record<string, string> = {
    created: 'blue',
    submitted: 'green',
    reviewing: 'orange',
    pending: 'red',
    approved: 'green',
    rejected: 'red'
  }
  return colorMap[type] || 'gray'
}

const getTimelineIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    created: ClockCircleOutlined,
    submitted: CheckCircleOutlined,
    reviewing: ExclamationCircleOutlined,
    pending: ClockCircleOutlined,
    approved: CheckCircleOutlined,
    rejected: CloseCircleOutlined
  }
  return iconMap[type] || ClockCircleOutlined
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const exportHistory = () => {
  const csvData = priceHistory.value.map(item => ({
    时间: formatTime(item.timestamp),
    事件: item.title,
    价格: item.price || '',
    操作人: item.operator || '',
    原因: item.reason || '',
    描述: item.description || ''
  }))

  const headers = Object.keys(csvData[0])
  const csvContent = [
    headers.join(','),
    ...csvData.map(row => headers.map(header => `"${row[header]}"`).join(','))
  ].join('\n')

  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `价格历史_${props.product.sku.itemNo}_${new Date().toISOString().split('T')[0]}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  message.success('价格历史导出成功')
}

// 生命周期
onMounted(() => {
  // 组件挂载时可以加载真实的历史数据
})
</script>

<style scoped>
.price-history-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.product-summary {
  margin-bottom: var(--space-lg, 16px);
}

.product-info {
  display: flex;
  align-items: center;
  gap: var(--space-md, 12px);
}

.product-image {
  border-radius: var(--border-radius-sm, 4px);
  border: 1px solid var(--border-secondary, #f0f0f0);
}

.product-details {
  flex: 1;
}

.product-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #262626);
  margin-bottom: var(--space-xs, 4px);
  line-height: 1.4;
}

.product-meta {
  display: flex;
  gap: var(--space-md, 12px);
}

.product-meta span {
  font-size: 12px;
  color: var(--text-secondary, #595959);
}

.history-content {
  margin-bottom: var(--space-lg, 16px);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md, 12px);
}

.history-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #262626);
}

.timeline-content {
  padding-left: var(--space-sm, 8px);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs, 4px);
}

.timeline-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary, #262626);
}

.timeline-time {
  font-size: 11px;
  color: var(--text-tertiary, #8c8c8c);
}

.timeline-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs, 4px);
}

.price-info,
.operator-info,
.reason-info {
  display: flex;
  gap: var(--space-xs, 4px);
}

.price-label,
.operator-label,
.reason-label {
  font-size: 12px;
  color: var(--text-secondary, #595959);
  min-width: 50px;
}

.price-value {
  font-size: 12px;
  color: var(--ant-primary-color, #1677ff);
  font-weight: 500;
}

.operator-value,
.reason-value {
  font-size: 12px;
  color: var(--text-primary, #262626);
}

.description-info {
  font-size: 12px;
  color: var(--text-secondary, #595959);
  line-height: 1.4;
  margin-top: var(--space-xs, 4px);
}

.empty-state {
  text-align: center;
  padding: var(--space-xl, 20px);
}

.modal-actions {
  text-align: right;
  border-top: 1px solid var(--border-secondary, #f0f0f0);
  padding-top: var(--space-md, 12px);
}

/* Timeline 样式优化 */
:deep(.ant-timeline-item-content) {
  margin-left: var(--space-md, 12px);
}

:deep(.ant-timeline-item-tail) {
  border-left-color: var(--border-primary, #d9d9d9);
}
</style>
