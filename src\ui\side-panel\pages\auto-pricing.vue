<script setup lang="ts">
import { ref } from 'vue'
import { RobotOutlined, PlayCircleOutlined } from '@ant-design/icons-vue'
import AutoPricingModal from '../components/auto-pricing/AutoPricingModal.vue'

// 控制自动核价模态框显示
const showAutoPricingModal = ref(false)

// 打开自动核价
const openAutoPricing = () => {
  showAutoPricingModal.value = true
}

// 关闭自动核价
const closeAutoPricing = () => {
  showAutoPricingModal.value = false
}

// 导入h函数用于渲染图标
import { h } from 'vue'

// 功能卡片数据
const features = ref([
  {
    id: 'auto-pricing',
    title: '智能自动核价',
    description: '基于Amazon成本和利润率自动进行核价决策，支持同意申报价、重新报价、放弃上新等操作',
    icon: 'RobotOutlined',
    color: '#1890ff',
    stats: {
      total: 0,
      success: 0,
      rate: 0
    }
  },
  {
    id: 'batch-pricing',
    title: '批量核价管理',
    description: '批量处理价格待确认商品，支持筛选条件和批量操作',
    icon: 'PlayCircleOutlined',
    color: '#52c41a',
    stats: {
      total: 0,
      success: 0,
      rate: 0
    }
  }
])




</script>

<template>
  <div class="auto-pricing-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-page-header
        title="自动核价"
        sub-title="智能化价格管理工具"
        class="px-0"
      >
        <template #extra>
          <a-space>
            <a-button
              type="primary"
              size="large"
              :icon="h(RobotOutlined)"
              @click="openAutoPricing"
            >
              开始自动核价
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <!-- 功能概览 -->
    <div class="features-overview">
      <a-row :gutter="[24, 24]">
        <a-col
          v-for="feature in features"
          :key="feature.id"
          :span="12"
        >
          <a-card
            :hoverable="true"
            class="feature-card"
            @click="feature.id === 'auto-pricing' ? openAutoPricing() : null"
          >
            <template #cover>
              <div
                class="feature-cover"
                :style="{ backgroundColor: feature.color + '15' }"
              >
                <component
                  :is="feature.icon === 'RobotOutlined' ? RobotOutlined : PlayCircleOutlined"
                  :style="{ fontSize: '48px', color: feature.color }"
                />
              </div>
            </template>

            <a-card-meta
              :title="feature.title"
              :description="feature.description"
            />

            <div class="feature-stats">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-statistic
                    title="总数"
                    :value="feature.stats.total"
                    :value-style="{ fontSize: '16px' }"
                  />
                </a-col>
                <a-col :span="8">
                  <a-statistic
                    title="成功"
                    :value="feature.stats.success"
                    :value-style="{ fontSize: '16px', color: '#52c41a' }"
                  />
                </a-col>
                <a-col :span="8">
                  <a-statistic
                    title="成功率"
                    :value="feature.stats.rate"
                    suffix="%"
                    :precision="1"
                    :value-style="{ fontSize: '16px', color: '#1890ff' }"
                  />
                </a-col>
              </a-row>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <a-card
        title="使用说明"
        size="small"
      >
        <a-steps
          direction="vertical"
          size="small"
        >
          <a-step
            title="配置核价参数"
            description="设置店铺、站点、库存阈值、利润率等核价条件"
          />
          <a-step
            title="选择SKC筛选"
            description="可指定特定SKC进行核价，留空则自动获取系统相关SKU"
          />
          <a-step
            title="开始自动核价"
            description="系统将自动获取Amazon成本，根据配置进行智能决策"
          />
          <a-step
            title="查看核价结果"
            description="实时查看核价进度和结果统计，支持暂停和恢复操作"
          />
        </a-steps>
      </a-card>
    </div>

    <!-- 自动核价模态框 -->
    <AutoPricingModal
      v-model:visible="showAutoPricingModal"
    />
  </div>
</template>







<style scoped>
.auto-pricing-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.features-overview {
  margin-bottom: 24px;
}

.feature-card {
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.feature-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-stats {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.usage-guide {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.ant-page-header) {
  padding: 24px;
}

:deep(.ant-card-meta-title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.ant-card-meta-description) {
  color: #666;
  line-height: 1.5;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
  color: #999;
}
</style>
