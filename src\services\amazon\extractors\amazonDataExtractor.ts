/**
 * Amazon数据提取器
 * 统一的数据提取逻辑，支持DOM和HTML字符串两种模式
 */

import type { AmazonProductData } from '../types'

/**
 * 数据源类型：DOM对象或HTML字符串
 */
type DataSource = Document | string

/**
 * Amazon数据提取器类
 * 提供统一的数据提取方法，自动检测数据源类型
 */
export class AmazonDataExtractor {
  /**
   * 检查数据源类型
   */
  private static isDocument(source: DataSource): source is Document {
    return typeof source === 'object' && source.nodeType === Node.DOCUMENT_NODE
  }

  /**
   * 统一的查询方法
   */
  private static query(source: DataSource, selector: string): string | null {
    if (this.isDocument(source)) {
      const element = source.querySelector(selector)
      return element?.textContent?.trim() || null
    } else {
      // HTML字符串模式，使用正则表达式
      const regex = this.selectorToRegex(selector)
      const match = source.match(regex)
      return match ? match[1]?.trim() || null : null
    }
  }

  /**
   * 将CSS选择器转换为正则表达式（简化版）
   */
  private static selectorToRegex(selector: string): RegExp {
    const patterns: Record<string, RegExp> = {
      '#productTitle': /<span[^>]*id="productTitle"[^>]*>([^<]+)<\/span>/,
      '#bylineInfo': /<a[^>]*id="bylineInfo"[^>]*>([^<]+)<\/a>/,
      '.a-price .a-offscreen': /<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/,
      '[data-hook="average-star-rating"] .a-icon-alt': /<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([^<]+)<\/span>/
    }
    
    return patterns[selector] || new RegExp(`<[^>]*${selector}[^>]*>([^<]+)<\/[^>]*>`)
  }

  /**
   * 提取ASIN
   */
  static extractASIN(source: DataSource): string {
    if (this.isDocument(source)) {
      const url = source.location?.href || window.location.href
      const match = url.match(/\/dp\/([A-Z0-9]{10})/)
      return match ? match[1] : 'UNKNOWN'
    } else {
      const patterns = [
        /\/dp\/([A-Z0-9]{10})/,
        /\/gp\/product\/([A-Z0-9]{10})/,
        /"asin":"([A-Z0-9]{10})"/,
        /data-asin="([A-Z0-9]{10})"/
      ]

      for (const pattern of patterns) {
        const match = source.match(pattern)
        if (match) {
          return match[1]
        }
      }
      return 'UNKNOWN'
    }
  }

  /**
   * 提取标题
   */
  static extractTitle(source: DataSource): string {
    if (this.isDocument(source)) {
      const titleElement = source.querySelector('#productTitle')
      return titleElement?.textContent?.trim() || ''
    } else {
      const patterns = [
        /<span[^>]*id="productTitle"[^>]*>([^<]+)<\/span>/,
        /<h1[^>]*id="title"[^>]*>([^<]+)<\/h1>/,
        /<title>([^<]+)<\/title>/
      ]

      for (const pattern of patterns) {
        const match = source.match(pattern)
        if (match) {
          return match[1].trim()
        }
      }
      return ''
    }
  }

  /**
   * 提取品牌
   */
  static extractBrand(source: DataSource): string | undefined {
    if (this.isDocument(source)) {
      const brandElement = source.querySelector('#bylineInfo')
      if (brandElement) {
        const brandText = brandElement.textContent?.trim() || ''
        const brandMatch = brandText.match(/Visit the (.+?) Store/)
        return brandMatch ? brandMatch[1] : brandText
      }
      return undefined
    } else {
      const patterns = [
        /<a[^>]*id="bylineInfo"[^>]*>([^<]+)<\/a>/,
        /<span[^>]*class="[^"]*brand[^"]*"[^>]*>([^<]+)<\/span>/,
        /Visit the ([^<]+) Store/
      ]

      for (const pattern of patterns) {
        const match = source.match(pattern)
        if (match) {
          return match[1].trim()
        }
      }
      return undefined
    }
  }

  /**
   * 提取价格
   */
  static extractPrice(source: DataSource): number | undefined {
    if (this.isDocument(source)) {
      const priceElement = source.querySelector('.a-price .a-offscreen')
      if (priceElement) {
        const priceText = priceElement.textContent?.trim() || ''
        const priceMatch = priceText.replace('$', '').match(/[\d,]+\.?\d*/)
        if (priceMatch) {
          return parseFloat(priceMatch[0].replace(',', ''))
        }
      }
      return undefined
    } else {
      const patterns = [
        /<span[^>]*class="[^"]*a-price-current[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/,
        /<span[^>]*class="[^"]*a-price[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-offscreen[^"]*"[^>]*>\$?([0-9,]+\.?[0-9]*)<\/span>/
      ]

      for (const pattern of patterns) {
        const match = source.match(pattern)
        if (match) {
          const priceStr = match[1].replace(/,/g, '')
          const price = parseFloat(priceStr)
          if (!isNaN(price)) {
            return price
          }
        }
      }
      return undefined
    }
  }

  /**
   * 提取评分
   */
  static extractRating(source: DataSource): number | undefined {
    if (this.isDocument(source)) {
      const ratingElement = source.querySelector('[data-hook="average-star-rating"] .a-icon-alt')
      if (ratingElement) {
        const ratingText = ratingElement.textContent?.trim() || ''
        const ratingMatch = ratingText.match(/(\d+\.?\d*)/)
        return ratingMatch ? parseFloat(ratingMatch[1]) : undefined
      }
      return undefined
    } else {
      const patterns = [
        /<span[^>]*data-hook="average-star-rating"[^>]*>.*?<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([^<]*(\d+\.?\d*)[^<]*)<\/span>/,
        /<i[^>]*class="[^"]*a-icon-star[^"]*"[^>]*>.*?<span[^>]*class="[^"]*a-icon-alt[^"]*"[^>]*>([^<]*(\d+\.?\d*)[^<]*)<\/span>/
      ]

      for (const pattern of patterns) {
        const match = source.match(pattern)
        if (match) {
          const ratingMatch = match[1].match(/(\d+\.?\d*)/)
          return ratingMatch ? parseFloat(ratingMatch[1]) : undefined
        }
      }
      return undefined
    }
  }

  /**
   * 提取评论数
   */
  static extractReviewCount(source: DataSource): number | undefined {
    if (this.isDocument(source)) {
      const reviewElement = source.querySelector('[data-hook="total-review-count"]')
      if (reviewElement) {
        const reviewText = reviewElement.textContent?.trim() || ''
        const reviewMatch = reviewText.replace(/,/g, '').match(/(\d+)/)
        return reviewMatch ? parseInt(reviewMatch[1]) : undefined
      }
      return undefined
    } else {
      const patterns = [
        /<span[^>]*data-hook="total-review-count"[^>]*>([^<]+)<\/span>/,
        /<a[^>]*href="[^"]*#customerReviews"[^>]*>([^<]+)<\/a>/
      ]

      for (const pattern of patterns) {
        const match = source.match(pattern)
        if (match) {
          const reviewMatch = match[1].replace(/,/g, '').match(/(\d+)/)
          return reviewMatch ? parseInt(reviewMatch[1]) : undefined
        }
      }
      return undefined
    }
  }

  /**
   * 提取要点列表
   */
  static extractBulletPoints(source: DataSource): string[] {
    const bulletPoints: string[] = []

    if (this.isDocument(source)) {
      const bulletContainer = source.querySelector('#feature-bullets ul')
      if (bulletContainer) {
        const bulletItems = bulletContainer.querySelectorAll('li span')
        bulletItems.forEach(item => {
          const text = item.textContent?.trim()
          if (text && text.length > 10) {
            bulletPoints.push(text)
          }
        })
      }
    } else {
      const bulletPattern = /<div[^>]*id="feature-bullets"[^>]*>.*?<ul[^>]*>(.*?)<\/ul>/s
      const bulletMatch = source.match(bulletPattern)
      
      if (bulletMatch) {
        const listContent = bulletMatch[1]
        const itemPattern = /<li[^>]*>.*?<span[^>]*>([^<]+)<\/span>.*?<\/li>/g
        let itemMatch
        
        while ((itemMatch = itemPattern.exec(listContent)) !== null) {
          const text = itemMatch[1].trim()
          if (text && text.length > 10) {
            bulletPoints.push(text)
          }
        }
      }
    }

    return bulletPoints.slice(0, 10) // 最多10个要点
  }

  /**
   * 提取规格参数
   */
  static extractSpecifications(source: DataSource): Record<string, string> {
    const specs: Record<string, string> = {}

    if (this.isDocument(source)) {
      const specTable = source.querySelector('#productDetails_techSpec_section_1')
      if (specTable) {
        const rows = specTable.querySelectorAll('tr')
        rows.forEach(row => {
          const cells = row.querySelectorAll('td')
          if (cells.length >= 2) {
            const key = cells[0].textContent?.trim()
            const value = cells[1].textContent?.trim()
            if (key && value) {
              specs[key] = value
            }
          }
        })
      }
    } else {
      const specPattern = /<table[^>]*id="productDetails_techSpec_section_1"[^>]*>.*?<\/table>/s
      const specMatch = source.match(specPattern)
      
      if (specMatch) {
        const tableContent = specMatch[0]
        const rowPattern = /<tr[^>]*>.*?<td[^>]*>([^<]+)<\/td>.*?<td[^>]*>([^<]+)<\/td>.*?<\/tr>/g
        let rowMatch
        
        while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
          const key = rowMatch[1].trim()
          const value = rowMatch[2].trim()
          if (key && value) {
            specs[key] = value
          }
        }
      }
    }

    return specs
  }

  /**
   * 提取源URL
   */
  static extractSourceUrl(source: DataSource): string {
    if (this.isDocument(source)) {
      return source.location?.href || window.location.href
    } else {
      const canonicalMatch = source.match(/<link[^>]*rel="canonical"[^>]*href="([^"]+)"/)
      if (canonicalMatch) {
        return canonicalMatch[1]
      }
      return 'https://www.amazon.com'
    }
  }

  /**
   * 提取完整的Amazon商品数据
   */
  static extractFullProductData(source: DataSource): AmazonProductData {
    return {
      asin: this.extractASIN(source),
      title: this.extractTitle(source),
      brand: this.extractBrand(source),
      price: this.extractPrice(source),
      currency: 'USD',
      rating: this.extractRating(source),
      reviewCount: this.extractReviewCount(source),
      mainImageUrl: undefined, // 需要单独处理图片
      imageUrls: [], // 需要单独处理图片
      bulletPoints: this.extractBulletPoints(source),
      description: this.extractBulletPoints(source).join(' '),
      categoryPath: '',
      stockStatus: 'In Stock',
      specifications: this.extractSpecifications(source),
      variations: [],
      sourceUrl: this.extractSourceUrl(source)
    }
  }
}

export default AmazonDataExtractor
