/**
 * Amazon服务相关类型定义
 * 统一所有Amazon相关的接口和类型
 */

// ==================== 基础数据类型 ====================

/**
 * Amazon商品数据接口
 */
export interface AmazonProductData {
  asin: string
  title: string
  brand?: string
  price?: number
  currency?: string
  rating?: number
  reviewCount?: number
  mainImageUrl?: string
  imageUrls: string[]
  bulletPoints: string[]
  description?: string
  categoryPath?: string
  stockStatus?: string
  specifications?: Record<string, string>
  variations?: AmazonVariation[]
  sourceUrl: string
}

/**
 * Amazon商品变体信息
 */
export interface AmazonVariation {
  asin: string
  title: string
  price?: number
  imageUrl?: string
  attributes: Record<string, string>
}

/**
 * Amazon商品价格信息
 */
export interface AmazonPriceInfo {
  e: string // extCode
  p: number // 价格（美元）
  s: number // 库存
  img: string // 图片URL
}

/**
 * Amazon价格数据
 */
export interface AmazonPriceData {
  extCode: string
  price: number
  stock: number
  imageUrl: string
  lastUpdated: string
}

// ==================== 店小秘数据类型 ====================

/**
 * 店小秘商品数据接口
 */
export interface DianxiaomiProductData {
  // 基础信息
  attributes: string
  categoryId: string
  shopId: string
  productSemiManagedReq: string
  sourceUrl: string
  productName: string
  productNameI18n: string
  outerGoodsUrl: string
  materialImgUrl: string
  productOrigin: string
  region2Id: string
  originFileUrl: string
  sensitiveAttr: string
  personalizationSwitch: number
  mainImage: string
  optionValue: string
  mainProductSkuSpecReqs: string
  goodsModel: string
  variationListStr: string
  productWarehouseRouteReq: string
  dxmPdfUrl: string
  qualifiedEn: string
  instructionsId: string
  instructionsName: string
  description: string
  instructionsTranslateId: string
  freightTemplateId: string
  shipmentLimitSecond: number
  op: number
  categoryType: number
  productId: string
  sizeTemplateIds: string

  // 扩展字段（用于兼容不同格式）
  title?: string
  price?: number
  images?: string[]
  category?: string
  brand?: string
  specifications?: Record<string, string>
  asin?: string
  rating?: number
  reviewCount?: number
  stockStatus?: string
  currency?: string
}

// ==================== 服务配置类型 ====================

/**
 * Amazon服务配置
 */
export interface AmazonServiceConfig {
  // 数据采集配置
  collection: {
    enableImageProcessing: boolean
    enableAutoPush: boolean
    maxImages: number
    imageQuality: number
  }
  // 数据处理配置
  processing: {
    titlePrefix?: string
    titleSuffix?: string
    priceMultiplier: number
    enablePriceSync: boolean
  }
  // 推送配置
  push: {
    autoPush: boolean
    batchSize: number
    delayBetweenPush: number
  }
}

/**
 * 采集配置
 */
export interface CollectionConfig {
  enableImageProcessing: boolean
  enableAutoPush: boolean
  maxConcurrentRequests: number
  retryAttempts: number
  imageProcessingOptions: {
    maxImages: number
    targetSize: number
    quality: number
  }
  pushOptions: {
    batchSize: number
    delayBetweenPush: number
  }
}

// ==================== 结果类型 ====================

/**
 * Amazon服务处理结果
 */
export interface AmazonServiceResult {
  success: boolean
  data?: {
    amazonData: AmazonProductData
    dianxiaomiData: DianxiaomiProductData
    processedImages?: string[]
  }
  error?: string
  warnings?: string[]
  metadata: {
    processingTime: number
    imageProcessingTime?: number
    pushTime?: number
  }
}

/**
 * 采集结果
 */
export interface CollectionResult {
  success: boolean
  productData?: {
    spuData: any
    skuDataList: any[]
    dianxiaomiData: any
    processedImages?: string[]
  }
  pushResult?: any
  error?: string
  warnings?: string[]
  metadata: {
    collectionTime: number
    imageProcessingTime?: number
    pushTime?: number
    totalTime: number
  }
}

/**
 * 批量处理结果
 */
export interface BatchProcessResult {
  success: boolean
  results: AmazonServiceResult[]
  summary: {
    total: number
    successful: number
    failed: number
  }
  totalTime: number
}

/**
 * 批量采集结果
 */
export interface BatchCollectionResult {
  success: boolean
  results: CollectionResult[]
  summary: {
    total: number
    successful: number
    failed: number
    warnings: number
  }
  metadata: {
    startTime: string
    endTime: string
    totalTime: number
  }
}

// ==================== 工具类型 ====================

/**
 * 数据源类型：DOM对象或HTML字符串
 */
export type DataSource = Document | string

/**
 * 图片处理结果
 */
export interface ImageProcessingResult {
  success: boolean
  originalUrl: string
  processedUrl?: string
  error?: string
  size?: {
    width: number
    height: number
    fileSize: number
  }
}

/**
 * 推送结果
 */
export interface PushResult {
  success: boolean
  data?: any
  error?: string
  metadata: {
    pushTime: number
    retryCount: number
  }
}

// ==================== 导出所有类型 ====================

export type {
  // 基础数据类型
  AmazonProductData,
  AmazonVariation,
  AmazonPriceInfo,
  AmazonPriceData,
  DianxiaomiProductData,
  
  // 配置类型
  AmazonServiceConfig,
  CollectionConfig,
  
  // 结果类型
  AmazonServiceResult,
  CollectionResult,
  BatchProcessResult,
  BatchCollectionResult,
  
  // 工具类型
  DataSource,
  ImageProcessingResult,
  PushResult
}
