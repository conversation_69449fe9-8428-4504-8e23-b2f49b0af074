<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { getSiteOptions } from '../../../../config/temuSites'

// 自动同步库存表单
const syncStockForm = ref({
  shop: '',
  site: '',
  onSale: '否',
  shopStockThreshold: '',
  skuFilter: '',
  skcFilter: '',
  warehouseStockThreshold: '',
  profitMargin: '',
  syncStockValue: 0,
  syncZeroStock: 1, // 1: 同步, 2: 不同步
  useLocalData: 1, // 1: 是, 2: 否
  stockSyncType: 1 // 1: 货盘库存, 2: 固定库存
})

// 店铺选项
const shopOptions = [
  {
    value: 'ZenithCai',
    label: 'ZenithCai',
    avatar: 'https://img.cdnfe.com/supplier-public-tag/201365d418b/060d9c4f-71ff-4da0-8b98-c6c8a8221896_300x300.jpeg'
  }
]

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.label,
  label: `${site.label}站`
})))

// 是否在售选项
const onSaleOptions = [
  { value: '是', label: '是' },
  { value: '否', label: '否' }
]

// 开始自动同步库存
const startSyncStock = () => {
  console.log('开始自动同步库存:', syncStockForm.value)
  alert('自动同步库存功能已启动！')
}
</script>

<template>
  <div>
    <a-form
      :model="syncStockForm"
      layout="vertical"
      class="space-y-6"
      @finish="startSyncStock"
    >
      <!-- 基础配置 -->
      <a-card
        title="基础配置"
        class="mb-6"
      >
        <a-row :gutter="16">
          <!-- 店铺选择 -->
          <a-col :span="8">
            <a-form-item label="店铺">
              <a-select
                v-model:value="syncStockForm.shop"
                placeholder="选择店铺"
              >
                <a-select-option
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  :value="shop.value"
                >
                  <div class="flex items-center space-x-2">
                    <img
                      :src="shop.avatar"
                      alt="店铺头像"
                      class="w-4 h-4 rounded"
                    />
                    <span>{{ shop.label }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 站点选择 -->
          <a-col :span="8">
            <a-form-item label="站点">
              <a-select
                v-model:value="syncStockForm.site"
                placeholder="选择站点"
              >
                <a-select-option
                  v-for="site in siteOptions"
                  :key="site.value"
                  :value="site.value"
                >
                  {{ site.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 是否在售 -->
          <a-col :span="8">
            <a-form-item label="是否在售">
              <a-select
                v-model:value="syncStockForm.onSale"
                placeholder="选择在售状态"
              >
                <a-select-option
                  v-for="option in onSaleOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 库存阈值 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="店铺库存阈值">
              <a-input-number
                v-model:value="syncStockForm.shopStockThreshold"
                placeholder="输入店铺库存阈值"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="货盘库存阈值">
              <a-input-number
                v-model:value="syncStockForm.warehouseStockThreshold"
                placeholder="输入货盘库存阈值"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="毛利率(%)">
              <a-input-number
                v-model:value="syncStockForm.profitMargin"
                placeholder="输入毛利率"
                class="w-full"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 筛选条件 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="SKU筛选">
              <a-input
                v-model:value="syncStockForm.skuFilter"
                placeholder="多个查询请英文逗号、空格依次输入(选填)"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="SKC筛选">
              <a-input
                v-model:value="syncStockForm.skcFilter"
                placeholder="多个查询请英文逗号、空格依次输入(选填)"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 同步设置 -->
      <a-card
        title="同步设置"
        class="mb-6"
      >
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="同步库存值">
              <a-input-number
                v-model:value="syncStockForm.syncStockValue"
                :min="0"
                class="w-full"
              />
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="同步零库存">
              <a-radio-group v-model:value="syncStockForm.syncZeroStock">
                <a-radio :value="1">同步</a-radio>
                <a-radio :value="2">不同步</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>

          <a-col :span="8">
            <a-form-item label="使用本地数据">
              <a-radio-group v-model:value="syncStockForm.useLocalData">
                <a-radio :value="1">是</a-radio>
                <a-radio :value="2">否</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="库存同步类型">
          <a-radio-group v-model:value="syncStockForm.stockSyncType">
            <a-radio :value="1">货盘库存</a-radio>
            <a-radio :value="2">固定库存</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-card>

      <!-- 同步说明 -->
      <a-card
        title="同步说明"
        class="mb-6"
      >
        <a-alert
          type="info"
          show-icon
          message="库存同步规则"
          description="系统将根据设置的条件自动同步店铺和货盘之间的库存数据，确保库存信息的准确性。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div>• 货盘库存：从货盘系统获取实时库存数据</div>
          <div>• 固定库存：使用设置的固定库存值</div>
          <div>• 零库存同步：可选择是否同步库存为0的商品</div>
        </div>
      </a-card>

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <a-button 
          type="primary"
          html-type="submit"
          size="large"
        >
          <template #icon>
            <PlayCircleOutlined />
          </template>
          开始同步库存
        </a-button>
      </div>
    </a-form>
  </div>
</template>
