/**
 * 店小秘API处理器 (DianXiaoMi API Handler)
 *
 * 文件功能：专门负责处理所有与店小秘平台相关的API调用和数据处理
 *
 * 主要职责：
 * 1. 登录状态检测 - 通过API检测用户是否已登录店小秘
 * 2. 店铺管理 - 获取店铺账号列表和店铺信息
 * 3. 仓库管理 - 获取发货仓库列表和仓库配置
 * 4. 运费模板 - 获取和管理运费模板
 * 5. 商品分类 - 获取店小秘和Temu的商品分类数据
 * 6. 消息路由 - 作为店小秘相关消息的统一处理器
 *
 * 运行环境：ISOLATED world (Content Script)
 * 适用页面：所有店小秘域名下的页面
 *
 * 与 dianxiaomi-ui-injector.ts 的区别：
 * - 本文件：专注于API调用和数据处理，不涉及DOM操作
 * - ui-injector：专注于页面UI注入和用户交互
 *
 * 注意：此文件作为通用的API处理器，响应来自扩展各个组件的请求
 */

console.info('[DianxiaomiAPIHandler] Content script loaded on:', window.location.href)

// API调用处理器
class DianxiaomiAPIHandler {
  constructor() {
    this.init()
  }

  init() {
    console.info('[DianxiaomiAPIHandler] 初始化API处理器')
    
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.info('[DianxiaomiAPIHandler] 收到消息:', request)
      
      if (request.action === 'CALL_DIANXIAOMI_API') {
        this.handleAPICall(request.apiConfig)
          .then(result => {
            console.info('[DianxiaomiAPIHandler] API调用结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] API调用失败:', error)
            sendResponse({
              success: false,
              error: error.message || 'API调用失败'
            })
          })
        
        // 返回true表示异步响应
        return true
      }
      
      if (request.action === 'UPLOAD_DIANXIAOMI_PRODUCT') {
        // 处理店小秘商品上传请求
        this.uploadDianxiaomiProduct(request.jsonData)
          .then(result => {
            sendResponse(result)
          })
          .catch(error => {
            sendResponse({
              success: false,
              error: error.message || '上传失败'
            })
          })

        return true
      }

      if (request.action === 'GET_TEMU_INFO') {
        // 处理Temu信息获取（保持兼容性）
        this.getTemuInfo()
          .then(result => {
            sendResponse(result)
          })
          .catch(error => {
            sendResponse({
              success: false,
              error: error.message || '获取Temu信息失败'
            })
          })

        return true
      }



      // 新增：获取店铺账号列表
      if (request.action === 'GET_SHOP_ACCOUNTS') {
        console.info('[DianxiaomiAPIHandler] 开始获取店铺账号列表...')
        this.getShopAccountsFromPage()
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取店铺账号结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取店铺账号失败:', error)
            sendResponse({
              success: false,
              error: error.message || '获取店铺账号失败'
            })
          })
        return true
      }

      // 新增：获取发货仓库列表
      if (request.action === 'GET_WAREHOUSES') {
        console.info('[DianxiaomiAPIHandler] 开始获取发货仓库列表...')
        this.getWarehousesFromPage()
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取仓库列表结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取仓库列表失败:', error)
            sendResponse({
              success: false,
              error: error.message || '获取仓库列表失败'
            })
          })
        return true
      }

      // 新增：获取运费模板列表
      if (request.action === 'GET_FREIGHT_TEMPLATES') {
        console.info('[DianxiaomiAPIHandler] 开始获取运费模板列表...')
        this.getFreightTemplatesFromPage()
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取运费模板结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取运费模板失败:', error)
            sendResponse({
              success: false,
              error: error.message || '获取运费模板失败'
            })
          })
        return true
      }

      // 新增：获取商品分类列表
      if (request.action === 'GET_PRODUCT_CATEGORIES') {
        console.info('[DianxiaomiAPIHandler] 开始获取商品分类列表...')
        this.getProductCategoriesFromPage(request.categoryParentId)
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取商品分类结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取商品分类失败:', error)
            sendResponse({
              success: false,
              error: error.message || '获取商品分类失败'
            })
          })
        return true
      }

      // 新增：获取Temu商品分类列表
      if (request.action === 'GET_TEMU_CATEGORIES') {
        console.info('[DianxiaomiAPIHandler] 开始获取Temu商品分类列表...')
        this.getTemuCategoriesFromPage(request.shopId, request.categoryParentId)
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取Temu商品分类结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取Temu商品分类失败:', error)
            sendResponse({
              success: false,
              error: error.message || '获取Temu商品分类失败'
            })
          })
        return true
      }

      return false
    })
  }

  // 处理API调用
  async handleAPICall(apiConfig: {
    url: string
    method?: string
    data?: any
    headers?: Record<string, string>
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 处理API调用:', apiConfig.url)

      const defaultHeaders = {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }

      const headers = { ...defaultHeaders, ...apiConfig.headers }
      const method = apiConfig.method || 'GET'

      const fetchOptions: RequestInit = {
        method,
        credentials: 'include',
        headers
      }

      if (method !== 'GET' && apiConfig.data) {
        fetchOptions.body = JSON.stringify(apiConfig.data)
      }

      console.info('[DianxiaomiAPIHandler] 发送请求:', {
        url: apiConfig.url,
        method,
        headers,
        hasBody: !!fetchOptions.body
      })

      const response = await fetch(apiConfig.url, fetchOptions)
      
      console.info('[DianxiaomiAPIHandler] 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentType = response.headers.get('content-type')
      let data: any

      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text()
      }

      console.info('[DianxiaomiAPIHandler] 响应数据:', data)

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] API调用失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API调用失败'
      }
    }
  }

  // 上传店小秘商品 - 借鉴test_upload.html的完整逻辑
  async uploadDianxiaomiProduct(jsonData: string): Promise<{ success: boolean; data?: any; error?: string; status?: number; headers?: any }> {
    try {
      console.info('[DianxiaomiAPIHandler] 开始上传店小秘商品...')
      console.info('[DianxiaomiAPIHandler] JSON数据大小:', jsonData.length, 'bytes')

      // 检查JSZip是否可用
      const JSZip = (window as any).JSZip
      if (!JSZip) {
        throw new Error('JSZip库不可用，请确保页面已加载JSZip')
      }

      // 创建ZIP文件
      console.info('[DianxiaomiAPIHandler] 创建ZIP文件...')
      const zip = new JSZip()
      zip.file('choiceSave.txt', jsonData)

      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6
        }
      })

      console.info('[DianxiaomiAPIHandler] ZIP文件创建成功，大小:', zipBlob.size, 'bytes')

      // 创建FormData - 完全按照test_upload.html的方式
      const formData = new FormData()
      formData.append('file', zipBlob, 'blob')
      formData.append('op', '1')

      console.info('[DianxiaomiAPIHandler] 发送上传请求...')

      // 发送请求 - 完全按照test_upload.html的方式
      const response = await fetch('https://www.dianxiaomi.com/api/popTemuProduct/add.json', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Origin': 'https://www.dianxiaomi.com',
          'Referer': 'https://www.dianxiaomi.com/web/popTemu/edit',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[DianxiaomiAPIHandler] 响应状态:', response.status, response.statusText)

      const responseText = await response.text()
      console.info('[DianxiaomiAPIHandler] 响应内容:', responseText)

      // 收集响应头
      const headers: Record<string, string> = {}
      for (const [key, value] of response.headers.entries()) {
        headers[key] = value
      }

      let responseData: any = responseText
      try {
        responseData = JSON.parse(responseText)
      } catch (e) {
        // 如果不是JSON，保持原文本
      }

      return {
        success: response.ok,
        data: responseData,
        status: response.status,
        headers,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 上传失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败'
      }
    }
  }

  // 获取Temu信息（保持兼容性）
  async getTemuInfo(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 获取Temu信息...')

      // 这里可以添加Temu特定的信息获取逻辑
      // 目前返回基本信息
      return {
        success: true,
        data: {
          url: window.location.href,
          title: document.title,
          timestamp: Date.now()
        }
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 获取Temu信息失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取Temu信息失败'
      }
    }
  }

  // 获取页面中的anti-content头部（用于某些API）
  getAntiContent(): string | null {
    try {
      // 方法1: 从全局变量获取
      const win = window as any
      if (win.antiContent) return win.antiContent
      if (win.__INITIAL_STATE__?.antiContent) return win.__INITIAL_STATE__.antiContent

      // 方法2: 从脚本中搜索
      const scripts = Array.from(document.querySelectorAll('script'))
      for (const script of scripts) {
        const content = script.textContent || script.innerHTML
        const patterns = [
          /["']anti-content["']\s*:\s*["']([^"']+)["']/i,
          /antiContent\s*:\s*["']([^"']+)["']/i,
          /"anti-content":\s*"([^"]+)"/i
        ]

        for (const pattern of patterns) {
          const match = content.match(pattern)
          if (match) return match[1]
        }
      }
      return null
    } catch (error) {
      console.warn('[DianxiaomiAPIHandler] 获取anti-content失败:', error)
      return null
    }
  }

  // 获取页面中的CSRF token
  getCSRFToken(): string | null {
    try {
      // 从meta标签获取
      const metaToken = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement
      if (metaToken) return metaToken.content

      // 从表单中获取
      const hiddenToken = document.querySelector('input[name="_token"]') as HTMLInputElement
      if (hiddenToken) return hiddenToken.value

      // 从全局变量获取
      const win = window as any
      if (win.csrfToken) return win.csrfToken
      if (win._token) return win._token

      return null
    } catch (error) {
      console.warn('[DianxiaomiAPIHandler] 获取CSRF token失败:', error)
      return null
    }
  }

  // 检查是否在店小秘域名下
  isDianxiaomiDomain(): boolean {
    return window.location.hostname.includes('dianxiaomi.com')
  }

  // 获取当前用户信息（从页面中提取）
  getCurrentUserInfo(): any {
    try {
      const win = window as any
      
      // 尝试从全局变量获取用户信息
      if (win.userInfo) return win.userInfo
      if (win.__INITIAL_STATE__?.userInfo) return win.__INITIAL_STATE__.userInfo
      if (win.USER_INFO) return win.USER_INFO

      // 尝试从localStorage获取
      const storedUserInfo = localStorage.getItem('userInfo') || localStorage.getItem('user_info')
      if (storedUserInfo) {
        return JSON.parse(storedUserInfo)
      }

      return null
    } catch (error) {
      console.warn('[DianxiaomiAPIHandler] 获取用户信息失败:', error)
      return null
    }
  }

  // 获取页面状态信息
  getPageStatus(): {
    isLoggedIn: boolean
    userInfo: any
    antiContent: string | null
    csrfToken: string | null
    isDianxiaomiDomain: boolean
  } {
    return {
      isLoggedIn: !!this.getCurrentUserInfo(),
      userInfo: this.getCurrentUserInfo(),
      antiContent: this.getAntiContent(),
      csrfToken: this.getCSRFToken(),
      isDianxiaomiDomain: this.isDianxiaomiDomain()
    }
  }



  // 获取店铺账号列表
  async getShopAccountsFromPage(): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 开始获取店铺账号数据...')

      // 使用API方式获取店铺列表
      console.info('[DianxiaomiAPIHandler] 使用API方式获取店铺列表...')

      const response = await fetch('https://www.dianxiaomi.com/shop/list/pddkj.htm', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'text/html, */*; q=0.01',
          'X-Requested-With': 'XMLHttpRequest',
          'User-Agent': navigator.userAgent
        }
      })

      console.info('[DianxiaomiAPIHandler] 店铺列表API响应状态:', response.status)

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          return { success: false, error: '未登录店小秘，请先登录' }
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const htmlText = await response.text()
      console.info('[DianxiaomiAPIHandler] 店铺列表API响应长度:', htmlText.length)

      // 检查是否是登录页面
      if (htmlText.includes('login') && htmlText.includes('password')) {
        return { success: false, error: '检测到登录页面，请先登录店小秘' }
      }

      // 创建临时DOM来解析HTML
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlText, 'text/html')

      // 解析店铺信息
      const shopAccounts: any[] = []

      // 尝试多种选择器
      const selectors = [
        'tr.content[data-id]',
        'tr[data-id]',
        '[data-id][data-shopName]',
        '.shop-row[data-id]'
      ]

      let shopRows: NodeListOf<Element> | null = null

      for (const selector of selectors) {
        shopRows = doc.querySelectorAll(selector)
        console.info(`[DianxiaomiAPIHandler] 使用选择器 "${selector}" 找到行数:`, shopRows.length)
        if (shopRows.length > 0) {
          break
        }
      }

      if (!shopRows || shopRows.length === 0) {
        // 尝试查找包含店铺ID的所有元素
        const allElementsWithDataId = doc.querySelectorAll('[data-id]')
        console.info('[DianxiaomiAPIHandler] 所有包含data-id的元素数量:', allElementsWithDataId.length)

        allElementsWithDataId.forEach((element, index) => {
          console.info(`[DianxiaomiAPIHandler] 元素 ${index + 1}:`, {
            tagName: element.tagName,
            className: element.className,
            dataId: element.getAttribute('data-id'),
            dataShopName: element.getAttribute('data-shopName'),
            innerHTML: element.innerHTML.substring(0, 100) + '...'
          })
        })

        return {
          success: false,
          error: `未找到店铺数据。页面内容长度: ${htmlText.length}，包含data-id的元素: ${allElementsWithDataId.length}`
        }
      }

      shopRows.forEach((row, index) => {
        try {
          const shopId = row.getAttribute('data-id') || ''
          const shopName = row.getAttribute('data-shopName') || ''

          console.info(`[DianxiaomiAPIHandler] 解析店铺 ${index + 1}:`, {
            shopId,
            shopName,
            tagName: row.tagName,
            className: row.className
          })

          // 获取币种
          const currencyElement = row.querySelector('.pddKJCurrency') ||
                                 row.querySelector('[class*="currency"]') ||
                                 row.querySelector('[class*="Currency"]')
          const currency = currencyElement?.textContent?.trim() || 'CNY'

          // 获取授权时间和过期时间
          const timeElements = row.querySelectorAll('td p span, span, p')
          let authTime = ''
          let expireTime = ''

          timeElements.forEach(span => {
            const text = span.textContent || ''
            if (text.includes('授权时间：')) {
              authTime = text.replace('授权时间：', '').trim()
            } else if (text.includes('过期时间：')) {
              expireTime = text.replace('过期时间：', '').trim()
            }
          })

          console.info(`[DianxiaomiAPIHandler] 店铺详细信息:`, {
            shopId,
            shopName,
            currency,
            authTime,
            expireTime
          })

          if (shopId && shopName) {
            shopAccounts.push({
              shopId,
              shopName,
              currency,
              authTime,
              expireTime
            })
          } else {
            console.warn(`[DianxiaomiAPIHandler] 店铺 ${index + 1} 缺少必要信息:`, { shopId, shopName })
          }
        } catch (error) {
          console.error(`[DianxiaomiAPIHandler] 解析店铺 ${index + 1} 失败:`, error)
        }
      })

      console.info('[DianxiaomiAPIHandler] 解析完成，共找到有效店铺:', shopAccounts.length)

      if (shopAccounts.length === 0) {
        // 输出页面内容的一部分用于调试
        const pagePreview = htmlText.substring(0, 1000)
        console.info('[DianxiaomiAPIHandler] 页面内容预览:', pagePreview)

        return {
          success: false,
          error: `解析到 ${shopRows?.length || 0} 行数据，但没有有效的店铺信息。请检查页面是否正确加载。`
        }
      }

      return {
        success: true,
        data: shopAccounts
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 获取店铺账号失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取店铺信息失败'
      }
    }
  }

  // 获取发货仓库列表
  async getWarehousesFromPage(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 开始获取发货仓库列表...')

      // 首先获取店铺信息
      const shopAccounts = await this.getShopAccountsFromPage()
      let shopId = ''

      if (shopAccounts.success && shopAccounts.data && shopAccounts.data.length > 0) {
        shopId = shopAccounts.data[0].shopId
        console.info('[DianxiaomiAPIHandler] 使用店铺ID:', shopId)
      } else {
        return {
          success: false,
          error: '无法获取店铺ID，请先同步店铺账号'
        }
      }

      // 构建POST请求参数
      const formData = new URLSearchParams()
      formData.append('shopId', shopId)
      formData.append('siteIdStr', '100')

      const response = await fetch('https://www.dianxiaomi.com/popTemuCategory/warehouseList.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData.toString()
      })

      console.info('[DianxiaomiAPIHandler] 仓库API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[DianxiaomiAPIHandler] 仓库API响应数据:', data)

      if (data.code === 0) {
        return {
          success: true,
          data: data.data
        }
      } else {
        return {
          success: false,
          error: data.msg || '获取仓库列表失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 获取仓库列表失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  // 获取运费模板列表
  async getFreightTemplatesFromPage(): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 开始获取运费模板列表...')

      // 首先获取店铺信息
      const shopAccounts = await this.getShopAccountsFromPage()
      let shopId = ''

      if (shopAccounts.success && shopAccounts.data && shopAccounts.data.length > 0) {
        shopId = shopAccounts.data[0].shopId
        console.info('[DianxiaomiAPIHandler] 使用店铺ID:', shopId)
      } else {
        return {
          success: false,
          error: '无法获取店铺ID，请先同步店铺账号'
        }
      }

      // 构建POST请求参数
      const formData = new URLSearchParams()
      formData.append('shopId', shopId)
      formData.append('siteIdStr', '100')

      const response = await fetch('https://www.dianxiaomi.com/popTemuCategory/syncTemuShipments.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData.toString()
      })

      console.info('[DianxiaomiAPIHandler] 运费模板API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[DianxiaomiAPIHandler] 运费模板API响应数据:', data)

      if (data.code === 0) {
        return {
          success: true,
          data: data.data
        }
      } else {
        return {
          success: false,
          error: data.msg || '获取运费模板失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 获取运费模板失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  // 获取商品分类列表
  async getProductCategoriesFromPage(categoryParentId?: number): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 开始获取商品分类列表...', { categoryParentId })

      // 首先获取店铺信息
      const shopAccounts = await this.getShopAccountsFromPage()
      let shopId = ''

      if (shopAccounts.success && shopAccounts.data && shopAccounts.data.length > 0) {
        shopId = shopAccounts.data[0].shopId
        console.info('[DianxiaomiAPIHandler] 使用店铺ID:', shopId)
      } else {
        return {
          success: false,
          error: '无法获取店铺ID，请先同步店铺账号'
        }
      }

      // 构建POST请求参数
      const formData = new URLSearchParams()
      formData.append('shopId', shopId)

      // 如果提供了父分类ID，则获取子分类；否则获取根分类
      if (categoryParentId !== undefined) {
        formData.append('categoryParentId', categoryParentId.toString())
      } else {
        formData.append('categoryParentId', '1') // 默认获取根分类
      }

      const response = await fetch('https://www.dianxiaomi.com/api/popTemuCategory/list.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData.toString()
      })

      console.info('[DianxiaomiAPIHandler] 商品分类API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[DianxiaomiAPIHandler] 商品分类API响应数据:', data)

      if (data.code === 0) {
        return {
          success: true,
          data: data.data
        }
      } else {
        return {
          success: false,
          error: data.msg || '获取商品分类失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 获取商品分类失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  // 获取指定店铺的Temu商品分类列表
  async getTemuCategoriesFromPage(shopId: string, categoryParentId?: number): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      console.info('[DianxiaomiAPIHandler] 开始获取Temu商品分类列表...', { shopId, categoryParentId })

      // 构建POST请求参数
      const formData = new URLSearchParams()
      formData.append('shopId', shopId)

      // 如果提供了父分类ID，则获取子分类；否则获取根分类（默认为0）
      if (categoryParentId !== undefined) {
        formData.append('categoryParentId', categoryParentId.toString())
      } else {
        formData.append('categoryParentId', '0')
      }

      const response = await fetch('https://www.dianxiaomi.com/api/popTemuCategory/list.json', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData.toString()
      })

      console.info('[DianxiaomiAPIHandler] Temu商品分类API响应状态:', response.status)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.info('[DianxiaomiAPIHandler] Temu商品分类API响应数据:', data)

      if (data.code === 0) {
        return {
          success: true,
          data: data.data
        }
      } else {
        return {
          success: false,
          error: data.msg || '获取Temu商品分类失败'
        }
      }
    } catch (error) {
      console.error('[DianxiaomiAPIHandler] 获取Temu商品分类失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '请求失败'
      }
    }
  }

  // 消息处理器 - 供主路由器调用
  handleMessage(request: any, sender: any, sendResponse: (response: any) => void): boolean {
    console.info('[DianxiaomiAPIHandler] 处理消息:', request.action)

    switch (request.action) {


      case 'GET_SHOP_ACCOUNTS':
        this.getShopAccountsFromPage()
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取店铺账号结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取店铺账号失败:', error)
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '获取店铺账号失败'
            })
          })
        return true

      case 'GET_WAREHOUSES':
        this.getWarehousesFromPage()
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取仓库列表结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取仓库列表失败:', error)
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '获取仓库列表失败'
            })
          })
        return true

      case 'GET_FREIGHT_TEMPLATES':
        this.getFreightTemplatesFromPage()
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取运费模板结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取运费模板失败:', error)
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '获取运费模板失败'
            })
          })
        return true

      case 'GET_PRODUCT_CATEGORIES':
        this.getProductCategoriesFromPage(request.categoryParentId)
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取商品分类结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取商品分类失败:', error)
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '获取商品分类失败'
            })
          })
        return true

      case 'GET_TEMU_CATEGORIES':
        this.getTemuCategoriesFromPage(request.shopId, request.categoryParentId)
          .then(result => {
            console.info('[DianxiaomiAPIHandler] 获取Temu商品分类结果:', result)
            sendResponse(result)
          })
          .catch(error => {
            console.error('[DianxiaomiAPIHandler] 获取Temu商品分类失败:', error)
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '获取Temu商品分类失败'
            })
          })
        return true

      default:
        console.warn('[DianxiaomiAPIHandler] 未知消息:', request.action)
        sendResponse({ success: false, error: '未知的action: ' + request.action })
        return true
    }
  }
}

// 初始化并暴露到全局
const dianxiaomiHandler = new DianxiaomiAPIHandler()

// 扩展全局接口
declare global {
  interface Window {
    dianxiaomiHandler: DianxiaomiAPIHandler
  }
}

// 暴露到全局
window.dianxiaomiHandler = dianxiaomiHandler

console.info('[DianxiaomiAPIHandler] ✅ 店小秘API处理器已初始化并暴露到全局')

// 只在店小秘域名下初始化
if (window.location.hostname.includes('dianxiaomi.com')) {
  console.info('[DianxiaomiAPIHandler] 在店小秘域名下，初始化API处理器')
  new DianxiaomiAPIHandler()
} else {
  console.info('[DianxiaomiAPIHandler] 不在店小秘域名下，跳过初始化')
}

export {}
