[{"/home/<USER>/temu-dmj-extension/define.config.mjs": "1", "/home/<USER>/temu-dmj-extension/manifest.chrome.config.ts": "2", "/home/<USER>/temu-dmj-extension/manifest.config.ts": "3", "/home/<USER>/temu-dmj-extension/manifest.firefox.config.ts": "4", "/home/<USER>/temu-dmj-extension/scripts/getInstalledBrowsers.ts": "5", "/home/<USER>/temu-dmj-extension/scripts/launch.ts": "6", "/home/<USER>/temu-dmj-extension/src/background/index.ts": "7", "/home/<USER>/temu-dmj-extension/src/components/AmazonDataPreviewModal.vue": "8", "/home/<USER>/temu-dmj-extension/src/components/AppFooter.vue": "9", "/home/<USER>/temu-dmj-extension/src/components/AppHeader.vue": "10", "/home/<USER>/temu-dmj-extension/src/components/AutoPricingModal.vue": "11", "/home/<USER>/temu-dmj-extension/src/components/LoginModal.vue": "12", "/home/<USER>/temu-dmj-extension/src/components/NotificationToast.vue": "13", "/home/<USER>/temu-dmj-extension/src/components/RouterLinkUp.vue": "14", "/home/<USER>/temu-dmj-extension/src/components/TemuNotFound.vue": "15", "/home/<USER>/temu-dmj-extension/src/components/ThemeSwitch.vue": "16", "/home/<USER>/temu-dmj-extension/src/components/TopNavbar.vue": "17", "/home/<USER>/temu-dmj-extension/src/components/state/DisplayError.vue": "18", "/home/<USER>/temu-dmj-extension/src/components/state/LoadingSpinner.vue": "19", "/home/<USER>/temu-dmj-extension/src/components/state/tailwind-empty-state.vue": "20", "/home/<USER>/temu-dmj-extension/src/composables/useAppRouter.ts": "21", "/home/<USER>/temu-dmj-extension/src/composables/useAuth.ts": "22", "/home/<USER>/temu-dmj-extension/src/composables/useAutoPricing.ts": "23", "/home/<USER>/temu-dmj-extension/src/composables/useBrowserStorage.ts": "24", "/home/<USER>/temu-dmj-extension/src/composables/useDashboard.ts": "25", "/home/<USER>/temu-dmj-extension/src/composables/useNotification.ts": "26", "/home/<USER>/temu-dmj-extension/src/composables/useShopBinding.ts": "27", "/home/<USER>/temu-dmj-extension/src/composables/useTheme.ts": "28", "/home/<USER>/temu-dmj-extension/src/config/index.ts": "29", "/home/<USER>/temu-dmj-extension/src/config/temu-config.ts": "30", "/home/<USER>/temu-dmj-extension/src/config/temuSites.ts": "31", "/home/<USER>/temu-dmj-extension/src/content-scripts/amazon/amazon-collector.ts": "32", "/home/<USER>/temu-dmj-extension/src/content-scripts/dxm/dianxiaomi-api-handler.ts": "33", "/home/<USER>/temu-dmj-extension/src/content-scripts/dxm/dianxiaomi-config-injector.ts": "34", "/home/<USER>/temu-dmj-extension/src/content-scripts/index.ts": "35", "/home/<USER>/temu-dmj-extension/src/content-scripts/temu/temu-unified.ts": "36", "/home/<USER>/temu-dmj-extension/src/lib/utils.ts": "37", "/home/<USER>/temu-dmj-extension/src/offscreen/index.ts": "38", "/home/<USER>/temu-dmj-extension/src/services/__tests__/productListService.test.ts": "39", "/home/<USER>/temu-dmj-extension/src/services/amazon/amazonCollectionController.ts": "40", "/home/<USER>/temu-dmj-extension/src/services/amazon/amazonDataService.ts": "41", "/home/<USER>/temu-dmj-extension/src/services/amazon/amazonPriceService.ts": "42", "/home/<USER>/temu-dmj-extension/src/services/amazon/dataAssemblyService.ts": "43", "/home/<USER>/temu-dmj-extension/src/services/configStorageService.ts": "44", "/home/<USER>/temu-dmj-extension/src/services/dataMapperService.ts": "45", "/home/<USER>/temu-dmj-extension/src/services/dxm/apiService.ts": "46", "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiDetectionService.ts": "47", "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiService.ts": "48", "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiTokenCache.ts": "49", "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiTokenService.ts": "50", "/home/<USER>/temu-dmj-extension/src/services/dxm/imageUploadService.ts": "51", "/home/<USER>/temu-dmj-extension/src/services/imageProcessingService.ts": "52", "/home/<USER>/temu-dmj-extension/src/services/priceReviewService.ts": "53", "/home/<USER>/temu-dmj-extension/src/services/productConfigService.ts": "54", "/home/<USER>/temu-dmj-extension/src/services/productListService.ts": "55", "/home/<USER>/temu-dmj-extension/src/services/pushService.ts": "56", "/home/<USER>/temu-dmj-extension/src/services/shopBindingService.ts": "57", "/home/<USER>/temu-dmj-extension/src/services/temu/temu-api.ts": "58", "/home/<USER>/temu-dmj-extension/src/services/temu/temu-auth.ts": "59", "/home/<USER>/temu-dmj-extension/src/services/temu/temuDataService.ts": "60", "/home/<USER>/temu-dmj-extension/src/stores/options.store.ts": "61", "/home/<USER>/temu-dmj-extension/src/stores/shop.store.ts": "62", "/home/<USER>/temu-dmj-extension/src/stores/test.store.ts": "63", "/home/<USER>/temu-dmj-extension/src/ui/action-popup/app.vue": "64", "/home/<USER>/temu-dmj-extension/src/ui/action-popup/index.ts": "65", "/home/<USER>/temu-dmj-extension/src/ui/action-popup/pages/index.vue": "66", "/home/<USER>/temu-dmj-extension/src/ui/common/pages/404.vue": "67", "/home/<USER>/temu-dmj-extension/src/ui/common/pages/about.vue": "68", "/home/<USER>/temu-dmj-extension/src/ui/common/pages/change-log.vue": "69", "/home/<USER>/temu-dmj-extension/src/ui/common/pages/features.vue": "70", "/home/<USER>/temu-dmj-extension/src/ui/common/pages/help.vue": "71", "/home/<USER>/temu-dmj-extension/src/ui/common/pages/privacy-policy.vue": "72", "/home/<USER>/temu-dmj-extension/src/ui/common/pages/terms-of-service.vue": "73", "/home/<USER>/temu-dmj-extension/src/ui/options-page/app.vue": "74", "/home/<USER>/temu-dmj-extension/src/ui/options-page/index.ts": "75", "/home/<USER>/temu-dmj-extension/src/ui/options-page/pages/index.vue": "76", "/home/<USER>/temu-dmj-extension/src/ui/setup/app.vue": "77", "/home/<USER>/temu-dmj-extension/src/ui/setup/index.ts": "78", "/home/<USER>/temu-dmj-extension/src/ui/setup/pages/install.vue": "79", "/home/<USER>/temu-dmj-extension/src/ui/setup/pages/update.vue": "80", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/app.vue": "81", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/AmazonPriceInfo.vue": "82", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/BasicSettings.vue": "83", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/CategoryCascader.vue": "84", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/CollectionConfig.vue": "85", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/PriceCheckModal.vue": "86", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/PriceHistoryModal.vue": "87", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/PricingManagement.vue": "88", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/ProductAttributes.vue": "89", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/ProductConfig.vue": "90", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/ProductDetailModal.vue": "91", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoActivity.vue": "92", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoDelist.vue": "93", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoFollowPrice.vue": "94", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoPricingCost.vue": "95", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoPricingFixed.vue": "96", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoPricingModal.vue": "97", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoPricingProgress.vue": "98", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoPricingResults.vue": "99", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/AutoSyncStock.vue": "100", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/BatchAd.vue": "101", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/BatchChangeCode.vue": "102", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/BatchCompliance.vue": "103", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/BatchManual.vue": "104", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/DownloadActivity.vue": "105", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/components/auto-pricing/JitStock.vue": "106", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/index.ts": "107", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/auto-pricing.vue": "108", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/dashboard.vue": "109", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/forbidden-words.vue": "110", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/index.vue": "111", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/indexeddb-manager.vue": "112", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/member-service.vue": "113", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/product-center.vue": "114", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/recharge-service.vue": "115", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/shop-maintenance.vue": "116", "/home/<USER>/temu-dmj-extension/src/ui/side-panel/pages/sub-account.vue": "117", "/home/<USER>/temu-dmj-extension/src/utils/cookieHelper.ts": "118", "/home/<USER>/temu-dmj-extension/src/utils/i18n.ts": "119", "/home/<USER>/temu-dmj-extension/src/utils/indexedDBManager.ts": "120", "/home/<USER>/temu-dmj-extension/src/utils/pinia.ts": "121", "/home/<USER>/temu-dmj-extension/src/utils/router/index.ts": "122", "/home/<USER>/temu-dmj-extension/vite.chrome.config.ts": "123", "/home/<USER>/temu-dmj-extension/vite.config.ts": "124", "/home/<USER>/temu-dmj-extension/vite.firefox.config copy.ts": "125", "/home/<USER>/temu-dmj-extension/vite.firefox.config.ts": "126"}, {"size": 1110, "mtime": *************, "results": "127", "hashOfConfig": "128"}, {"size": 263, "mtime": *************, "results": "129", "hashOfConfig": "130"}, {"size": 4873, "mtime": *************, "results": "131", "hashOfConfig": "130"}, {"size": 624, "mtime": *************, "results": "132", "hashOfConfig": "130"}, {"size": 8989, "mtime": *************, "results": "133", "hashOfConfig": "134"}, {"size": 3127, "mtime": *************, "results": "135", "hashOfConfig": "134"}, {"size": 44394, "mtime": *************, "results": "136", "hashOfConfig": "130"}, {"size": 7840, "mtime": *************}, {"size": 1935, "mtime": *************, "results": "137", "hashOfConfig": "138"}, {"size": 940, "mtime": *************}, {"size": 11684, "mtime": *************}, {"size": 7733, "mtime": *************}, {"size": 3249, "mtime": *************}, {"size": 273, "mtime": *************, "results": "139", "hashOfConfig": "138"}, {"size": 27446, "mtime": *************}, {"size": 249, "mtime": *************, "results": "140", "hashOfConfig": "138"}, {"size": 11491, "mtime": 1751035318141}, {"size": 258, "mtime": 1751033556844, "results": "141", "hashOfConfig": "138"}, {"size": 239, "mtime": 1751033556844, "results": "142", "hashOfConfig": "138"}, {"size": 101532, "mtime": 1751033556844, "results": "143", "hashOfConfig": "138"}, {"size": 7808, "mtime": 1751033556844, "results": "144", "hashOfConfig": "130"}, {"size": 1877, "mtime": 1751033556844, "results": "145", "hashOfConfig": "130"}, {"size": 13587, "mtime": 1751033556844, "results": "146", "hashOfConfig": "130"}, {"size": 3081, "mtime": 1751033556844, "results": "147", "hashOfConfig": "130"}, {"size": 35053, "mtime": 1751033556844, "results": "148", "hashOfConfig": "130"}, {"size": 2884, "mtime": 1751033556844, "results": "149", "hashOfConfig": "130"}, {"size": 29968, "mtime": 1751033556844}, {"size": 1237, "mtime": 1751033556844, "results": "150", "hashOfConfig": "130"}, {"size": 4124, "mtime": 1751033556844, "results": "151", "hashOfConfig": "130"}, {"size": 5942, "mtime": 1751033556844, "results": "152", "hashOfConfig": "130"}, {"size": 3616, "mtime": 1751033556844, "results": "153", "hashOfConfig": "130"}, {"size": 53896, "mtime": 1751035849532, "results": "154", "hashOfConfig": "130"}, {"size": 10849, "mtime": 1751033556844, "results": "155", "hashOfConfig": "130"}, {"size": 21959, "mtime": 1751033556844, "results": "156", "hashOfConfig": "130"}, {"size": 40158, "mtime": 1751033556844, "results": "157", "hashOfConfig": "130"}, {"size": 11216, "mtime": 1751033556844, "results": "158", "hashOfConfig": "130"}, {"size": 166, "mtime": 1751033556844, "results": "159", "hashOfConfig": "130"}, {"size": 201, "mtime": 1751033556844, "results": "160", "hashOfConfig": "130"}, {"size": 3578, "mtime": 1751033556844, "results": "161", "hashOfConfig": "130"}, {"size": 12290, "mtime": 1751033556844, "results": "162", "hashOfConfig": "130"}, {"size": 28893, "mtime": 1751033556844, "results": "163", "hashOfConfig": "130"}, {"size": 8394, "mtime": 1751033556844, "results": "164", "hashOfConfig": "130"}, {"size": 12546, "mtime": 1751033556844, "results": "165", "hashOfConfig": "130"}, {"size": 21823, "mtime": 1751033556844, "results": "166", "hashOfConfig": "130"}, {"size": 11492, "mtime": 1751033556844, "results": "167", "hashOfConfig": "130"}, {"size": 6552, "mtime": 1751033556844, "results": "168", "hashOfConfig": "130"}, {"size": 17400, "mtime": 1751033556844, "results": "169", "hashOfConfig": "130"}, {"size": 9220, "mtime": 1751033556844, "results": "170", "hashOfConfig": "130"}, {"size": 11671, "mtime": 1751033556844, "results": "171", "hashOfConfig": "130"}, {"size": 15379, "mtime": 1751033556848, "results": "172", "hashOfConfig": "130"}, {"size": 8641, "mtime": 1751033556848, "results": "173", "hashOfConfig": "130"}, {"size": 10110, "mtime": 1751033556848, "results": "174", "hashOfConfig": "130"}, {"size": 13460, "mtime": 1751033556848, "results": "175", "hashOfConfig": "130"}, {"size": 7094, "mtime": 1751033556848, "results": "176", "hashOfConfig": "130"}, {"size": 22376, "mtime": 1751033556848, "results": "177", "hashOfConfig": "130"}, {"size": 9777, "mtime": 1751033556848, "results": "178", "hashOfConfig": "130"}, {"size": 7919, "mtime": 1751033556848, "results": "179", "hashOfConfig": "130"}, {"size": 8358, "mtime": 1751033556848, "results": "180", "hashOfConfig": "130"}, {"size": 8845, "mtime": 1751033556848, "results": "181", "hashOfConfig": "130"}, {"size": 30054, "mtime": 1751033556848, "results": "182", "hashOfConfig": "130"}, {"size": 471, "mtime": 1751033556848, "results": "183", "hashOfConfig": "130"}, {"size": 3926, "mtime": 1751033556848, "results": "184", "hashOfConfig": "130"}, {"size": 560, "mtime": 1751033556848, "results": "185", "hashOfConfig": "130"}, {"size": 1006, "mtime": 1751033556848, "results": "186", "hashOfConfig": "138"}, {"size": 805, "mtime": 1751033556848, "results": "187", "hashOfConfig": "130"}, {"size": 11470, "mtime": 1751035525783}, {"size": 371, "mtime": 1751033556848, "results": "188", "hashOfConfig": "189"}, {"size": 2528, "mtime": 1751033556848, "results": "190", "hashOfConfig": "189"}, {"size": 5104, "mtime": 1751033556848}, {"size": 5835, "mtime": 1751033556848}, {"size": 5428, "mtime": 1751033556848, "results": "191", "hashOfConfig": "189"}, {"size": 10421, "mtime": 1751033556848, "results": "192", "hashOfConfig": "189"}, {"size": 13546, "mtime": 1751033556848, "results": "193", "hashOfConfig": "189"}, {"size": 343, "mtime": 1751033556848, "results": "194", "hashOfConfig": "138"}, {"size": 670, "mtime": 1751033556848, "results": "195", "hashOfConfig": "130"}, {"size": 16177, "mtime": 1751033556848}, {"size": 555, "mtime": 1751033556848, "results": "196", "hashOfConfig": "138"}, {"size": 690, "mtime": 1751033556848, "results": "197", "hashOfConfig": "130"}, {"size": 1648, "mtime": 1751033556848}, {"size": 1962, "mtime": 1751033556848}, {"size": 774, "mtime": 1751033556848, "results": "198", "hashOfConfig": "138"}, {"size": 12813, "mtime": 1751033556848}, {"size": 56232, "mtime": 1751033556848}, {"size": 6167, "mtime": 1751033556848}, {"size": 13983, "mtime": 1751033556848}, {"size": 9177, "mtime": 1751033556848}, {"size": 9665, "mtime": 1751033556848}, {"size": 11876, "mtime": 1751033556848}, {"size": 11921, "mtime": 1751033556848}, {"size": 16993, "mtime": 1751033556848}, {"size": 10248, "mtime": 1751033556852}, {"size": 6627, "mtime": 1751033556852}, {"size": 5005, "mtime": 1751033556852}, {"size": 5079, "mtime": 1751033556852}, {"size": 6929, "mtime": 1751033556852}, {"size": 7775, "mtime": 1751033556852}, {"size": 7228, "mtime": 1751033556852}, {"size": 6067, "mtime": 1751033556852}, {"size": 8761, "mtime": 1751033556852}, {"size": 7550, "mtime": 1751033556852}, {"size": 5933, "mtime": 1751033556852}, {"size": 3125, "mtime": 1751033556852}, {"size": 6025, "mtime": 1751033556852}, {"size": 5663, "mtime": 1751033556852}, {"size": 5405, "mtime": 1751033556852}, {"size": 5988, "mtime": 1751033556852}, {"size": 2979, "mtime": 1751033556852, "results": "199", "hashOfConfig": "130"}, {"size": 5676, "mtime": 1751033556852}, {"size": 65178, "mtime": 1751035550103}, {"size": 14984, "mtime": 1751033556852}, {"size": 660, "mtime": 1751033556852}, {"size": 21436, "mtime": 1751033556852}, {"size": 16800, "mtime": 1751033556852}, {"size": 20146, "mtime": 1751033556852}, {"size": 752, "mtime": 1751033556852}, {"size": 734, "mtime": 1751033556852}, {"size": 14582, "mtime": 1751033556852}, {"size": 6556, "mtime": 1751033556852, "results": "200", "hashOfConfig": "130"}, {"size": 449, "mtime": 1751033556852, "results": "201", "hashOfConfig": "130"}, {"size": 6637, "mtime": 1751033556852, "results": "202", "hashOfConfig": "130"}, {"size": 72, "mtime": 1751033556852, "results": "203", "hashOfConfig": "130"}, {"size": 357, "mtime": 1751033556852, "results": "204", "hashOfConfig": "130"}, {"size": 2641, "mtime": 1751033556852, "results": "205", "hashOfConfig": "130"}, {"size": 4247, "mtime": 1751033556852, "results": "206", "hashOfConfig": "130"}, {"size": 2529, "mtime": 1751033556852, "results": "207", "hashOfConfig": "130"}, {"size": 2529, "mtime": 1751033556852, "results": "208", "hashOfConfig": "130"}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qf4f08", {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2oiy9n", {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1knar9n", {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wbs818", {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ldrzap", {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/temu-dmj-extension/define.config.mjs", [], [], "/home/<USER>/temu-dmj-extension/manifest.chrome.config.ts", [], [], "/home/<USER>/temu-dmj-extension/manifest.config.ts", [], [], "/home/<USER>/temu-dmj-extension/manifest.firefox.config.ts", [], [], "/home/<USER>/temu-dmj-extension/scripts/getInstalledBrowsers.ts", [], ["440", "441"], "/home/<USER>/temu-dmj-extension/scripts/launch.ts", [], [], "/home/<USER>/temu-dmj-extension/src/background/index.ts", ["442", "443", "444", "445", "446", "447", "448", "449", "450", "451", "452", "453", "454", "455", "456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466", "467"], [], "/home/<USER>/temu-dmj-extension/src/components/AppFooter.vue", [], [], "/home/<USER>/temu-dmj-extension/src/components/RouterLinkUp.vue", [], [], "/home/<USER>/temu-dmj-extension/src/components/ThemeSwitch.vue", [], [], "/home/<USER>/temu-dmj-extension/src/components/state/DisplayError.vue", [], [], "/home/<USER>/temu-dmj-extension/src/components/state/LoadingSpinner.vue", [], [], "/home/<USER>/temu-dmj-extension/src/components/state/tailwind-empty-state.vue", [], [], "/home/<USER>/temu-dmj-extension/src/composables/useAppRouter.ts", ["468", "469", "470", "471", "472", "473", "474", "475", "476", "477"], [], "/home/<USER>/temu-dmj-extension/src/composables/useAuth.ts", [], [], "/home/<USER>/temu-dmj-extension/src/composables/useAutoPricing.ts", ["478", "479"], [], "/home/<USER>/temu-dmj-extension/src/composables/useBrowserStorage.ts", ["480", "481", "482", "483", "484", "485"], [], "/home/<USER>/temu-dmj-extension/src/composables/useDashboard.ts", ["486", "487", "488", "489", "490", "491", "492"], [], "/home/<USER>/temu-dmj-extension/src/composables/useNotification.ts", ["493"], [], "/home/<USER>/temu-dmj-extension/src/composables/useTheme.ts", [], [], "/home/<USER>/temu-dmj-extension/src/config/index.ts", ["494", "495"], [], "/home/<USER>/temu-dmj-extension/src/config/temu-config.ts", ["496"], [], "/home/<USER>/temu-dmj-extension/src/config/temuSites.ts", [], [], "/home/<USER>/temu-dmj-extension/src/content-scripts/amazon/amazon-collector.ts", ["497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518"], [], "/home/<USER>/temu-dmj-extension/src/content-scripts/dxm/dianxiaomi-api-handler.ts", ["519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531"], [], "/home/<USER>/temu-dmj-extension/src/content-scripts/dxm/dianxiaomi-config-injector.ts", ["532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552"], [], "/home/<USER>/temu-dmj-extension/src/content-scripts/index.ts", ["553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568"], [], "/home/<USER>/temu-dmj-extension/src/content-scripts/temu/temu-unified.ts", ["569", "570", "571", "572", "573", "574", "575", "576", "577"], [], "/home/<USER>/temu-dmj-extension/src/lib/utils.ts", [], [], "/home/<USER>/temu-dmj-extension/src/offscreen/index.ts", [], [], "/home/<USER>/temu-dmj-extension/src/services/__tests__/productListService.test.ts", ["578", "579"], [], "/home/<USER>/temu-dmj-extension/src/services/amazon/amazonCollectionController.ts", ["580", "581", "582", "583", "584", "585", "586"], [], "/home/<USER>/temu-dmj-extension/src/services/amazon/amazonDataService.ts", [], [], "/home/<USER>/temu-dmj-extension/src/services/amazon/amazonPriceService.ts", [], [], "/home/<USER>/temu-dmj-extension/src/services/amazon/dataAssemblyService.ts", ["587", "588"], [], "/home/<USER>/temu-dmj-extension/src/services/configStorageService.ts", ["589", "590", "591", "592", "593", "594", "595", "596"], [], "/home/<USER>/temu-dmj-extension/src/services/dataMapperService.ts", ["597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607"], [], "/home/<USER>/temu-dmj-extension/src/services/dxm/apiService.ts", ["608", "609", "610", "611", "612", "613", "614", "615"], [], "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiDetectionService.ts", [], [], "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiService.ts", ["616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627"], [], "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiTokenCache.ts", ["628", "629", "630", "631"], [], "/home/<USER>/temu-dmj-extension/src/services/dxm/dianxiaomiTokenService.ts", ["632", "633", "634"], [], "/home/<USER>/temu-dmj-extension/src/services/dxm/imageUploadService.ts", [], [], "/home/<USER>/temu-dmj-extension/src/services/imageProcessingService.ts", [], [], "/home/<USER>/temu-dmj-extension/src/services/priceReviewService.ts", ["635"], [], "/home/<USER>/temu-dmj-extension/src/services/productConfigService.ts", ["636"], [], "/home/<USER>/temu-dmj-extension/src/services/productListService.ts", ["637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649"], [], "/home/<USER>/temu-dmj-extension/src/services/pushService.ts", ["650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660"], [], "/home/<USER>/temu-dmj-extension/src/services/shopBindingService.ts", ["661"], [], "/home/<USER>/temu-dmj-extension/src/services/temu/temu-api.ts", ["662", "663", "664", "665", "666", "667", "668", "669", "670", "671"], [], "/home/<USER>/temu-dmj-extension/src/services/temu/temu-auth.ts", [], [], "/home/<USER>/temu-dmj-extension/src/services/temu/temuDataService.ts", ["672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686"], [], "/home/<USER>/temu-dmj-extension/src/stores/options.store.ts", [], [], "/home/<USER>/temu-dmj-extension/src/stores/shop.store.ts", [], [], "/home/<USER>/temu-dmj-extension/src/stores/test.store.ts", [], [], "/home/<USER>/temu-dmj-extension/src/ui/action-popup/app.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/action-popup/index.ts", [], [], "/home/<USER>/temu-dmj-extension/src/ui/common/pages/404.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/common/pages/about.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/common/pages/help.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/common/pages/privacy-policy.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/common/pages/terms-of-service.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/options-page/app.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/options-page/index.ts", [], [], "/home/<USER>/temu-dmj-extension/src/ui/setup/app.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/setup/index.ts", [], [], "/home/<USER>/temu-dmj-extension/src/ui/side-panel/app.vue", [], [], "/home/<USER>/temu-dmj-extension/src/ui/side-panel/index.ts", ["687", "688", "689", "690"], [], "/home/<USER>/temu-dmj-extension/src/utils/cookieHelper.ts", [], [], "/home/<USER>/temu-dmj-extension/src/utils/i18n.ts", [], [], "/home/<USER>/temu-dmj-extension/src/utils/indexedDBManager.ts", ["691", "692", "693", "694", "695"], [], "/home/<USER>/temu-dmj-extension/src/utils/pinia.ts", [], [], "/home/<USER>/temu-dmj-extension/src/utils/router/index.ts", [], [], "/home/<USER>/temu-dmj-extension/vite.chrome.config.ts", [], [], "/home/<USER>/temu-dmj-extension/vite.config.ts", [], [], "/home/<USER>/temu-dmj-extension/vite.firefox.config copy.ts", [], [], "/home/<USER>/temu-dmj-extension/vite.firefox.config.ts", [], [], {"ruleId": "696", "severity": 2, "message": "697", "line": 77, "column": 13, "nodeType": "698", "messageId": "699", "endLine": 77, "endColumn": 15, "suggestions": "700", "suppressions": "701"}, {"ruleId": "696", "severity": 2, "message": "697", "line": 83, "column": 13, "nodeType": "698", "messageId": "699", "endLine": 83, "endColumn": 15, "suggestions": "702", "suppressions": "703"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 16, "column": 22, "nodeType": "706", "messageId": "707", "endLine": 16, "endColumn": 25, "suggestions": "708"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 37, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 37, "endColumn": 26, "suggestions": "709"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 38, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 38, "endColumn": 26, "suggestions": "710"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 41, "column": 24, "nodeType": "706", "messageId": "707", "endLine": 41, "endColumn": 27, "suggestions": "711"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 47, "column": 24, "nodeType": "706", "messageId": "707", "endLine": 47, "endColumn": 27, "suggestions": "712"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 74, "column": 45, "nodeType": "706", "messageId": "707", "endLine": 74, "endColumn": 48, "suggestions": "713"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 74, "column": 58, "nodeType": "706", "messageId": "707", "endLine": 74, "endColumn": 61, "suggestions": "714"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 74, "column": 77, "nodeType": "706", "messageId": "707", "endLine": 74, "endColumn": 80, "suggestions": "715"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 121, "column": 53, "nodeType": "706", "messageId": "707", "endLine": 121, "endColumn": 56, "suggestions": "716"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 121, "column": 66, "nodeType": "706", "messageId": "707", "endLine": 121, "endColumn": 69, "suggestions": "717"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 121, "column": 85, "nodeType": "706", "messageId": "707", "endLine": 121, "endColumn": 88, "suggestions": "718"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 135, "column": 25, "nodeType": "706", "messageId": "707", "endLine": 135, "endColumn": 28, "suggestions": "719"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 136, "column": 22, "nodeType": "706", "messageId": "707", "endLine": 136, "endColumn": 25, "suggestions": "720"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 141, "column": 22, "nodeType": "706", "messageId": "707", "endLine": 141, "endColumn": 25, "suggestions": "721"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 145, "column": 63, "nodeType": "706", "messageId": "707", "endLine": 145, "endColumn": 66, "suggestions": "722"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 154, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 154, "endColumn": 40, "suggestions": "723"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 240, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 240, "endColumn": 40, "suggestions": "724"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 258, "column": 39, "nodeType": "706", "messageId": "707", "endLine": 258, "endColumn": 42, "suggestions": "725"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 306, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 306, "endColumn": 46, "suggestions": "726"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 562, "column": 91, "nodeType": "706", "messageId": "707", "endLine": 562, "endColumn": 94, "suggestions": "727"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 628, "column": 94, "nodeType": "706", "messageId": "707", "endLine": 628, "endColumn": 97, "suggestions": "728"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 712, "column": 89, "nodeType": "706", "messageId": "707", "endLine": 712, "endColumn": 92, "suggestions": "729"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 799, "column": 39, "nodeType": "706", "messageId": "707", "endLine": 799, "endColumn": 42, "suggestions": "730"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 872, "column": 46, "nodeType": "706", "messageId": "707", "endLine": 872, "endColumn": 49, "suggestions": "731"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 872, "column": 59, "nodeType": "706", "messageId": "707", "endLine": 872, "endColumn": 62, "suggestions": "732"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 872, "column": 78, "nodeType": "706", "messageId": "707", "endLine": 872, "endColumn": 81, "suggestions": "733"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 22, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 22, "endColumn": 19, "suggestions": "734"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 23, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 23, "endColumn": 19, "suggestions": "735"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 28, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 28, "endColumn": 19, "suggestions": "736"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 58, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 58, "endColumn": 18, "suggestions": "741"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 137, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 137, "endColumn": 16, "suggestions": "742"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 143, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 143, "endColumn": 16, "suggestions": "743"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 149, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 149, "endColumn": 16, "suggestions": "744"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 166, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 166, "endColumn": 16, "suggestions": "745"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 184, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 184, "endColumn": 16, "suggestions": "746"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 190, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 190, "endColumn": 16, "suggestions": "747"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 73, "column": 27, "nodeType": "706", "messageId": "707", "endLine": 73, "endColumn": 30, "suggestions": "748"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 125, "column": 42, "nodeType": "706", "messageId": "707", "endLine": 125, "endColumn": 45, "suggestions": "749"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 3, "column": 30, "nodeType": "706", "messageId": "707", "endLine": 3, "endColumn": 33, "suggestions": "750"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 3, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 3, "endColumn": 46, "suggestions": "751"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 3, "column": 49, "nodeType": "706", "messageId": "707", "endLine": 3, "endColumn": 52, "suggestions": "752"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 26, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 26, "endColumn": 37, "suggestions": "753"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 26, "column": 46, "nodeType": "706", "messageId": "707", "endLine": 26, "endColumn": 49, "suggestions": "754"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 36, "column": 26, "nodeType": "706", "messageId": "707", "endLine": 36, "endColumn": 29, "suggestions": "755"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 41, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 41, "endColumn": 26, "suggestions": "756"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 221, "column": 38, "nodeType": "706", "messageId": "707", "endLine": 221, "endColumn": 41, "suggestions": "757"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 224, "column": 31, "nodeType": "706", "messageId": "707", "endLine": 224, "endColumn": 34, "suggestions": "758"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 251, "column": 47, "nodeType": "706", "messageId": "707", "endLine": 251, "endColumn": 50, "suggestions": "759"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 319, "column": 41, "nodeType": "706", "messageId": "707", "endLine": 319, "endColumn": 44, "suggestions": "760"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 325, "column": 28, "nodeType": "706", "messageId": "707", "endLine": 325, "endColumn": 31, "suggestions": "761"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 426, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 426, "endColumn": 26, "suggestions": "762"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 12, "column": 58, "nodeType": "706", "messageId": "707", "endLine": 12, "endColumn": 61, "suggestions": "763"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 133, "column": 12, "nodeType": "706", "messageId": "707", "endLine": 133, "endColumn": 15, "suggestions": "764"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 135, "column": 48, "nodeType": "706", "messageId": "707", "endLine": 135, "endColumn": 51, "suggestions": "765"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 225, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 225, "endColumn": 37, "suggestions": "766"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 209, "column": 41, "nodeType": "706", "messageId": "707", "endLine": 209, "endColumn": 44, "suggestions": "767"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 239, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 239, "endColumn": 32, "suggestions": "768"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 385, "column": 40, "nodeType": "706", "messageId": "707", "endLine": 385, "endColumn": 43, "suggestions": "769"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 407, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 407, "endColumn": 37, "suggestions": "770"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 426, "column": 26, "nodeType": "706", "messageId": "707", "endLine": 426, "endColumn": 29, "suggestions": "771"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 532, "column": 62, "nodeType": "706", "messageId": "707", "endLine": 532, "endColumn": 65, "suggestions": "772"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 697, "column": 92, "nodeType": "706", "messageId": "707", "endLine": 697, "endColumn": 95, "suggestions": "773"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 860, "column": 55, "nodeType": "706", "messageId": "707", "endLine": 860, "endColumn": 58, "suggestions": "774"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 860, "column": 74, "nodeType": "706", "messageId": "707", "endLine": 860, "endColumn": 77, "suggestions": "775"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 860, "column": 112, "nodeType": "706", "messageId": "707", "endLine": 860, "endColumn": 115, "suggestions": "776"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 908, "column": 53, "nodeType": "706", "messageId": "707", "endLine": 908, "endColumn": 56, "suggestions": "777"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 908, "column": 72, "nodeType": "706", "messageId": "707", "endLine": 908, "endColumn": 75, "suggestions": "778"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 908, "column": 110, "nodeType": "706", "messageId": "707", "endLine": 908, "endColumn": 113, "suggestions": "779"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 982, "column": 61, "nodeType": "706", "messageId": "707", "endLine": 982, "endColumn": 64, "suggestions": "780"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 982, "column": 80, "nodeType": "706", "messageId": "707", "endLine": 982, "endColumn": 83, "suggestions": "781"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 982, "column": 118, "nodeType": "706", "messageId": "707", "endLine": 982, "endColumn": 121, "suggestions": "782"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 982, "column": 203, "nodeType": "706", "messageId": "707", "endLine": 982, "endColumn": 206, "suggestions": "783"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1108, "column": 55, "nodeType": "706", "messageId": "707", "endLine": 1108, "endColumn": 58, "suggestions": "784"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1108, "column": 74, "nodeType": "706", "messageId": "707", "endLine": 1108, "endColumn": 77, "suggestions": "785"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1125, "column": 79, "nodeType": "706", "messageId": "707", "endLine": 1125, "endColumn": 82, "suggestions": "786"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1125, "column": 98, "nodeType": "706", "messageId": "707", "endLine": 1125, "endColumn": 101, "suggestions": "787"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1125, "column": 136, "nodeType": "706", "messageId": "707", "endLine": 1125, "endColumn": 139, "suggestions": "788"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 77, "column": 12, "nodeType": "706", "messageId": "707", "endLine": 77, "endColumn": 15, "suggestions": "789"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 79, "column": 42, "nodeType": "706", "messageId": "707", "endLine": 79, "endColumn": 45, "suggestions": "790"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 118, "column": 17, "nodeType": "706", "messageId": "707", "endLine": 118, "endColumn": 20, "suggestions": "791"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 142, "column": 87, "nodeType": "706", "messageId": "707", "endLine": 142, "endColumn": 90, "suggestions": "792"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 142, "column": 135, "nodeType": "706", "messageId": "707", "endLine": 142, "endColumn": 138, "suggestions": "793"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 148, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 148, "endColumn": 35, "suggestions": "794"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 203, "column": 25, "nodeType": "706", "messageId": "707", "endLine": 203, "endColumn": 28, "suggestions": "795"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 227, "column": 59, "nodeType": "706", "messageId": "707", "endLine": 227, "endColumn": 62, "suggestions": "796"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 254, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 254, "endColumn": 32, "suggestions": "797"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 292, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 292, "endColumn": 32, "suggestions": "798"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 309, "column": 25, "nodeType": "706", "messageId": "707", "endLine": 309, "endColumn": 28, "suggestions": "799"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 311, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 311, "endColumn": 32, "suggestions": "800"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 334, "column": 15, "nodeType": "706", "messageId": "707", "endLine": 334, "endColumn": 18, "suggestions": "801"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 335, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 335, "endColumn": 46, "suggestions": "802"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 336, "column": 41, "nodeType": "706", "messageId": "707", "endLine": 336, "endColumn": 44, "suggestions": "803"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 370, "column": 60, "nodeType": "706", "messageId": "707", "endLine": 370, "endColumn": 63, "suggestions": "804"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 370, "column": 79, "nodeType": "706", "messageId": "707", "endLine": 370, "endColumn": 82, "suggestions": "805"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 389, "column": 49, "nodeType": "706", "messageId": "707", "endLine": 389, "endColumn": 52, "suggestions": "806"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 446, "column": 61, "nodeType": "706", "messageId": "707", "endLine": 446, "endColumn": 64, "suggestions": "807"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 446, "column": 76, "nodeType": "706", "messageId": "707", "endLine": 446, "endColumn": 79, "suggestions": "808"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 469, "column": 36, "nodeType": "706", "messageId": "707", "endLine": 469, "endColumn": 39, "suggestions": "809"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 470, "column": 36, "nodeType": "706", "messageId": "707", "endLine": 470, "endColumn": 39, "suggestions": "810"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 475, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 475, "endColumn": 46, "suggestions": "811"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 491, "column": 50, "nodeType": "706", "messageId": "707", "endLine": 491, "endColumn": 53, "suggestions": "812"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 493, "column": 35, "nodeType": "706", "messageId": "707", "endLine": 493, "endColumn": 38, "suggestions": "813"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 495, "column": 45, "nodeType": "706", "messageId": "707", "endLine": 495, "endColumn": 48, "suggestions": "814"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 496, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 496, "endColumn": 35, "suggestions": "815"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 497, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 497, "endColumn": 37, "suggestions": "816"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 501, "column": 36, "nodeType": "706", "messageId": "707", "endLine": 501, "endColumn": 39, "suggestions": "817"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 502, "column": 36, "nodeType": "706", "messageId": "707", "endLine": 502, "endColumn": 39, "suggestions": "818"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 506, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 506, "endColumn": 46, "suggestions": "819"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 508, "column": 45, "nodeType": "706", "messageId": "707", "endLine": 508, "endColumn": 48, "suggestions": "820"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 509, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 509, "endColumn": 35, "suggestions": "821"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 518, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 518, "endColumn": 35, "suggestions": "822"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 399, "column": 27, "nodeType": "706", "messageId": "707", "endLine": 399, "endColumn": 30, "suggestions": "823"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 496, "column": 61, "nodeType": "706", "messageId": "707", "endLine": 496, "endColumn": 64, "suggestions": "824"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 496, "column": 91, "nodeType": "706", "messageId": "707", "endLine": 496, "endColumn": 94, "suggestions": "825"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 581, "column": 78, "nodeType": "706", "messageId": "707", "endLine": 581, "endColumn": 81, "suggestions": "826"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 620, "column": 25, "nodeType": "706", "messageId": "707", "endLine": 620, "endColumn": 28, "suggestions": "827"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 744, "column": 76, "nodeType": "706", "messageId": "707", "endLine": 744, "endColumn": 79, "suggestions": "828"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 808, "column": 82, "nodeType": "706", "messageId": "707", "endLine": 808, "endColumn": 85, "suggestions": "829"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 872, "column": 108, "nodeType": "706", "messageId": "707", "endLine": 872, "endColumn": 111, "suggestions": "830"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 942, "column": 121, "nodeType": "706", "messageId": "707", "endLine": 942, "endColumn": 124, "suggestions": "831"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1089, "column": 42, "nodeType": "706", "messageId": "707", "endLine": 1089, "endColumn": 45, "suggestions": "832"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1092, "column": 27, "nodeType": "706", "messageId": "707", "endLine": 1092, "endColumn": 30, "suggestions": "833"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1117, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 1117, "endColumn": 32, "suggestions": "834"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1124, "column": 17, "nodeType": "706", "messageId": "707", "endLine": 1124, "endColumn": 20, "suggestions": "835"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1163, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 1163, "endColumn": 32, "suggestions": "836"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1197, "column": 63, "nodeType": "706", "messageId": "707", "endLine": 1197, "endColumn": 66, "suggestions": "837"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 1197, "column": 77, "nodeType": "706", "messageId": "707", "endLine": 1197, "endColumn": 80, "suggestions": "838"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 28, "column": 1, "nodeType": "739", "messageId": "740", "endLine": 28, "endColumn": 12, "suggestions": "839"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 44, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 44, "endColumn": 16, "suggestions": "840"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 187, "column": 53, "nodeType": "706", "messageId": "707", "endLine": 187, "endColumn": 56, "suggestions": "841"}, {"ruleId": "842", "severity": 2, "message": "843", "line": 187, "column": 72, "nodeType": "844", "messageId": "845", "endLine": 187, "endColumn": 80}, {"ruleId": "842", "severity": 2, "message": "843", "line": 233, "column": 43, "nodeType": "844", "messageId": "845", "endLine": 233, "endColumn": 51}, {"ruleId": "842", "severity": 2, "message": "843", "line": 246, "column": 54, "nodeType": "844", "messageId": "845", "endLine": 246, "endColumn": 62}, {"ruleId": "704", "severity": 1, "message": "705", "line": 329, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 329, "endColumn": 32, "suggestions": "846"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 367, "column": 17, "nodeType": "706", "messageId": "707", "endLine": 367, "endColumn": 20, "suggestions": "847"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 384, "column": 9, "nodeType": "739", "messageId": "740", "endLine": 384, "endColumn": 20, "suggestions": "848"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 47, "column": 41, "nodeType": "706", "messageId": "707", "endLine": 47, "endColumn": 44, "suggestions": "849"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 57, "column": 47, "nodeType": "706", "messageId": "707", "endLine": 57, "endColumn": 50, "suggestions": "850"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 30, "column": 14, "nodeType": "706", "messageId": "707", "endLine": 30, "endColumn": 17, "suggestions": "851"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 31, "column": 18, "nodeType": "706", "messageId": "707", "endLine": 31, "endColumn": 21, "suggestions": "852"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 32, "column": 21, "nodeType": "706", "messageId": "707", "endLine": 32, "endColumn": 24, "suggestions": "853"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 35, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 35, "endColumn": 19, "suggestions": "854"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 172, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 172, "endColumn": 26, "suggestions": "855"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 378, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 378, "endColumn": 46, "suggestions": "856"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 378, "column": 49, "nodeType": "706", "messageId": "707", "endLine": 378, "endColumn": 52, "suggestions": "857"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 27, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 27, "endColumn": 19, "suggestions": "858"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 43, "column": 18, "nodeType": "706", "messageId": "707", "endLine": 43, "endColumn": 21, "suggestions": "859"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 92, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 92, "endColumn": 37, "suggestions": "860"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 519, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 519, "endColumn": 40, "suggestions": "861"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 532, "column": 39, "nodeType": "706", "messageId": "707", "endLine": 532, "endColumn": 42, "suggestions": "862"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 667, "column": 41, "nodeType": "706", "messageId": "707", "endLine": 667, "endColumn": 44, "suggestions": "863"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 667, "column": 59, "nodeType": "706", "messageId": "707", "endLine": 667, "endColumn": 62, "suggestions": "864"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 669, "column": 11, "nodeType": "706", "messageId": "707", "endLine": 669, "endColumn": 14, "suggestions": "865"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 670, "column": 11, "nodeType": "706", "messageId": "707", "endLine": 670, "endColumn": 14, "suggestions": "866"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 672, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 672, "endColumn": 19, "suggestions": "867"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 58, "column": 24, "nodeType": "706", "messageId": "707", "endLine": 58, "endColumn": 27, "suggestions": "868"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 91, "column": 14, "nodeType": "706", "messageId": "707", "endLine": 91, "endColumn": 17, "suggestions": "869"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 92, "column": 18, "nodeType": "706", "messageId": "707", "endLine": 92, "endColumn": 21, "suggestions": "870"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 178, "column": 36, "nodeType": "706", "messageId": "707", "endLine": 178, "endColumn": 39, "suggestions": "871"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 178, "column": 59, "nodeType": "706", "messageId": "707", "endLine": 178, "endColumn": 62, "suggestions": "872"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 178, "column": 65, "nodeType": "706", "messageId": "707", "endLine": 178, "endColumn": 68, "suggestions": "873"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 179, "column": 23, "nodeType": "706", "messageId": "707", "endLine": 179, "endColumn": 26, "suggestions": "874"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 252, "column": 46, "nodeType": "706", "messageId": "707", "endLine": 252, "endColumn": 49, "suggestions": "875"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 320, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 320, "endColumn": 40, "suggestions": "876"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 321, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 321, "endColumn": 32, "suggestions": "877"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 368, "column": 52, "nodeType": "706", "messageId": "707", "endLine": 368, "endColumn": 55, "suggestions": "878"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 13, "column": 12, "nodeType": "706", "messageId": "707", "endLine": 13, "endColumn": 15, "suggestions": "879"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 15, "column": 42, "nodeType": "706", "messageId": "707", "endLine": 15, "endColumn": 45, "suggestions": "880"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 80, "column": 72, "nodeType": "706", "messageId": "707", "endLine": 80, "endColumn": 75, "suggestions": "881"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 90, "column": 59, "nodeType": "706", "messageId": "707", "endLine": 90, "endColumn": 62, "suggestions": "882"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 123, "column": 52, "nodeType": "706", "messageId": "707", "endLine": 123, "endColumn": 55, "suggestions": "883"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 123, "column": 93, "nodeType": "706", "messageId": "707", "endLine": 123, "endColumn": 96, "suggestions": "884"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 163, "column": 38, "nodeType": "706", "messageId": "707", "endLine": 163, "endColumn": 41, "suggestions": "885"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 163, "column": 71, "nodeType": "706", "messageId": "707", "endLine": 163, "endColumn": 74, "suggestions": "886"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 142, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 142, "endColumn": 18, "suggestions": "887"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 156, "column": 9, "nodeType": "739", "messageId": "740", "endLine": 156, "endColumn": 20, "suggestions": "888"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 187, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 187, "endColumn": 18, "suggestions": "889"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 201, "column": 9, "nodeType": "739", "messageId": "740", "endLine": 201, "endColumn": 20, "suggestions": "890"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 231, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 231, "endColumn": 18, "suggestions": "891"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 241, "column": 9, "nodeType": "739", "messageId": "740", "endLine": 241, "endColumn": 20, "suggestions": "892"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 270, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 270, "endColumn": 18, "suggestions": "893"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 283, "column": 9, "nodeType": "739", "messageId": "740", "endLine": 283, "endColumn": 20, "suggestions": "894"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 304, "column": 66, "nodeType": "706", "messageId": "707", "endLine": 304, "endColumn": 69, "suggestions": "895"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 304, "column": 81, "nodeType": "706", "messageId": "707", "endLine": 304, "endColumn": 84, "suggestions": "896"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 313, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 313, "endColumn": 18, "suggestions": "897"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 323, "column": 9, "nodeType": "739", "messageId": "740", "endLine": 323, "endColumn": 20, "suggestions": "898"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 12, "column": 15, "nodeType": "706", "messageId": "707", "endLine": 12, "endColumn": 18, "suggestions": "899"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 13, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 13, "endColumn": 19, "suggestions": "900"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 152, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 152, "endColumn": 37, "suggestions": "901"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 305, "column": 33, "nodeType": "706", "messageId": "707", "endLine": 305, "endColumn": 36, "suggestions": "902"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 130, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 130, "endColumn": 19, "suggestions": "903"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 168, "column": 69, "nodeType": "706", "messageId": "707", "endLine": 168, "endColumn": 72, "suggestions": "904"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 209, "column": 16, "nodeType": "706", "messageId": "707", "endLine": 209, "endColumn": 19, "suggestions": "905"}, {"ruleId": "906", "severity": 2, "message": "907", "line": 114, "column": 18, "nodeType": "844", "messageId": "908", "endLine": 114, "endColumn": 34, "suggestions": "909"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 60, "column": 39, "nodeType": "706", "messageId": "707", "endLine": 60, "endColumn": 42, "suggestions": "910"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 150, "column": 33, "nodeType": "706", "messageId": "707", "endLine": 150, "endColumn": 36, "suggestions": "911"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 170, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 170, "endColumn": 40, "suggestions": "912"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 170, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 170, "endColumn": 46, "suggestions": "913"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 203, "column": 34, "nodeType": "706", "messageId": "707", "endLine": 203, "endColumn": 37, "suggestions": "914"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 248, "column": 11, "nodeType": "706", "messageId": "707", "endLine": 248, "endColumn": 14, "suggestions": "915"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 250, "column": 15, "nodeType": "706", "messageId": "707", "endLine": 250, "endColumn": 18, "suggestions": "916"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 349, "column": 38, "nodeType": "706", "messageId": "707", "endLine": 349, "endColumn": 41, "suggestions": "917"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 357, "column": 58, "nodeType": "706", "messageId": "707", "endLine": 357, "endColumn": 61, "suggestions": "918"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 621, "column": 49, "nodeType": "706", "messageId": "707", "endLine": 621, "endColumn": 52, "suggestions": "919"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 632, "column": 52, "nodeType": "706", "messageId": "707", "endLine": 632, "endColumn": 55, "suggestions": "920"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 641, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 641, "endColumn": 35, "suggestions": "921"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 655, "column": 54, "nodeType": "706", "messageId": "707", "endLine": 655, "endColumn": 57, "suggestions": "922"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 679, "column": 40, "nodeType": "706", "messageId": "707", "endLine": 679, "endColumn": 43, "suggestions": "923"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 11, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 11, "endColumn": 13, "suggestions": "924"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 35, "column": 43, "nodeType": "706", "messageId": "707", "endLine": 35, "endColumn": 46, "suggestions": "925"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 70, "column": 22, "nodeType": "706", "messageId": "707", "endLine": 70, "endColumn": 25, "suggestions": "926"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 138, "column": 50, "nodeType": "706", "messageId": "707", "endLine": 138, "endColumn": 53, "suggestions": "927"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 143, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 143, "endColumn": 35, "suggestions": "928"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 204, "column": 65, "nodeType": "706", "messageId": "707", "endLine": 204, "endColumn": 68, "suggestions": "929"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 204, "column": 79, "nodeType": "706", "messageId": "707", "endLine": 204, "endColumn": 82, "suggestions": "930"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 257, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 257, "endColumn": 40, "suggestions": "931"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 290, "column": 33, "nodeType": "706", "messageId": "707", "endLine": 290, "endColumn": 36, "suggestions": "932"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 299, "column": 51, "nodeType": "706", "messageId": "707", "endLine": 299, "endColumn": 54, "suggestions": "933"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 319, "column": 35, "nodeType": "706", "messageId": "707", "endLine": 319, "endColumn": 38, "suggestions": "934"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 45, "column": 31, "nodeType": "706", "messageId": "707", "endLine": 45, "endColumn": 34, "suggestions": "935"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 28, "column": 10, "nodeType": "706", "messageId": "707", "endLine": 28, "endColumn": 13, "suggestions": "936"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 42, "column": 18, "nodeType": "706", "messageId": "707", "endLine": 42, "endColumn": 21, "suggestions": "937"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 52, "column": 25, "nodeType": "706", "messageId": "707", "endLine": 52, "endColumn": 28, "suggestions": "938"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 164, "column": 67, "nodeType": "706", "messageId": "707", "endLine": 164, "endColumn": 70, "suggestions": "939"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 164, "column": 81, "nodeType": "706", "messageId": "707", "endLine": 164, "endColumn": 84, "suggestions": "940"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 242, "column": 31, "nodeType": "706", "messageId": "707", "endLine": 242, "endColumn": 34, "suggestions": "941"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 242, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 242, "endColumn": 40, "suggestions": "942"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 261, "column": 31, "nodeType": "706", "messageId": "707", "endLine": 261, "endColumn": 34, "suggestions": "943"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 261, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 261, "endColumn": 40, "suggestions": "944"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 292, "column": 19, "nodeType": "706", "messageId": "707", "endLine": 292, "endColumn": 22, "suggestions": "945"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 29, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 29, "endColumn": 35, "suggestions": "946"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 158, "column": 45, "nodeType": "706", "messageId": "707", "endLine": 158, "endColumn": 48, "suggestions": "947"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 159, "column": 41, "nodeType": "706", "messageId": "707", "endLine": 159, "endColumn": 44, "suggestions": "948"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 160, "column": 27, "nodeType": "706", "messageId": "707", "endLine": 160, "endColumn": 30, "suggestions": "949"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 215, "column": 64, "nodeType": "706", "messageId": "707", "endLine": 215, "endColumn": 67, "suggestions": "950"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 217, "column": 11, "nodeType": "706", "messageId": "707", "endLine": 217, "endColumn": 14, "suggestions": "951"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 299, "column": 56, "nodeType": "706", "messageId": "707", "endLine": 299, "endColumn": 59, "suggestions": "952"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 315, "column": 36, "nodeType": "706", "messageId": "707", "endLine": 315, "endColumn": 39, "suggestions": "953"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 339, "column": 63, "nodeType": "706", "messageId": "707", "endLine": 339, "endColumn": 66, "suggestions": "954"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 339, "column": 84, "nodeType": "706", "messageId": "707", "endLine": 339, "endColumn": 87, "suggestions": "955"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 429, "column": 47, "nodeType": "706", "messageId": "707", "endLine": 429, "endColumn": 50, "suggestions": "956"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 496, "column": 54, "nodeType": "706", "messageId": "707", "endLine": 496, "endColumn": 57, "suggestions": "957"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 585, "column": 52, "nodeType": "706", "messageId": "707", "endLine": 585, "endColumn": 55, "suggestions": "958"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 736, "column": 40, "nodeType": "706", "messageId": "707", "endLine": 736, "endColumn": 43, "suggestions": "959"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 777, "column": 51, "nodeType": "706", "messageId": "707", "endLine": 777, "endColumn": 54, "suggestions": "960"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 31, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 31, "endColumn": 18, "suggestions": "961"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 35, "column": 9, "nodeType": "739", "messageId": "740", "endLine": 35, "endColumn": 20, "suggestions": "962"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 48, "column": 5, "nodeType": "739", "messageId": "740", "endLine": 48, "endColumn": 16, "suggestions": "963"}, {"ruleId": "737", "severity": 1, "message": "738", "line": 50, "column": 7, "nodeType": "739", "messageId": "740", "endLine": 50, "endColumn": 18, "suggestions": "964"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 13, "column": 29, "nodeType": "706", "messageId": "707", "endLine": 13, "endColumn": 32, "suggestions": "965"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 53, "column": 37, "nodeType": "706", "messageId": "707", "endLine": 53, "endColumn": 40, "suggestions": "966"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 87, "column": 32, "nodeType": "706", "messageId": "707", "endLine": 87, "endColumn": 35, "suggestions": "967"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 141, "column": 53, "nodeType": "706", "messageId": "707", "endLine": 141, "endColumn": 56, "suggestions": "968"}, {"ruleId": "704", "severity": 1, "message": "705", "line": 229, "column": 14, "nodeType": "706", "messageId": "707", "endLine": 229, "endColumn": 17, "suggestions": "969"}, "no-empty", "Empty block statement.", "BlockStatement", "unexpected", ["970"], ["971"], ["972"], ["973"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["974", "975"], ["976", "977"], ["978", "979"], ["980", "981"], ["982", "983"], ["984", "985"], ["986", "987"], ["988", "989"], ["990", "991"], ["992", "993"], ["994", "995"], ["996", "997"], ["998", "999"], ["1000", "1001"], ["1002", "1003"], ["1004", "1005"], ["1006", "1007"], ["1008", "1009"], ["1010", "1011"], ["1012", "1013"], ["1014", "1015"], ["1016", "1017"], ["1018", "1019"], ["1020", "1021"], ["1022", "1023"], ["1024", "1025"], ["1026", "1027"], ["1028", "1029"], ["1030", "1031"], "no-console", "Unexpected console statement. Only these console methods are allowed: info, warn, error.", "MemberExpression", "limited", ["1032"], ["1033"], ["1034"], ["1035"], ["1036"], ["1037"], ["1038"], ["1039", "1040"], ["1041", "1042"], ["1043", "1044"], ["1045", "1046"], ["1047", "1048"], ["1049", "1050"], ["1051", "1052"], ["1053", "1054"], ["1055", "1056"], ["1057", "1058"], ["1059", "1060"], ["1061", "1062"], ["1063", "1064"], ["1065", "1066"], ["1067", "1068"], ["1069", "1070"], ["1071", "1072"], ["1073", "1074"], ["1075", "1076"], ["1077", "1078"], ["1079", "1080"], ["1081", "1082"], ["1083", "1084"], ["1085", "1086"], ["1087", "1088"], ["1089", "1090"], ["1091", "1092"], ["1093", "1094"], ["1095", "1096"], ["1097", "1098"], ["1099", "1100"], ["1101", "1102"], ["1103", "1104"], ["1105", "1106"], ["1107", "1108"], ["1109", "1110"], ["1111", "1112"], ["1113", "1114"], ["1115", "1116"], ["1117", "1118"], ["1119", "1120"], ["1121", "1122"], ["1123", "1124"], ["1125", "1126"], ["1127", "1128"], ["1129", "1130"], ["1131", "1132"], ["1133", "1134"], ["1135", "1136"], ["1137", "1138"], ["1139", "1140"], ["1141", "1142"], ["1143", "1144"], ["1145", "1146"], ["1147", "1148"], ["1149", "1150"], ["1151", "1152"], ["1153", "1154"], ["1155", "1156"], ["1157", "1158"], ["1159", "1160"], ["1161", "1162"], ["1163", "1164"], ["1165", "1166"], ["1167", "1168"], ["1169", "1170"], ["1171", "1172"], ["1173", "1174"], ["1175", "1176"], ["1177", "1178"], ["1179", "1180"], ["1181", "1182"], ["1183", "1184"], ["1185", "1186"], ["1187", "1188"], ["1189", "1190"], ["1191", "1192"], ["1193", "1194"], ["1195", "1196"], ["1197", "1198"], ["1199", "1200"], ["1201", "1202"], ["1203", "1204"], ["1205", "1206"], ["1207", "1208"], ["1209", "1210"], ["1211", "1212"], ["1213", "1214"], ["1215", "1216"], ["1217", "1218"], ["1219", "1220"], ["1221"], ["1222"], ["1223", "1224"], "@typescript-eslint/no-unsafe-function-type", "The `Function` type accepts any function-like value.\nPrefer explicitly defining any function parameters and return type.", "Identifier", "bannedFunctionType", ["1225", "1226"], ["1227", "1228"], ["1229"], ["1230", "1231"], ["1232", "1233"], ["1234", "1235"], ["1236", "1237"], ["1238", "1239"], ["1240", "1241"], ["1242", "1243"], ["1244", "1245"], ["1246", "1247"], ["1248", "1249"], ["1250", "1251"], ["1252", "1253"], ["1254", "1255"], ["1256", "1257"], ["1258", "1259"], ["1260", "1261"], ["1262", "1263"], ["1264", "1265"], ["1266", "1267"], ["1268", "1269"], ["1270", "1271"], ["1272", "1273"], ["1274", "1275"], ["1276", "1277"], ["1278", "1279"], ["1280", "1281"], ["1282", "1283"], ["1284", "1285"], ["1286", "1287"], ["1288", "1289"], ["1290", "1291"], ["1292", "1293"], ["1294", "1295"], ["1296", "1297"], ["1298", "1299"], ["1300", "1301"], ["1302", "1303"], ["1304", "1305"], ["1306"], ["1307"], ["1308"], ["1309"], ["1310"], ["1311"], ["1312"], ["1313"], ["1314", "1315"], ["1316", "1317"], ["1318"], ["1319"], ["1320", "1321"], ["1322", "1323"], ["1324", "1325"], ["1326", "1327"], ["1328", "1329"], ["1330", "1331"], ["1332", "1333"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["1334"], ["1335", "1336"], ["1337", "1338"], ["1339", "1340"], ["1341", "1342"], ["1343", "1344"], ["1345", "1346"], ["1347", "1348"], ["1349", "1350"], ["1351", "1352"], ["1353", "1354"], ["1355", "1356"], ["1357", "1358"], ["1359", "1360"], ["1361", "1362"], ["1363", "1364"], ["1365", "1366"], ["1367", "1368"], ["1369", "1370"], ["1371", "1372"], ["1373", "1374"], ["1375", "1376"], ["1377", "1378"], ["1379", "1380"], ["1381", "1382"], ["1383", "1384"], ["1385", "1386"], ["1387", "1388"], ["1389", "1390"], ["1391", "1392"], ["1393", "1394"], ["1395", "1396"], ["1397", "1398"], ["1399", "1400"], ["1401", "1402"], ["1403", "1404"], ["1405", "1406"], ["1407", "1408"], ["1409", "1410"], ["1411", "1412"], ["1413", "1414"], ["1415", "1416"], ["1417", "1418"], ["1419", "1420"], ["1421", "1422"], ["1423", "1424"], ["1425", "1426"], ["1427", "1428"], ["1429", "1430"], ["1431", "1432"], ["1433", "1434"], ["1435", "1436"], ["1437"], ["1438"], ["1439"], ["1440"], ["1441", "1442"], ["1443", "1444"], ["1445", "1446"], ["1447", "1448"], ["1449", "1450"], {"messageId": "1451", "data": "1452", "fix": "1453", "desc": "1454"}, {"kind": "1455", "justification": "1456"}, {"messageId": "1451", "data": "1457", "fix": "1458", "desc": "1454"}, {"kind": "1455", "justification": "1456"}, {"messageId": "1459", "fix": "1460", "desc": "1461"}, {"messageId": "1462", "fix": "1463", "desc": "1464"}, {"messageId": "1459", "fix": "1465", "desc": "1461"}, {"messageId": "1462", "fix": "1466", "desc": "1464"}, {"messageId": "1459", "fix": "1467", "desc": "1461"}, {"messageId": "1462", "fix": "1468", "desc": "1464"}, {"messageId": "1459", "fix": "1469", "desc": "1461"}, {"messageId": "1462", "fix": "1470", "desc": "1464"}, {"messageId": "1459", "fix": "1471", "desc": "1461"}, {"messageId": "1462", "fix": "1472", "desc": "1464"}, {"messageId": "1459", "fix": "1473", "desc": "1461"}, {"messageId": "1462", "fix": "1474", "desc": "1464"}, {"messageId": "1459", "fix": "1475", "desc": "1461"}, {"messageId": "1462", "fix": "1476", "desc": "1464"}, {"messageId": "1459", "fix": "1477", "desc": "1461"}, {"messageId": "1462", "fix": "1478", "desc": "1464"}, {"messageId": "1459", "fix": "1479", "desc": "1461"}, {"messageId": "1462", "fix": "1480", "desc": "1464"}, {"messageId": "1459", "fix": "1481", "desc": "1461"}, {"messageId": "1462", "fix": "1482", "desc": "1464"}, {"messageId": "1459", "fix": "1483", "desc": "1461"}, {"messageId": "1462", "fix": "1484", "desc": "1464"}, {"messageId": "1459", "fix": "1485", "desc": "1461"}, {"messageId": "1462", "fix": "1486", "desc": "1464"}, {"messageId": "1459", "fix": "1487", "desc": "1461"}, {"messageId": "1462", "fix": "1488", "desc": "1464"}, {"messageId": "1459", "fix": "1489", "desc": "1461"}, {"messageId": "1462", "fix": "1490", "desc": "1464"}, {"messageId": "1459", "fix": "1491", "desc": "1461"}, {"messageId": "1462", "fix": "1492", "desc": "1464"}, {"messageId": "1459", "fix": "1493", "desc": "1461"}, {"messageId": "1462", "fix": "1494", "desc": "1464"}, {"messageId": "1459", "fix": "1495", "desc": "1461"}, {"messageId": "1462", "fix": "1496", "desc": "1464"}, {"messageId": "1459", "fix": "1497", "desc": "1461"}, {"messageId": "1462", "fix": "1498", "desc": "1464"}, {"messageId": "1459", "fix": "1499", "desc": "1461"}, {"messageId": "1462", "fix": "1500", "desc": "1464"}, {"messageId": "1459", "fix": "1501", "desc": "1461"}, {"messageId": "1462", "fix": "1502", "desc": "1464"}, {"messageId": "1459", "fix": "1503", "desc": "1461"}, {"messageId": "1462", "fix": "1504", "desc": "1464"}, {"messageId": "1459", "fix": "1505", "desc": "1461"}, {"messageId": "1462", "fix": "1506", "desc": "1464"}, {"messageId": "1459", "fix": "1507", "desc": "1461"}, {"messageId": "1462", "fix": "1508", "desc": "1464"}, {"messageId": "1459", "fix": "1509", "desc": "1461"}, {"messageId": "1462", "fix": "1510", "desc": "1464"}, {"messageId": "1459", "fix": "1511", "desc": "1461"}, {"messageId": "1462", "fix": "1512", "desc": "1464"}, {"messageId": "1459", "fix": "1513", "desc": "1461"}, {"messageId": "1462", "fix": "1514", "desc": "1464"}, {"messageId": "1459", "fix": "1515", "desc": "1461"}, {"messageId": "1462", "fix": "1516", "desc": "1464"}, {"messageId": "1459", "fix": "1517", "desc": "1461"}, {"messageId": "1462", "fix": "1518", "desc": "1464"}, {"messageId": "1459", "fix": "1519", "desc": "1461"}, {"messageId": "1462", "fix": "1520", "desc": "1464"}, {"fix": "1521", "messageId": "1522", "data": "1523", "desc": "1524"}, {"fix": "1525", "messageId": "1522", "data": "1526", "desc": "1524"}, {"fix": "1527", "messageId": "1522", "data": "1528", "desc": "1524"}, {"fix": "1529", "messageId": "1522", "data": "1530", "desc": "1524"}, {"fix": "1531", "messageId": "1522", "data": "1532", "desc": "1524"}, {"fix": "1533", "messageId": "1522", "data": "1534", "desc": "1524"}, {"fix": "1535", "messageId": "1522", "data": "1536", "desc": "1524"}, {"messageId": "1459", "fix": "1537", "desc": "1461"}, {"messageId": "1462", "fix": "1538", "desc": "1464"}, {"messageId": "1459", "fix": "1539", "desc": "1461"}, {"messageId": "1462", "fix": "1540", "desc": "1464"}, {"messageId": "1459", "fix": "1541", "desc": "1461"}, {"messageId": "1462", "fix": "1542", "desc": "1464"}, {"messageId": "1459", "fix": "1543", "desc": "1461"}, {"messageId": "1462", "fix": "1544", "desc": "1464"}, {"messageId": "1459", "fix": "1545", "desc": "1461"}, {"messageId": "1462", "fix": "1546", "desc": "1464"}, {"messageId": "1459", "fix": "1547", "desc": "1461"}, {"messageId": "1462", "fix": "1548", "desc": "1464"}, {"messageId": "1459", "fix": "1549", "desc": "1461"}, {"messageId": "1462", "fix": "1550", "desc": "1464"}, {"messageId": "1459", "fix": "1551", "desc": "1461"}, {"messageId": "1462", "fix": "1552", "desc": "1464"}, {"messageId": "1459", "fix": "1553", "desc": "1461"}, {"messageId": "1462", "fix": "1554", "desc": "1464"}, {"messageId": "1459", "fix": "1555", "desc": "1461"}, {"messageId": "1462", "fix": "1556", "desc": "1464"}, {"messageId": "1459", "fix": "1557", "desc": "1461"}, {"messageId": "1462", "fix": "1558", "desc": "1464"}, {"messageId": "1459", "fix": "1559", "desc": "1461"}, {"messageId": "1462", "fix": "1560", "desc": "1464"}, {"messageId": "1459", "fix": "1561", "desc": "1461"}, {"messageId": "1462", "fix": "1562", "desc": "1464"}, {"messageId": "1459", "fix": "1563", "desc": "1461"}, {"messageId": "1462", "fix": "1564", "desc": "1464"}, {"messageId": "1459", "fix": "1565", "desc": "1461"}, {"messageId": "1462", "fix": "1566", "desc": "1464"}, {"messageId": "1459", "fix": "1567", "desc": "1461"}, {"messageId": "1462", "fix": "1568", "desc": "1464"}, {"messageId": "1459", "fix": "1569", "desc": "1461"}, {"messageId": "1462", "fix": "1570", "desc": "1464"}, {"messageId": "1459", "fix": "1571", "desc": "1461"}, {"messageId": "1462", "fix": "1572", "desc": "1464"}, {"messageId": "1459", "fix": "1573", "desc": "1461"}, {"messageId": "1462", "fix": "1574", "desc": "1464"}, {"messageId": "1459", "fix": "1575", "desc": "1461"}, {"messageId": "1462", "fix": "1576", "desc": "1464"}, {"messageId": "1459", "fix": "1577", "desc": "1461"}, {"messageId": "1462", "fix": "1578", "desc": "1464"}, {"messageId": "1459", "fix": "1579", "desc": "1461"}, {"messageId": "1462", "fix": "1580", "desc": "1464"}, {"messageId": "1459", "fix": "1581", "desc": "1461"}, {"messageId": "1462", "fix": "1582", "desc": "1464"}, {"messageId": "1459", "fix": "1583", "desc": "1461"}, {"messageId": "1462", "fix": "1584", "desc": "1464"}, {"messageId": "1459", "fix": "1585", "desc": "1461"}, {"messageId": "1462", "fix": "1586", "desc": "1464"}, {"messageId": "1459", "fix": "1587", "desc": "1461"}, {"messageId": "1462", "fix": "1588", "desc": "1464"}, {"messageId": "1459", "fix": "1589", "desc": "1461"}, {"messageId": "1462", "fix": "1590", "desc": "1464"}, {"messageId": "1459", "fix": "1591", "desc": "1461"}, {"messageId": "1462", "fix": "1592", "desc": "1464"}, {"messageId": "1459", "fix": "1593", "desc": "1461"}, {"messageId": "1462", "fix": "1594", "desc": "1464"}, {"messageId": "1459", "fix": "1595", "desc": "1461"}, {"messageId": "1462", "fix": "1596", "desc": "1464"}, {"messageId": "1459", "fix": "1597", "desc": "1461"}, {"messageId": "1462", "fix": "1598", "desc": "1464"}, {"messageId": "1459", "fix": "1599", "desc": "1461"}, {"messageId": "1462", "fix": "1600", "desc": "1464"}, {"messageId": "1459", "fix": "1601", "desc": "1461"}, {"messageId": "1462", "fix": "1602", "desc": "1464"}, {"messageId": "1459", "fix": "1603", "desc": "1461"}, {"messageId": "1462", "fix": "1604", "desc": "1464"}, {"messageId": "1459", "fix": "1605", "desc": "1461"}, {"messageId": "1462", "fix": "1606", "desc": "1464"}, {"messageId": "1459", "fix": "1607", "desc": "1461"}, {"messageId": "1462", "fix": "1608", "desc": "1464"}, {"messageId": "1459", "fix": "1609", "desc": "1461"}, {"messageId": "1462", "fix": "1610", "desc": "1464"}, {"messageId": "1459", "fix": "1611", "desc": "1461"}, {"messageId": "1462", "fix": "1612", "desc": "1464"}, {"messageId": "1459", "fix": "1613", "desc": "1461"}, {"messageId": "1462", "fix": "1614", "desc": "1464"}, {"messageId": "1459", "fix": "1615", "desc": "1461"}, {"messageId": "1462", "fix": "1616", "desc": "1464"}, {"messageId": "1459", "fix": "1617", "desc": "1461"}, {"messageId": "1462", "fix": "1618", "desc": "1464"}, {"messageId": "1459", "fix": "1619", "desc": "1461"}, {"messageId": "1462", "fix": "1620", "desc": "1464"}, {"messageId": "1459", "fix": "1621", "desc": "1461"}, {"messageId": "1462", "fix": "1622", "desc": "1464"}, {"messageId": "1459", "fix": "1623", "desc": "1461"}, {"messageId": "1462", "fix": "1624", "desc": "1464"}, {"messageId": "1459", "fix": "1625", "desc": "1461"}, {"messageId": "1462", "fix": "1626", "desc": "1464"}, {"messageId": "1459", "fix": "1627", "desc": "1461"}, {"messageId": "1462", "fix": "1628", "desc": "1464"}, {"messageId": "1459", "fix": "1629", "desc": "1461"}, {"messageId": "1462", "fix": "1630", "desc": "1464"}, {"messageId": "1459", "fix": "1631", "desc": "1461"}, {"messageId": "1462", "fix": "1632", "desc": "1464"}, {"messageId": "1459", "fix": "1633", "desc": "1461"}, {"messageId": "1462", "fix": "1634", "desc": "1464"}, {"messageId": "1459", "fix": "1635", "desc": "1461"}, {"messageId": "1462", "fix": "1636", "desc": "1464"}, {"messageId": "1459", "fix": "1637", "desc": "1461"}, {"messageId": "1462", "fix": "1638", "desc": "1464"}, {"messageId": "1459", "fix": "1639", "desc": "1461"}, {"messageId": "1462", "fix": "1640", "desc": "1464"}, {"messageId": "1459", "fix": "1641", "desc": "1461"}, {"messageId": "1462", "fix": "1642", "desc": "1464"}, {"messageId": "1459", "fix": "1643", "desc": "1461"}, {"messageId": "1462", "fix": "1644", "desc": "1464"}, {"messageId": "1459", "fix": "1645", "desc": "1461"}, {"messageId": "1462", "fix": "1646", "desc": "1464"}, {"messageId": "1459", "fix": "1647", "desc": "1461"}, {"messageId": "1462", "fix": "1648", "desc": "1464"}, {"messageId": "1459", "fix": "1649", "desc": "1461"}, {"messageId": "1462", "fix": "1650", "desc": "1464"}, {"messageId": "1459", "fix": "1651", "desc": "1461"}, {"messageId": "1462", "fix": "1652", "desc": "1464"}, {"messageId": "1459", "fix": "1653", "desc": "1461"}, {"messageId": "1462", "fix": "1654", "desc": "1464"}, {"messageId": "1459", "fix": "1655", "desc": "1461"}, {"messageId": "1462", "fix": "1656", "desc": "1464"}, {"messageId": "1459", "fix": "1657", "desc": "1461"}, {"messageId": "1462", "fix": "1658", "desc": "1464"}, {"messageId": "1459", "fix": "1659", "desc": "1461"}, {"messageId": "1462", "fix": "1660", "desc": "1464"}, {"messageId": "1459", "fix": "1661", "desc": "1461"}, {"messageId": "1462", "fix": "1662", "desc": "1464"}, {"messageId": "1459", "fix": "1663", "desc": "1461"}, {"messageId": "1462", "fix": "1664", "desc": "1464"}, {"messageId": "1459", "fix": "1665", "desc": "1461"}, {"messageId": "1462", "fix": "1666", "desc": "1464"}, {"messageId": "1459", "fix": "1667", "desc": "1461"}, {"messageId": "1462", "fix": "1668", "desc": "1464"}, {"messageId": "1459", "fix": "1669", "desc": "1461"}, {"messageId": "1462", "fix": "1670", "desc": "1464"}, {"messageId": "1459", "fix": "1671", "desc": "1461"}, {"messageId": "1462", "fix": "1672", "desc": "1464"}, {"messageId": "1459", "fix": "1673", "desc": "1461"}, {"messageId": "1462", "fix": "1674", "desc": "1464"}, {"messageId": "1459", "fix": "1675", "desc": "1461"}, {"messageId": "1462", "fix": "1676", "desc": "1464"}, {"messageId": "1459", "fix": "1677", "desc": "1461"}, {"messageId": "1462", "fix": "1678", "desc": "1464"}, {"messageId": "1459", "fix": "1679", "desc": "1461"}, {"messageId": "1462", "fix": "1680", "desc": "1464"}, {"messageId": "1459", "fix": "1681", "desc": "1461"}, {"messageId": "1462", "fix": "1682", "desc": "1464"}, {"messageId": "1459", "fix": "1683", "desc": "1461"}, {"messageId": "1462", "fix": "1684", "desc": "1464"}, {"messageId": "1459", "fix": "1685", "desc": "1461"}, {"messageId": "1462", "fix": "1686", "desc": "1464"}, {"messageId": "1459", "fix": "1687", "desc": "1461"}, {"messageId": "1462", "fix": "1688", "desc": "1464"}, {"messageId": "1459", "fix": "1689", "desc": "1461"}, {"messageId": "1462", "fix": "1690", "desc": "1464"}, {"messageId": "1459", "fix": "1691", "desc": "1461"}, {"messageId": "1462", "fix": "1692", "desc": "1464"}, {"messageId": "1459", "fix": "1693", "desc": "1461"}, {"messageId": "1462", "fix": "1694", "desc": "1464"}, {"messageId": "1459", "fix": "1695", "desc": "1461"}, {"messageId": "1462", "fix": "1696", "desc": "1464"}, {"messageId": "1459", "fix": "1697", "desc": "1461"}, {"messageId": "1462", "fix": "1698", "desc": "1464"}, {"messageId": "1459", "fix": "1699", "desc": "1461"}, {"messageId": "1462", "fix": "1700", "desc": "1464"}, {"messageId": "1459", "fix": "1701", "desc": "1461"}, {"messageId": "1462", "fix": "1702", "desc": "1464"}, {"messageId": "1459", "fix": "1703", "desc": "1461"}, {"messageId": "1462", "fix": "1704", "desc": "1464"}, {"messageId": "1459", "fix": "1705", "desc": "1461"}, {"messageId": "1462", "fix": "1706", "desc": "1464"}, {"messageId": "1459", "fix": "1707", "desc": "1461"}, {"messageId": "1462", "fix": "1708", "desc": "1464"}, {"messageId": "1459", "fix": "1709", "desc": "1461"}, {"messageId": "1462", "fix": "1710", "desc": "1464"}, {"messageId": "1459", "fix": "1711", "desc": "1461"}, {"messageId": "1462", "fix": "1712", "desc": "1464"}, {"messageId": "1459", "fix": "1713", "desc": "1461"}, {"messageId": "1462", "fix": "1714", "desc": "1464"}, {"messageId": "1459", "fix": "1715", "desc": "1461"}, {"messageId": "1462", "fix": "1716", "desc": "1464"}, {"messageId": "1459", "fix": "1717", "desc": "1461"}, {"messageId": "1462", "fix": "1718", "desc": "1464"}, {"fix": "1719", "messageId": "1522", "data": "1720", "desc": "1524"}, {"fix": "1721", "messageId": "1522", "data": "1722", "desc": "1524"}, {"messageId": "1459", "fix": "1723", "desc": "1461"}, {"messageId": "1462", "fix": "1724", "desc": "1464"}, {"messageId": "1459", "fix": "1725", "desc": "1461"}, {"messageId": "1462", "fix": "1726", "desc": "1464"}, {"messageId": "1459", "fix": "1727", "desc": "1461"}, {"messageId": "1462", "fix": "1728", "desc": "1464"}, {"fix": "1729", "messageId": "1522", "data": "1730", "desc": "1524"}, {"messageId": "1459", "fix": "1731", "desc": "1461"}, {"messageId": "1462", "fix": "1732", "desc": "1464"}, {"messageId": "1459", "fix": "1733", "desc": "1461"}, {"messageId": "1462", "fix": "1734", "desc": "1464"}, {"messageId": "1459", "fix": "1735", "desc": "1461"}, {"messageId": "1462", "fix": "1736", "desc": "1464"}, {"messageId": "1459", "fix": "1737", "desc": "1461"}, {"messageId": "1462", "fix": "1738", "desc": "1464"}, {"messageId": "1459", "fix": "1739", "desc": "1461"}, {"messageId": "1462", "fix": "1740", "desc": "1464"}, {"messageId": "1459", "fix": "1741", "desc": "1461"}, {"messageId": "1462", "fix": "1742", "desc": "1464"}, {"messageId": "1459", "fix": "1743", "desc": "1461"}, {"messageId": "1462", "fix": "1744", "desc": "1464"}, {"messageId": "1459", "fix": "1745", "desc": "1461"}, {"messageId": "1462", "fix": "1746", "desc": "1464"}, {"messageId": "1459", "fix": "1747", "desc": "1461"}, {"messageId": "1462", "fix": "1748", "desc": "1464"}, {"messageId": "1459", "fix": "1749", "desc": "1461"}, {"messageId": "1462", "fix": "1750", "desc": "1464"}, {"messageId": "1459", "fix": "1751", "desc": "1461"}, {"messageId": "1462", "fix": "1752", "desc": "1464"}, {"messageId": "1459", "fix": "1753", "desc": "1461"}, {"messageId": "1462", "fix": "1754", "desc": "1464"}, {"messageId": "1459", "fix": "1755", "desc": "1461"}, {"messageId": "1462", "fix": "1756", "desc": "1464"}, {"messageId": "1459", "fix": "1757", "desc": "1461"}, {"messageId": "1462", "fix": "1758", "desc": "1464"}, {"messageId": "1459", "fix": "1759", "desc": "1461"}, {"messageId": "1462", "fix": "1760", "desc": "1464"}, {"messageId": "1459", "fix": "1761", "desc": "1461"}, {"messageId": "1462", "fix": "1762", "desc": "1464"}, {"messageId": "1459", "fix": "1763", "desc": "1461"}, {"messageId": "1462", "fix": "1764", "desc": "1464"}, {"messageId": "1459", "fix": "1765", "desc": "1461"}, {"messageId": "1462", "fix": "1766", "desc": "1464"}, {"messageId": "1459", "fix": "1767", "desc": "1461"}, {"messageId": "1462", "fix": "1768", "desc": "1464"}, {"messageId": "1459", "fix": "1769", "desc": "1461"}, {"messageId": "1462", "fix": "1770", "desc": "1464"}, {"messageId": "1459", "fix": "1771", "desc": "1461"}, {"messageId": "1462", "fix": "1772", "desc": "1464"}, {"messageId": "1459", "fix": "1773", "desc": "1461"}, {"messageId": "1462", "fix": "1774", "desc": "1464"}, {"messageId": "1459", "fix": "1775", "desc": "1461"}, {"messageId": "1462", "fix": "1776", "desc": "1464"}, {"messageId": "1459", "fix": "1777", "desc": "1461"}, {"messageId": "1462", "fix": "1778", "desc": "1464"}, {"messageId": "1459", "fix": "1779", "desc": "1461"}, {"messageId": "1462", "fix": "1780", "desc": "1464"}, {"messageId": "1459", "fix": "1781", "desc": "1461"}, {"messageId": "1462", "fix": "1782", "desc": "1464"}, {"messageId": "1459", "fix": "1783", "desc": "1461"}, {"messageId": "1462", "fix": "1784", "desc": "1464"}, {"messageId": "1459", "fix": "1785", "desc": "1461"}, {"messageId": "1462", "fix": "1786", "desc": "1464"}, {"messageId": "1459", "fix": "1787", "desc": "1461"}, {"messageId": "1462", "fix": "1788", "desc": "1464"}, {"messageId": "1459", "fix": "1789", "desc": "1461"}, {"messageId": "1462", "fix": "1790", "desc": "1464"}, {"messageId": "1459", "fix": "1791", "desc": "1461"}, {"messageId": "1462", "fix": "1792", "desc": "1464"}, {"messageId": "1459", "fix": "1793", "desc": "1461"}, {"messageId": "1462", "fix": "1794", "desc": "1464"}, {"messageId": "1459", "fix": "1795", "desc": "1461"}, {"messageId": "1462", "fix": "1796", "desc": "1464"}, {"messageId": "1459", "fix": "1797", "desc": "1461"}, {"messageId": "1462", "fix": "1798", "desc": "1464"}, {"messageId": "1459", "fix": "1799", "desc": "1461"}, {"messageId": "1462", "fix": "1800", "desc": "1464"}, {"messageId": "1459", "fix": "1801", "desc": "1461"}, {"messageId": "1462", "fix": "1802", "desc": "1464"}, {"messageId": "1459", "fix": "1803", "desc": "1461"}, {"messageId": "1462", "fix": "1804", "desc": "1464"}, {"messageId": "1459", "fix": "1805", "desc": "1461"}, {"messageId": "1462", "fix": "1806", "desc": "1464"}, {"fix": "1807", "messageId": "1522", "data": "1808", "desc": "1524"}, {"fix": "1809", "messageId": "1522", "data": "1810", "desc": "1524"}, {"fix": "1811", "messageId": "1522", "data": "1812", "desc": "1524"}, {"fix": "1813", "messageId": "1522", "data": "1814", "desc": "1524"}, {"fix": "1815", "messageId": "1522", "data": "1816", "desc": "1524"}, {"fix": "1817", "messageId": "1522", "data": "1818", "desc": "1524"}, {"fix": "1819", "messageId": "1522", "data": "1820", "desc": "1524"}, {"fix": "1821", "messageId": "1522", "data": "1822", "desc": "1524"}, {"messageId": "1459", "fix": "1823", "desc": "1461"}, {"messageId": "1462", "fix": "1824", "desc": "1464"}, {"messageId": "1459", "fix": "1825", "desc": "1461"}, {"messageId": "1462", "fix": "1826", "desc": "1464"}, {"fix": "1827", "messageId": "1522", "data": "1828", "desc": "1524"}, {"fix": "1829", "messageId": "1522", "data": "1830", "desc": "1524"}, {"messageId": "1459", "fix": "1831", "desc": "1461"}, {"messageId": "1462", "fix": "1832", "desc": "1464"}, {"messageId": "1459", "fix": "1833", "desc": "1461"}, {"messageId": "1462", "fix": "1834", "desc": "1464"}, {"messageId": "1459", "fix": "1835", "desc": "1461"}, {"messageId": "1462", "fix": "1836", "desc": "1464"}, {"messageId": "1459", "fix": "1837", "desc": "1461"}, {"messageId": "1462", "fix": "1838", "desc": "1464"}, {"messageId": "1459", "fix": "1839", "desc": "1461"}, {"messageId": "1462", "fix": "1840", "desc": "1464"}, {"messageId": "1459", "fix": "1841", "desc": "1461"}, {"messageId": "1462", "fix": "1842", "desc": "1464"}, {"messageId": "1459", "fix": "1843", "desc": "1461"}, {"messageId": "1462", "fix": "1844", "desc": "1464"}, {"messageId": "1845", "fix": "1846", "desc": "1847"}, {"messageId": "1459", "fix": "1848", "desc": "1461"}, {"messageId": "1462", "fix": "1849", "desc": "1464"}, {"messageId": "1459", "fix": "1850", "desc": "1461"}, {"messageId": "1462", "fix": "1851", "desc": "1464"}, {"messageId": "1459", "fix": "1852", "desc": "1461"}, {"messageId": "1462", "fix": "1853", "desc": "1464"}, {"messageId": "1459", "fix": "1854", "desc": "1461"}, {"messageId": "1462", "fix": "1855", "desc": "1464"}, {"messageId": "1459", "fix": "1856", "desc": "1461"}, {"messageId": "1462", "fix": "1857", "desc": "1464"}, {"messageId": "1459", "fix": "1858", "desc": "1461"}, {"messageId": "1462", "fix": "1859", "desc": "1464"}, {"messageId": "1459", "fix": "1860", "desc": "1461"}, {"messageId": "1462", "fix": "1861", "desc": "1464"}, {"messageId": "1459", "fix": "1862", "desc": "1461"}, {"messageId": "1462", "fix": "1863", "desc": "1464"}, {"messageId": "1459", "fix": "1864", "desc": "1461"}, {"messageId": "1462", "fix": "1865", "desc": "1464"}, {"messageId": "1459", "fix": "1866", "desc": "1461"}, {"messageId": "1462", "fix": "1867", "desc": "1464"}, {"messageId": "1459", "fix": "1868", "desc": "1461"}, {"messageId": "1462", "fix": "1869", "desc": "1464"}, {"messageId": "1459", "fix": "1870", "desc": "1461"}, {"messageId": "1462", "fix": "1871", "desc": "1464"}, {"messageId": "1459", "fix": "1872", "desc": "1461"}, {"messageId": "1462", "fix": "1873", "desc": "1464"}, {"messageId": "1459", "fix": "1874", "desc": "1461"}, {"messageId": "1462", "fix": "1875", "desc": "1464"}, {"messageId": "1459", "fix": "1876", "desc": "1461"}, {"messageId": "1462", "fix": "1877", "desc": "1464"}, {"messageId": "1459", "fix": "1878", "desc": "1461"}, {"messageId": "1462", "fix": "1879", "desc": "1464"}, {"messageId": "1459", "fix": "1880", "desc": "1461"}, {"messageId": "1462", "fix": "1881", "desc": "1464"}, {"messageId": "1459", "fix": "1882", "desc": "1461"}, {"messageId": "1462", "fix": "1883", "desc": "1464"}, {"messageId": "1459", "fix": "1884", "desc": "1461"}, {"messageId": "1462", "fix": "1885", "desc": "1464"}, {"messageId": "1459", "fix": "1886", "desc": "1461"}, {"messageId": "1462", "fix": "1887", "desc": "1464"}, {"messageId": "1459", "fix": "1888", "desc": "1461"}, {"messageId": "1462", "fix": "1889", "desc": "1464"}, {"messageId": "1459", "fix": "1890", "desc": "1461"}, {"messageId": "1462", "fix": "1891", "desc": "1464"}, {"messageId": "1459", "fix": "1892", "desc": "1461"}, {"messageId": "1462", "fix": "1893", "desc": "1464"}, {"messageId": "1459", "fix": "1894", "desc": "1461"}, {"messageId": "1462", "fix": "1895", "desc": "1464"}, {"messageId": "1459", "fix": "1896", "desc": "1461"}, {"messageId": "1462", "fix": "1897", "desc": "1464"}, {"messageId": "1459", "fix": "1898", "desc": "1461"}, {"messageId": "1462", "fix": "1899", "desc": "1464"}, {"messageId": "1459", "fix": "1900", "desc": "1461"}, {"messageId": "1462", "fix": "1901", "desc": "1464"}, {"messageId": "1459", "fix": "1902", "desc": "1461"}, {"messageId": "1462", "fix": "1903", "desc": "1464"}, {"messageId": "1459", "fix": "1904", "desc": "1461"}, {"messageId": "1462", "fix": "1905", "desc": "1464"}, {"messageId": "1459", "fix": "1906", "desc": "1461"}, {"messageId": "1462", "fix": "1907", "desc": "1464"}, {"messageId": "1459", "fix": "1908", "desc": "1461"}, {"messageId": "1462", "fix": "1909", "desc": "1464"}, {"messageId": "1459", "fix": "1910", "desc": "1461"}, {"messageId": "1462", "fix": "1911", "desc": "1464"}, {"messageId": "1459", "fix": "1912", "desc": "1461"}, {"messageId": "1462", "fix": "1913", "desc": "1464"}, {"messageId": "1459", "fix": "1914", "desc": "1461"}, {"messageId": "1462", "fix": "1915", "desc": "1464"}, {"messageId": "1459", "fix": "1916", "desc": "1461"}, {"messageId": "1462", "fix": "1917", "desc": "1464"}, {"messageId": "1459", "fix": "1918", "desc": "1461"}, {"messageId": "1462", "fix": "1919", "desc": "1464"}, {"messageId": "1459", "fix": "1920", "desc": "1461"}, {"messageId": "1462", "fix": "1921", "desc": "1464"}, {"messageId": "1459", "fix": "1922", "desc": "1461"}, {"messageId": "1462", "fix": "1923", "desc": "1464"}, {"messageId": "1459", "fix": "1924", "desc": "1461"}, {"messageId": "1462", "fix": "1925", "desc": "1464"}, {"messageId": "1459", "fix": "1926", "desc": "1461"}, {"messageId": "1462", "fix": "1927", "desc": "1464"}, {"messageId": "1459", "fix": "1928", "desc": "1461"}, {"messageId": "1462", "fix": "1929", "desc": "1464"}, {"messageId": "1459", "fix": "1930", "desc": "1461"}, {"messageId": "1462", "fix": "1931", "desc": "1464"}, {"messageId": "1459", "fix": "1932", "desc": "1461"}, {"messageId": "1462", "fix": "1933", "desc": "1464"}, {"messageId": "1459", "fix": "1934", "desc": "1461"}, {"messageId": "1462", "fix": "1935", "desc": "1464"}, {"messageId": "1459", "fix": "1936", "desc": "1461"}, {"messageId": "1462", "fix": "1937", "desc": "1464"}, {"messageId": "1459", "fix": "1938", "desc": "1461"}, {"messageId": "1462", "fix": "1939", "desc": "1464"}, {"messageId": "1459", "fix": "1940", "desc": "1461"}, {"messageId": "1462", "fix": "1941", "desc": "1464"}, {"messageId": "1459", "fix": "1942", "desc": "1461"}, {"messageId": "1462", "fix": "1943", "desc": "1464"}, {"messageId": "1459", "fix": "1944", "desc": "1461"}, {"messageId": "1462", "fix": "1945", "desc": "1464"}, {"messageId": "1459", "fix": "1946", "desc": "1461"}, {"messageId": "1462", "fix": "1947", "desc": "1464"}, {"messageId": "1459", "fix": "1948", "desc": "1461"}, {"messageId": "1462", "fix": "1949", "desc": "1464"}, {"fix": "1950", "messageId": "1522", "data": "1951", "desc": "1524"}, {"fix": "1952", "messageId": "1522", "data": "1953", "desc": "1524"}, {"fix": "1954", "messageId": "1522", "data": "1955", "desc": "1524"}, {"fix": "1956", "messageId": "1522", "data": "1957", "desc": "1524"}, {"messageId": "1459", "fix": "1958", "desc": "1461"}, {"messageId": "1462", "fix": "1959", "desc": "1464"}, {"messageId": "1459", "fix": "1960", "desc": "1461"}, {"messageId": "1462", "fix": "1961", "desc": "1464"}, {"messageId": "1459", "fix": "1962", "desc": "1461"}, {"messageId": "1462", "fix": "1963", "desc": "1464"}, {"messageId": "1459", "fix": "1964", "desc": "1461"}, {"messageId": "1462", "fix": "1965", "desc": "1464"}, {"messageId": "1459", "fix": "1966", "desc": "1461"}, {"messageId": "1462", "fix": "1967", "desc": "1464"}, "suggestComment", {"type": "1968"}, {"range": "1969", "text": "1970"}, "Add comment inside empty block statement.", "directive", "", {"type": "1968"}, {"range": "1971", "text": "1970"}, "suggestUnknown", {"range": "1972", "text": "1973"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1974", "text": "1975"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1976", "text": "1973"}, {"range": "1977", "text": "1975"}, {"range": "1978", "text": "1973"}, {"range": "1979", "text": "1975"}, {"range": "1980", "text": "1973"}, {"range": "1981", "text": "1975"}, {"range": "1982", "text": "1973"}, {"range": "1983", "text": "1975"}, {"range": "1984", "text": "1973"}, {"range": "1985", "text": "1975"}, {"range": "1986", "text": "1973"}, {"range": "1987", "text": "1975"}, {"range": "1988", "text": "1973"}, {"range": "1989", "text": "1975"}, {"range": "1990", "text": "1973"}, {"range": "1991", "text": "1975"}, {"range": "1992", "text": "1973"}, {"range": "1993", "text": "1975"}, {"range": "1994", "text": "1973"}, {"range": "1995", "text": "1975"}, {"range": "1996", "text": "1973"}, {"range": "1997", "text": "1975"}, {"range": "1998", "text": "1973"}, {"range": "1999", "text": "1975"}, {"range": "2000", "text": "1973"}, {"range": "2001", "text": "1975"}, {"range": "2002", "text": "1973"}, {"range": "2003", "text": "1975"}, {"range": "2004", "text": "1973"}, {"range": "2005", "text": "1975"}, {"range": "2006", "text": "1973"}, {"range": "2007", "text": "1975"}, {"range": "2008", "text": "1973"}, {"range": "2009", "text": "1975"}, {"range": "2010", "text": "1973"}, {"range": "2011", "text": "1975"}, {"range": "2012", "text": "1973"}, {"range": "2013", "text": "1975"}, {"range": "2014", "text": "1973"}, {"range": "2015", "text": "1975"}, {"range": "2016", "text": "1973"}, {"range": "2017", "text": "1975"}, {"range": "2018", "text": "1973"}, {"range": "2019", "text": "1975"}, {"range": "2020", "text": "1973"}, {"range": "2021", "text": "1975"}, {"range": "2022", "text": "1973"}, {"range": "2023", "text": "1975"}, {"range": "2024", "text": "1973"}, {"range": "2025", "text": "1975"}, {"range": "2026", "text": "1973"}, {"range": "2027", "text": "1975"}, {"range": "2028", "text": "1973"}, {"range": "2029", "text": "1975"}, {"range": "2030", "text": "1973"}, {"range": "2031", "text": "1975"}, {"range": "2032", "text": "1456"}, "removeConsole", {"propertyName": "2033"}, "Remove the console.log().", {"range": "2034", "text": "1456"}, {"propertyName": "2033"}, {"range": "2035", "text": "1456"}, {"propertyName": "2033"}, {"range": "2036", "text": "1456"}, {"propertyName": "2033"}, {"range": "2037", "text": "1456"}, {"propertyName": "2033"}, {"range": "2038", "text": "1456"}, {"propertyName": "2033"}, {"range": "2039", "text": "1456"}, {"propertyName": "2033"}, {"range": "2040", "text": "1973"}, {"range": "2041", "text": "1975"}, {"range": "2042", "text": "1973"}, {"range": "2043", "text": "1975"}, {"range": "2044", "text": "1973"}, {"range": "2045", "text": "1975"}, {"range": "2046", "text": "1973"}, {"range": "2047", "text": "1975"}, {"range": "2048", "text": "1973"}, {"range": "2049", "text": "1975"}, {"range": "2050", "text": "1973"}, {"range": "2051", "text": "1975"}, {"range": "2052", "text": "1973"}, {"range": "2053", "text": "1975"}, {"range": "2054", "text": "1973"}, {"range": "2055", "text": "1975"}, {"range": "2056", "text": "1973"}, {"range": "2057", "text": "1975"}, {"range": "2058", "text": "1973"}, {"range": "2059", "text": "1975"}, {"range": "2060", "text": "1973"}, {"range": "2061", "text": "1975"}, {"range": "2062", "text": "1973"}, {"range": "2063", "text": "1975"}, {"range": "2064", "text": "1973"}, {"range": "2065", "text": "1975"}, {"range": "2066", "text": "1973"}, {"range": "2067", "text": "1975"}, {"range": "2068", "text": "1973"}, {"range": "2069", "text": "1975"}, {"range": "2070", "text": "1973"}, {"range": "2071", "text": "1975"}, {"range": "2072", "text": "1973"}, {"range": "2073", "text": "1975"}, {"range": "2074", "text": "1973"}, {"range": "2075", "text": "1975"}, {"range": "2076", "text": "1973"}, {"range": "2077", "text": "1975"}, {"range": "2078", "text": "1973"}, {"range": "2079", "text": "1975"}, {"range": "2080", "text": "1973"}, {"range": "2081", "text": "1975"}, {"range": "2082", "text": "1973"}, {"range": "2083", "text": "1975"}, {"range": "2084", "text": "1973"}, {"range": "2085", "text": "1975"}, {"range": "2086", "text": "1973"}, {"range": "2087", "text": "1975"}, {"range": "2088", "text": "1973"}, {"range": "2089", "text": "1975"}, {"range": "2090", "text": "1973"}, {"range": "2091", "text": "1975"}, {"range": "2092", "text": "1973"}, {"range": "2093", "text": "1975"}, {"range": "2094", "text": "1973"}, {"range": "2095", "text": "1975"}, {"range": "2096", "text": "1973"}, {"range": "2097", "text": "1975"}, {"range": "2098", "text": "1973"}, {"range": "2099", "text": "1975"}, {"range": "2100", "text": "1973"}, {"range": "2101", "text": "1975"}, {"range": "2102", "text": "1973"}, {"range": "2103", "text": "1975"}, {"range": "2104", "text": "1973"}, {"range": "2105", "text": "1975"}, {"range": "2106", "text": "1973"}, {"range": "2107", "text": "1975"}, {"range": "2108", "text": "1973"}, {"range": "2109", "text": "1975"}, {"range": "2110", "text": "1973"}, {"range": "2111", "text": "1975"}, {"range": "2112", "text": "1973"}, {"range": "2113", "text": "1975"}, {"range": "2114", "text": "1973"}, {"range": "2115", "text": "1975"}, {"range": "2116", "text": "1973"}, {"range": "2117", "text": "1975"}, {"range": "2118", "text": "1973"}, {"range": "2119", "text": "1975"}, {"range": "2120", "text": "1973"}, {"range": "2121", "text": "1975"}, {"range": "2122", "text": "1973"}, {"range": "2123", "text": "1975"}, {"range": "2124", "text": "1973"}, {"range": "2125", "text": "1975"}, {"range": "2126", "text": "1973"}, {"range": "2127", "text": "1975"}, {"range": "2128", "text": "1973"}, {"range": "2129", "text": "1975"}, {"range": "2130", "text": "1973"}, {"range": "2131", "text": "1975"}, {"range": "2132", "text": "1973"}, {"range": "2133", "text": "1975"}, {"range": "2134", "text": "1973"}, {"range": "2135", "text": "1975"}, {"range": "2136", "text": "1973"}, {"range": "2137", "text": "1975"}, {"range": "2138", "text": "1973"}, {"range": "2139", "text": "1975"}, {"range": "2140", "text": "1973"}, {"range": "2141", "text": "1975"}, {"range": "2142", "text": "1973"}, {"range": "2143", "text": "1975"}, {"range": "2144", "text": "1973"}, {"range": "2145", "text": "1975"}, {"range": "2146", "text": "1973"}, {"range": "2147", "text": "1975"}, {"range": "2148", "text": "1973"}, {"range": "2149", "text": "1975"}, {"range": "2150", "text": "1973"}, {"range": "2151", "text": "1975"}, {"range": "2152", "text": "1973"}, {"range": "2153", "text": "1975"}, {"range": "2154", "text": "1973"}, {"range": "2155", "text": "1975"}, {"range": "2156", "text": "1973"}, {"range": "2157", "text": "1975"}, {"range": "2158", "text": "1973"}, {"range": "2159", "text": "1975"}, {"range": "2160", "text": "1973"}, {"range": "2161", "text": "1975"}, {"range": "2162", "text": "1973"}, {"range": "2163", "text": "1975"}, {"range": "2164", "text": "1973"}, {"range": "2165", "text": "1975"}, {"range": "2166", "text": "1973"}, {"range": "2167", "text": "1975"}, {"range": "2168", "text": "1973"}, {"range": "2169", "text": "1975"}, {"range": "2170", "text": "1973"}, {"range": "2171", "text": "1975"}, {"range": "2172", "text": "1973"}, {"range": "2173", "text": "1975"}, {"range": "2174", "text": "1973"}, {"range": "2175", "text": "1975"}, {"range": "2176", "text": "1973"}, {"range": "2177", "text": "1975"}, {"range": "2178", "text": "1973"}, {"range": "2179", "text": "1975"}, {"range": "2180", "text": "1973"}, {"range": "2181", "text": "1975"}, {"range": "2182", "text": "1973"}, {"range": "2183", "text": "1975"}, {"range": "2184", "text": "1973"}, {"range": "2185", "text": "1975"}, {"range": "2186", "text": "1973"}, {"range": "2187", "text": "1975"}, {"range": "2188", "text": "1973"}, {"range": "2189", "text": "1975"}, {"range": "2190", "text": "1973"}, {"range": "2191", "text": "1975"}, {"range": "2192", "text": "1973"}, {"range": "2193", "text": "1975"}, {"range": "2194", "text": "1973"}, {"range": "2195", "text": "1975"}, {"range": "2196", "text": "1973"}, {"range": "2197", "text": "1975"}, {"range": "2198", "text": "1973"}, {"range": "2199", "text": "1975"}, {"range": "2200", "text": "1973"}, {"range": "2201", "text": "1975"}, {"range": "2202", "text": "1973"}, {"range": "2203", "text": "1975"}, {"range": "2204", "text": "1973"}, {"range": "2205", "text": "1975"}, {"range": "2206", "text": "1973"}, {"range": "2207", "text": "1975"}, {"range": "2208", "text": "1973"}, {"range": "2209", "text": "1975"}, {"range": "2210", "text": "1973"}, {"range": "2211", "text": "1975"}, {"range": "2212", "text": "1973"}, {"range": "2213", "text": "1975"}, {"range": "2214", "text": "1973"}, {"range": "2215", "text": "1975"}, {"range": "2216", "text": "1973"}, {"range": "2217", "text": "1975"}, {"range": "2218", "text": "1973"}, {"range": "2219", "text": "1975"}, {"range": "2220", "text": "1973"}, {"range": "2221", "text": "1975"}, {"range": "2222", "text": "1456"}, {"propertyName": "2033"}, {"range": "2223", "text": "1456"}, {"propertyName": "2033"}, {"range": "2224", "text": "1973"}, {"range": "2225", "text": "1975"}, {"range": "2226", "text": "1973"}, {"range": "2227", "text": "1975"}, {"range": "2228", "text": "1973"}, {"range": "2229", "text": "1975"}, {"range": "2230", "text": "1456"}, {"propertyName": "2033"}, {"range": "2231", "text": "1973"}, {"range": "2232", "text": "1975"}, {"range": "2233", "text": "1973"}, {"range": "2234", "text": "1975"}, {"range": "2235", "text": "1973"}, {"range": "2236", "text": "1975"}, {"range": "2237", "text": "1973"}, {"range": "2238", "text": "1975"}, {"range": "2239", "text": "1973"}, {"range": "2240", "text": "1975"}, {"range": "2241", "text": "1973"}, {"range": "2242", "text": "1975"}, {"range": "2243", "text": "1973"}, {"range": "2244", "text": "1975"}, {"range": "2245", "text": "1973"}, {"range": "2246", "text": "1975"}, {"range": "2247", "text": "1973"}, {"range": "2248", "text": "1975"}, {"range": "2249", "text": "1973"}, {"range": "2250", "text": "1975"}, {"range": "2251", "text": "1973"}, {"range": "2252", "text": "1975"}, {"range": "2253", "text": "1973"}, {"range": "2254", "text": "1975"}, {"range": "2255", "text": "1973"}, {"range": "2256", "text": "1975"}, {"range": "2257", "text": "1973"}, {"range": "2258", "text": "1975"}, {"range": "2259", "text": "1973"}, {"range": "2260", "text": "1975"}, {"range": "2261", "text": "1973"}, {"range": "2262", "text": "1975"}, {"range": "2263", "text": "1973"}, {"range": "2264", "text": "1975"}, {"range": "2265", "text": "1973"}, {"range": "2266", "text": "1975"}, {"range": "2267", "text": "1973"}, {"range": "2268", "text": "1975"}, {"range": "2269", "text": "1973"}, {"range": "2270", "text": "1975"}, {"range": "2271", "text": "1973"}, {"range": "2272", "text": "1975"}, {"range": "2273", "text": "1973"}, {"range": "2274", "text": "1975"}, {"range": "2275", "text": "1973"}, {"range": "2276", "text": "1975"}, {"range": "2277", "text": "1973"}, {"range": "2278", "text": "1975"}, {"range": "2279", "text": "1973"}, {"range": "2280", "text": "1975"}, {"range": "2281", "text": "1973"}, {"range": "2282", "text": "1975"}, {"range": "2283", "text": "1973"}, {"range": "2284", "text": "1975"}, {"range": "2285", "text": "1973"}, {"range": "2286", "text": "1975"}, {"range": "2287", "text": "1973"}, {"range": "2288", "text": "1975"}, {"range": "2289", "text": "1973"}, {"range": "2290", "text": "1975"}, {"range": "2291", "text": "1973"}, {"range": "2292", "text": "1975"}, {"range": "2293", "text": "1973"}, {"range": "2294", "text": "1975"}, {"range": "2295", "text": "1973"}, {"range": "2296", "text": "1975"}, {"range": "2297", "text": "1973"}, {"range": "2298", "text": "1975"}, {"range": "2299", "text": "1973"}, {"range": "2300", "text": "1975"}, {"range": "2301", "text": "1973"}, {"range": "2302", "text": "1975"}, {"range": "2303", "text": "1973"}, {"range": "2304", "text": "1975"}, {"range": "2305", "text": "1973"}, {"range": "2306", "text": "1975"}, {"range": "2307", "text": "1456"}, {"propertyName": "2033"}, {"range": "2308", "text": "1456"}, {"propertyName": "2033"}, {"range": "2309", "text": "1456"}, {"propertyName": "2033"}, {"range": "2310", "text": "1456"}, {"propertyName": "2033"}, {"range": "2311", "text": "1456"}, {"propertyName": "2033"}, {"range": "2312", "text": "1456"}, {"propertyName": "2033"}, {"range": "2313", "text": "1456"}, {"propertyName": "2033"}, {"range": "2314", "text": "1456"}, {"propertyName": "2033"}, {"range": "2315", "text": "1973"}, {"range": "2316", "text": "1975"}, {"range": "2317", "text": "1973"}, {"range": "2318", "text": "1975"}, {"range": "2319", "text": "1456"}, {"propertyName": "2033"}, {"range": "2320", "text": "1456"}, {"propertyName": "2033"}, {"range": "2321", "text": "1973"}, {"range": "2322", "text": "1975"}, {"range": "2323", "text": "1973"}, {"range": "2324", "text": "1975"}, {"range": "2325", "text": "1973"}, {"range": "2326", "text": "1975"}, {"range": "2327", "text": "1973"}, {"range": "2328", "text": "1975"}, {"range": "2329", "text": "1973"}, {"range": "2330", "text": "1975"}, {"range": "2331", "text": "1973"}, {"range": "2332", "text": "1975"}, {"range": "2333", "text": "1973"}, {"range": "2334", "text": "1975"}, "replaceEmptyInterfaceWithSuper", {"range": "2335", "text": "2336"}, "Replace empty interface with a type alias.", {"range": "2337", "text": "1973"}, {"range": "2338", "text": "1975"}, {"range": "2339", "text": "1973"}, {"range": "2340", "text": "1975"}, {"range": "2341", "text": "1973"}, {"range": "2342", "text": "1975"}, {"range": "2343", "text": "1973"}, {"range": "2344", "text": "1975"}, {"range": "2345", "text": "1973"}, {"range": "2346", "text": "1975"}, {"range": "2347", "text": "1973"}, {"range": "2348", "text": "1975"}, {"range": "2349", "text": "1973"}, {"range": "2350", "text": "1975"}, {"range": "2351", "text": "1973"}, {"range": "2352", "text": "1975"}, {"range": "2353", "text": "1973"}, {"range": "2354", "text": "1975"}, {"range": "2355", "text": "1973"}, {"range": "2356", "text": "1975"}, {"range": "2357", "text": "1973"}, {"range": "2358", "text": "1975"}, {"range": "2359", "text": "1973"}, {"range": "2360", "text": "1975"}, {"range": "2361", "text": "1973"}, {"range": "2362", "text": "1975"}, {"range": "2363", "text": "1973"}, {"range": "2364", "text": "1975"}, {"range": "2365", "text": "1973"}, {"range": "2366", "text": "1975"}, {"range": "2367", "text": "1973"}, {"range": "2368", "text": "1975"}, {"range": "2369", "text": "1973"}, {"range": "2370", "text": "1975"}, {"range": "2371", "text": "1973"}, {"range": "2372", "text": "1975"}, {"range": "2373", "text": "1973"}, {"range": "2374", "text": "1975"}, {"range": "2375", "text": "1973"}, {"range": "2376", "text": "1975"}, {"range": "2377", "text": "1973"}, {"range": "2378", "text": "1975"}, {"range": "2379", "text": "1973"}, {"range": "2380", "text": "1975"}, {"range": "2381", "text": "1973"}, {"range": "2382", "text": "1975"}, {"range": "2383", "text": "1973"}, {"range": "2384", "text": "1975"}, {"range": "2385", "text": "1973"}, {"range": "2386", "text": "1975"}, {"range": "2387", "text": "1973"}, {"range": "2388", "text": "1975"}, {"range": "2389", "text": "1973"}, {"range": "2390", "text": "1975"}, {"range": "2391", "text": "1973"}, {"range": "2392", "text": "1975"}, {"range": "2393", "text": "1973"}, {"range": "2394", "text": "1975"}, {"range": "2395", "text": "1973"}, {"range": "2396", "text": "1975"}, {"range": "2397", "text": "1973"}, {"range": "2398", "text": "1975"}, {"range": "2399", "text": "1973"}, {"range": "2400", "text": "1975"}, {"range": "2401", "text": "1973"}, {"range": "2402", "text": "1975"}, {"range": "2403", "text": "1973"}, {"range": "2404", "text": "1975"}, {"range": "2405", "text": "1973"}, {"range": "2406", "text": "1975"}, {"range": "2407", "text": "1973"}, {"range": "2408", "text": "1975"}, {"range": "2409", "text": "1973"}, {"range": "2410", "text": "1975"}, {"range": "2411", "text": "1973"}, {"range": "2412", "text": "1975"}, {"range": "2413", "text": "1973"}, {"range": "2414", "text": "1975"}, {"range": "2415", "text": "1973"}, {"range": "2416", "text": "1975"}, {"range": "2417", "text": "1973"}, {"range": "2418", "text": "1975"}, {"range": "2419", "text": "1973"}, {"range": "2420", "text": "1975"}, {"range": "2421", "text": "1973"}, {"range": "2422", "text": "1975"}, {"range": "2423", "text": "1973"}, {"range": "2424", "text": "1975"}, {"range": "2425", "text": "1973"}, {"range": "2426", "text": "1975"}, {"range": "2427", "text": "1973"}, {"range": "2428", "text": "1975"}, {"range": "2429", "text": "1973"}, {"range": "2430", "text": "1975"}, {"range": "2431", "text": "1973"}, {"range": "2432", "text": "1975"}, {"range": "2433", "text": "1973"}, {"range": "2434", "text": "1975"}, {"range": "2435", "text": "1973"}, {"range": "2436", "text": "1975"}, {"range": "2437", "text": "1973"}, {"range": "2438", "text": "1975"}, {"range": "2439", "text": "1456"}, {"propertyName": "2033"}, {"range": "2440", "text": "1456"}, {"propertyName": "2033"}, {"range": "2441", "text": "1456"}, {"propertyName": "2033"}, {"range": "2442", "text": "1456"}, {"propertyName": "2033"}, {"range": "2443", "text": "1973"}, {"range": "2444", "text": "1975"}, {"range": "2445", "text": "1973"}, {"range": "2446", "text": "1975"}, {"range": "2447", "text": "1973"}, {"range": "2448", "text": "1975"}, {"range": "2449", "text": "1973"}, {"range": "2450", "text": "1975"}, {"range": "2451", "text": "1973"}, {"range": "2452", "text": "1975"}, "block", [2014, 2014], " /* empty */ ", [2157, 2157], [582, 585], "unknown", [582, 585], "never", [1129, 1132], [1129, 1132], [1162, 1165], [1162, 1165], [1209, 1212], [1209, 1212], [1344, 1347], [1344, 1347], [1862, 1865], [1862, 1865], [1875, 1878], [1875, 1878], [1894, 1897], [1894, 1897], [3317, 3320], [3317, 3320], [3330, 3333], [3330, 3333], [3349, 3352], [3349, 3352], [3726, 3729], [3726, 3729], [3774, 3777], [3774, 3777], [3938, 3941], [3938, 3941], [4155, 4158], [4155, 4158], [4481, 4484], [4481, 4484], [7349, 7352], [7349, 7352], [7846, 7849], [7846, 7849], [9324, 9327], [9324, 9327], [18024, 18027], [18024, 18027], [19907, 19910], [19907, 19910], [21951, 21954], [21951, 21954], [24282, 24285], [24282, 24285], [26425, 26428], [26425, 26428], [26438, 26441], [26438, 26441], [26457, 26460], [26457, 26460], [547, 550], [547, 550], [566, 569], [566, 569], [655, 658], [655, 658], [1384, 1425], "log", [4301, 4342], [4448, 4494], [4613, 4659], [5026, 5070], [5503, 5541], [5664, 5701], [1598, 1601], [1598, 1601], [3295, 3298], [3295, 3298], [80, 83], [80, 83], [93, 96], [93, 96], [99, 102], [99, 102], [802, 805], [802, 805], [814, 817], [814, 817], [1144, 1147], [1144, 1147], [1055, 1058], [1055, 1058], [5171, 5174], [5171, 5174], [5272, 5275], [5272, 5275], [6031, 6034], [6031, 6034], [8372, 8375], [8372, 8375], [8533, 8536], [8533, 8536], [11421, 11424], [11421, 11424], [338, 341], [338, 341], [2581, 2584], [2581, 2584], [2683, 2686], [2683, 2686], [5135, 5138], [5135, 5138], [5626, 5629], [5626, 5629], [6363, 6366], [6363, 6366], [10175, 10178], [10175, 10178], [10681, 10684], [10681, 10684], [11149, 11152], [11149, 11152], [14264, 14267], [14264, 14267], [20309, 20312], [20309, 20312], [26214, 26217], [26214, 26217], [26233, 26236], [26233, 26236], [26271, 26274], [26271, 26274], [27433, 27436], [27433, 27436], [27452, 27455], [27452, 27455], [27490, 27493], [27490, 27493], [30950, 30953], [30950, 30953], [30969, 30972], [30969, 30972], [31007, 31010], [31007, 31010], [31092, 31095], [31092, 31095], [37337, 37340], [37337, 37340], [37356, 37359], [37356, 37359], [38399, 38402], [38399, 38402], [38418, 38421], [38418, 38421], [38456, 38459], [38456, 38459], [1907, 1910], [1907, 1910], [1989, 1992], [1989, 1992], [3134, 3137], [3134, 3137], [3756, 3759], [3756, 3759], [3804, 3807], [3804, 3807], [4015, 4018], [4015, 4018], [5758, 5761], [5758, 5761], [6395, 6398], [6395, 6398], [7054, 7057], [7054, 7057], [8285, 8288], [8285, 8288], [8678, 8681], [8678, 8681], [8722, 8725], [8722, 8725], [9337, 9340], [9337, 9340], [12204, 12207], [12204, 12207], [12271, 12274], [12271, 12274], [13308, 13311], [13308, 13311], [13327, 13330], [13327, 13330], [14105, 14108], [14105, 14108], [16259, 16262], [16259, 16262], [16274, 16277], [16274, 16277], [16917, 16920], [16917, 16920], [16999, 17002], [16999, 17002], [17213, 17216], [17213, 17216], [17723, 17726], [17723, 17726], [17821, 17824], [17821, 17824], [17916, 17919], [17916, 17919], [17985, 17988], [17985, 17988], [18037, 18040], [18037, 18040], [18150, 18153], [18150, 18153], [18232, 18235], [18232, 18235], [18465, 18468], [18465, 18468], [18556, 18559], [18556, 18559], [18625, 18628], [18625, 18628], [18861, 18864], [18861, 18864], [11810, 11813], [11810, 11813], [14701, 14704], [14701, 14704], [14731, 14734], [14731, 14734], [17448, 17451], [17448, 17451], [18634, 18637], [18634, 18637], [22274, 22277], [22274, 22277], [23969, 23972], [23969, 23972], [25700, 25703], [25700, 25703], [27650, 27653], [27650, 27653], [31419, 31422], [31419, 31422], [31480, 31483], [31480, 31483], [31921, 31924], [31921, 31924], [32110, 32113], [32110, 32113], [32940, 32943], [32940, 32943], [33742, 33745], [33742, 33745], [33756, 33759], [33756, 33759], [491, 555], [1001, 1045], [4309, 4312], [4309, 4312], [7994, 7997], [7994, 7997], [9085, 9088], [9085, 9088], [9636, 9940], [1385, 1388], [1385, 1388], [1748, 1751], [1748, 1751], [681, 684], [681, 684], [702, 705], [702, 705], [728, 731], [728, 731], [782, 785], [782, 785], [4844, 4847], [4844, 4847], [10280, 10283], [10280, 10283], [10286, 10289], [10286, 10289], [553, 556], [553, 556], [823, 826], [823, 826], [1716, 1719], [1716, 1719], [12577, 12580], [12577, 12580], [12887, 12890], [12887, 12890], [16506, 16509], [16506, 16509], [16524, 16527], [16524, 16527], [16577, 16580], [16577, 16580], [16591, 16594], [16591, 16594], [16637, 16640], [16637, 16640], [1292, 1295], [1292, 1295], [1950, 1953], [1950, 1953], [1972, 1975], [1972, 1975], [4813, 4816], [4813, 4816], [4836, 4839], [4836, 4839], [4842, 4845], [4842, 4845], [4872, 4875], [4872, 4875], [6686, 6689], [6686, 6689], [9043, 9046], [9043, 9046], [9086, 9089], [9086, 9089], [10306, 10309], [10306, 10309], [187, 190], [187, 190], [269, 272], [269, 272], [2316, 2319], [2316, 2319], [2550, 2553], [2550, 2553], [3227, 3230], [3227, 3230], [3268, 3271], [3268, 3271], [4280, 4283], [4280, 4283], [4313, 4316], [4313, 4316], [2701, 2746], [3100, 3150], [3979, 4024], [4364, 4414], [5180, 5224], [5496, 5548], [6260, 6305], [6623, 6673], [7271, 7274], [7271, 7274], [7286, 7289], [7286, 7289], [7466, 7510], [7765, 7817], [287, 290], [287, 290], [306, 309], [306, 309], [4306, 4309], [4306, 4309], [8557, 8560], [8557, 8560], [3399, 3402], [3399, 3402], [4640, 4643], [4640, 4643], [5887, 5890], [5887, 5890], [2314, 2375], "type RepricingRequest = PricingOperationRequest", [1334, 1337], [1334, 1337], [2869, 2872], [2869, 2872], [3354, 3357], [3354, 3357], [3360, 3363], [3360, 3363], [4589, 4592], [4589, 4592], [5950, 5953], [5950, 5953], [5988, 5991], [5988, 5991], [8661, 8664], [8661, 8664], [8920, 8923], [8920, 8923], [17996, 17999], [17996, 17999], [18382, 18385], [18382, 18385], [18676, 18679], [18676, 18679], [18998, 19001], [18998, 19001], [19501, 19504], [19501, 19504], [155, 158], [155, 158], [602, 605], [602, 605], [1398, 1401], [1398, 1401], [2968, 2971], [2968, 2971], [3106, 3109], [3106, 3109], [4812, 4815], [4812, 4815], [4826, 4829], [4826, 4829], [6382, 6385], [6382, 6385], [7226, 7229], [7226, 7229], [7391, 7394], [7391, 7394], [7797, 7800], [7797, 7800], [814, 817], [814, 817], [581, 584], [581, 584], [924, 927], [924, 927], [1106, 1109], [1106, 1109], [3927, 3930], [3927, 3930], [3941, 3944], [3941, 3944], [5824, 5827], [5824, 5827], [5830, 5833], [5830, 5833], [6201, 6204], [6201, 6204], [6207, 6210], [6207, 6210], [7141, 7144], [7141, 7144], [485, 488], [485, 488], [3486, 3489], [3486, 3489], [3564, 3567], [3564, 3567], [3624, 3627], [3624, 3627], [5238, 5241], [5238, 5241], [5290, 5293], [5290, 5293], [7760, 7763], [7760, 7763], [8394, 8397], [8394, 8397], [9180, 9183], [9180, 9183], [9201, 9204], [9201, 9204], [12201, 12204], [12201, 12204], [14372, 14375], [14372, 14375], [17384, 17387], [17384, 17387], [22959, 22962], [22959, 22962], [24134, 24137], [24134, 24137], [805, 861], [953, 1007], [1350, 1424], [1499, 1569], [228, 231], [228, 231], [1143, 1146], [1143, 1146], [2013, 2016], [2013, 2016], [3443, 3446], [3443, 3446], [5871, 5874], [5871, 5874]]