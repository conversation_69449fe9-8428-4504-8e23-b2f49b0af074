<script setup lang="ts">
import { UpOutlined, RocketOutlined, FileTextOutlined } from '@ant-design/icons-vue'

const displayName = __DISPLAY_NAME__
const version = __VERSION__
</script>

<template>
  <div class="min-h-screen bg-gray-50 flex items-center justify-center">
    <a-card
      class="w-full max-w-lg text-center shadow-lg"
      :bordered="false"
    >
      <div class="space-y-6">
        <!-- 更新图标 -->
        <div class="flex justify-center">
          <a-avatar
            :size="80"
            class="bg-blue-500"
          >
            <template #icon>
              <UpOutlined class="text-3xl" />
            </template>
          </a-avatar>
        </div>

        <!-- 标题 -->
        <div>
          <h1 class="text-2xl font-bold text-gray-800 mb-2">
            🎉 更新成功！
          </h1>
          <p class="text-gray-600">
            感谢您更新 <strong>{{ displayName }}</strong>！❤️
          </p>
        </div>

        <!-- 版本信息 -->
        <a-alert
          type="info"
          show-icon
          :message="`新版本：${version}`"
          description="现在您可以关闭此标签页并开始使用新版本的扩展程序。"
        />

        <!-- 操作按钮 -->
        <div class="space-y-3">
          <a-button
            type="primary"
            size="large"
            block
          >
            <template #icon>
              <RocketOutlined />
            </template>
            开始使用新版本
          </a-button>

          <a-button
            size="large"
            block
          >
            <template #icon>
              <FileTextOutlined />
            </template>
            查看更新日志
          </a-button>

          <a-button
            size="large"
            block
            @click="window.close()"
          >
            关闭页面
          </a-button>
        </div>

        <!-- 开发信息 -->
        <a-divider />

        <div class="text-xs text-gray-500">
          <p>扩展程序已成功更新到最新版本</p>
        </div>
      </div>
    </a-card>
  </div>
</template>
