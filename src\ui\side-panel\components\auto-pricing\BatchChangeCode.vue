<script setup lang="ts">
import { ref } from 'vue'
import { UploadOutlined, DownloadOutlined, PlayCircleOutlined } from '@ant-design/icons-vue'

// 批量修改货号表单
const modifyItemNoForm = ref({
  uploadFile: null
})

// 上传货号表
const uploadItemNoFile = () => {
  console.log('上传货号表')
  alert('上传货号表功能已启动！')
}

// 导出店铺货号关系表
const exportItemNoRelation = () => {
  console.log('导出店铺货号关系表')
  alert('导出店铺货号关系表功能已启动！')
}

// 文件上传处理
const handleFileChange = (info: any) => {
  console.log('文件上传:', info)
}
</script>

<template>
  <div>
    <div class="space-y-6">
      <!-- 文件上传 -->
      <a-card
        title="批量修改货号"
        class="mb-6"
      >
        <a-form-item label="上传货号表">
          <a-upload
            :file-list="[]"
            :before-upload="() => false"
            @change="handleFileChange"
          >
            <a-button>
              <template #icon>
                <UploadOutlined />
              </template>
              选择文件
            </a-button>
          </a-upload>
          <div class="text-sm text-gray-500 mt-2">
            支持 Excel 格式(.xlsx, .xls)，文件大小不超过 10MB
          </div>
        </a-form-item>

        <div class="flex space-x-4 mt-6">
          <a-button 
            type="primary"
            size="large"
            @click="uploadItemNoFile"
          >
            <template #icon>
              <PlayCircleOutlined />
            </template>
            开始批量修改
          </a-button>

          <a-button 
            size="large"
            @click="exportItemNoRelation"
          >
            <template #icon>
              <DownloadOutlined />
            </template>
            导出货号关系表
          </a-button>
        </div>
      </a-card>

      <!-- 操作说明 -->
      <a-card
        title="操作说明"
        class="mb-6"
      >
        <a-alert
          type="info"
          show-icon
          message="批量修改货号"
          description="通过上传Excel文件批量修改商品货号，请确保文件格式正确。"
        />
        
        <div class="mt-4 space-y-2 text-sm text-gray-600">
          <div><strong>文件格式要求：</strong></div>
          <div>• 第一列：原货号</div>
          <div>• 第二列：新货号</div>
          <div>• 第一行为标题行，从第二行开始为数据</div>
          <div>• 支持 .xlsx 和 .xls 格式</div>
        </div>
      </a-card>

      <!-- 示例表格 -->
      <a-card title="文件格式示例">
        <a-table
          :columns="[
            { title: '原货号', dataIndex: 'oldCode', key: 'oldCode' },
            { title: '新货号', dataIndex: 'newCode', key: 'newCode' }
          ]"
          :data-source="[
            { key: '1', oldCode: 'ABC123', newCode: 'XYZ789' },
            { key: '2', oldCode: 'DEF456', newCode: 'UVW012' }
          ]"
          :pagination="false"
          size="small"
        />
      </a-card>
    </div>
  </div>
</template>
