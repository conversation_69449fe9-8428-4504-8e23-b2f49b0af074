<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  SettingOutlined,
  UserOutlined,
  BulbOutlined,
  ShopOutlined,
  DatabaseOutlined,
  SecurityScanOutlined,
  NotificationOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 扩展设置数据
const settings = ref({
  // 界面设置
  theme: 'light', // light, dark, auto
  language: 'zh-CN',

  // 店铺设置
  shops: [] as any[],
  defaultShop: '',

  // 采集设置
  collection: {
    autoTranslate: true,
    imageOptimization: true,
    priceMarkup: 1.2,
    maxConcurrent: 5
  },

  // 通知设置
  notifications: {
    priceChange: true,
    stockAlert: true,
    orderUpdate: true,
    systemUpdate: true
  },

  // 安全设置
  security: {
    autoLogout: 30, // 分钟
    dataBackup: true,
    encryptData: true
  }
})

// 加载设置
const loadSettings = async () => {
  try {
    const result = await chrome.storage.sync.get(['hjdmj_settings'])
    if (result.hjdmj_settings) {
      Object.assign(settings.value, result.hjdmj_settings)
    }
  } catch (error) {
    console.error('加载设置失败:', error)
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    await chrome.storage.sync.set({ hjdmj_settings: settings.value })
    message.success('设置已保存')
  } catch (error) {
    console.error('保存设置失败:', error)
    message.error('保存设置失败')
  }
}

// 重置设置
const resetSettings = () => {
  settings.value = {
    theme: 'light',
    language: 'zh-CN',
    shops: [],
    defaultShop: '',
    collection: {
      autoTranslate: true,
      imageOptimization: true,
      priceMarkup: 1.2,
      maxConcurrent: 5
    },
    notifications: {
      priceChange: true,
      stockAlert: true,
      orderUpdate: true,
      systemUpdate: true
    },
    security: {
      autoLogout: 30,
      dataBackup: true,
      encryptData: true
    }
  }
  message.success('设置已重置')
}

// 添加店铺
const addShop = () => {
  // 这里可以打开添加店铺的对话框
  message.info('请在侧边栏中绑定店铺')
}

// 移除店铺
const removeShop = (shopId: string) => {
  settings.value.shops = settings.value.shops.filter(shop => shop.id !== shopId)
  if (settings.value.defaultShop === shopId) {
    settings.value.defaultShop = settings.value.shops[0]?.id || ''
  }
  saveSettings()
}

// 导出设置
const exportSettings = () => {
  const dataStr = JSON.stringify(settings.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `hjdmj_settings_${new Date().toISOString().split('T')[0]}.json`
  link.click()
  URL.revokeObjectURL(url)
  message.success('设置已导出')
}

// 导入设置
const importSettings = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const importedSettings = JSON.parse(e.target?.result as string)
      Object.assign(settings.value, importedSettings)
      saveSettings()
      message.success('设置已导入')
    } catch (error) {
      message.error('导入失败：文件格式错误')
    }
  }
  reader.readAsText(file)
}

onMounted(() => {
  loadSettings()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <!-- 页面标题 -->
      <a-card
        class="mb-6 shadow-sm"
        :bordered="false"
      >
        <template #title>
          <a-space :size="8">
            <SettingOutlined />
            <span class="text-xl font-semibold">胡建大卖家 - 扩展设置</span>
          </a-space>
        </template>

        <p class="text-gray-600">
          配置您的Temu店铺管理扩展，优化您的使用体验。所有设置将自动同步到云端。
        </p>
      </a-card>

      <!-- 设置表单 -->
      <div class="space-y-6">
        <!-- 界面设置 -->
        <a-card
          title="界面设置"
          class="shadow-sm"
          :bordered="false"
        >
          <template #extra>
            <BulbOutlined />
          </template>

          <p class="text-gray-600 mb-4">自定义扩展的外观和语言设置。</p>

          <a-form
            layout="vertical"
            @finish="saveSettings"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="主题模式">
                  <a-select
                    v-model:value="settings.theme"
                    @change="saveSettings"
                  >
                    <a-select-option value="light">浅色主题</a-select-option>
                    <a-select-option value="dark">深色主题</a-select-option>
                    <a-select-option value="auto">跟随系统</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="语言设置">
                  <a-select
                    v-model:value="settings.language"
                    @change="saveSettings"
                  >
                    <a-select-option value="zh-CN">简体中文</a-select-option>
                    <a-select-option value="zh-TW">繁体中文</a-select-option>
                    <a-select-option value="en-US">English</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 店铺管理 -->
        <a-card
          title="店铺管理"
          class="shadow-sm"
          :bordered="false"
        >
          <template #extra>
            <ShopOutlined />
          </template>

          <p class="text-gray-600 mb-4">管理您的Temu店铺账号，设置默认店铺。</p>

          <a-form layout="vertical">
            <a-form-item label="已绑定店铺">
              <div
                v-if="settings.shops.length === 0"
                class="text-center py-8 text-gray-500"
              >
                <ShopOutlined class="text-4xl mb-2" />
                <p>暂未绑定任何店铺</p>
                <a-button
                  type="primary"
                  class="mt-2"
                  @click="addShop"
                >
                  绑定店铺
                </a-button>
              </div>
              <a-space
                direction="vertical"
                :size="12"
                class="w-full"
              >
                <a-card
                  v-for="shop in settings.shops"
                  :key="shop.id"
                  size="small"
                  :bordered="true"
                >
                  <a-space
                    class="w-full"
                    align="center"
                    :size="16"
                  >
                    <a-space
                      align="center"
                      :size="12"
                    >
                      <a-avatar
                        :src="shop.avatar"
                        :size="40"
                      >
                        {{ shop.name.charAt(0) }}
                      </a-avatar>
                      <div>
                        <p class="font-medium">{{ shop.name }}</p>
                        <p class="text-sm text-gray-500">ID: {{ shop.id }}</p>
                      </div>
                      <a-tag
                        v-if="shop.id === settings.defaultShop"
                        color="blue"
                      >
                        默认店铺
                      </a-tag>
                    </a-space>
                    <div class="flex-1"></div>
                    <a-space :size="8">
                      <a-button
                        v-if="shop.id !== settings.defaultShop"
                        size="small"
                        @click="settings.defaultShop = shop.id; saveSettings()"
                      >
                        设为默认
                      </a-button>
                      <a-button
                        size="small"
                        danger
                        @click="removeShop(shop.id)"
                      >
                        移除
                      </a-button>
                    </a-space>
                  </a-space>
                </a-card>
                <a-button
                  type="dashed"
                  block
                  @click="addShop"
                >
                  + 添加更多店铺
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 采集设置 -->
        <a-card
          title="商品采集设置"
          class="shadow-sm"
          :bordered="false"
        >
          <template #extra>
            <DatabaseOutlined />
          </template>

          <p class="text-gray-600 mb-4">配置商品采集和处理的相关参数。</p>

          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="自动翻译">
                  <a-switch
                    v-model:checked="settings.collection.autoTranslate"
                    checked-children="开启"
                    un-checked-children="关闭"
                    @change="saveSettings"
                  />
                  <p class="text-xs text-gray-500 mt-1">自动翻译商品标题和描述</p>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="图片优化">
                  <a-switch
                    v-model:checked="settings.collection.imageOptimization"
                    checked-children="开启"
                    un-checked-children="关闭"
                    @change="saveSettings"
                  />
                  <p class="text-xs text-gray-500 mt-1">自动压缩和优化商品图片</p>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="价格倍数">
                  <a-input-number
                    v-model:value="settings.collection.priceMarkup"
                    :min="1"
                    :max="10"
                    :step="0.1"
                    class="w-full"
                    @change="saveSettings"
                  />
                  <p class="text-xs text-gray-500 mt-1">采集价格的倍数调整</p>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="并发数量">
                  <a-input-number
                    v-model:value="settings.collection.maxConcurrent"
                    :min="1"
                    :max="20"
                    class="w-full"
                    @change="saveSettings"
                  />
                  <p class="text-xs text-gray-500 mt-1">同时处理的商品数量</p>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 通知设置 -->
        <a-card
          title="通知设置"
          class="shadow-sm"
          :bordered="false"
        >
          <template #extra>
            <NotificationOutlined />
          </template>

          <p class="text-gray-600 mb-4">管理各种通知的开启和关闭。</p>

          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="价格变动通知">
                  <a-switch
                    v-model:checked="settings.notifications.priceChange"
                    checked-children="开启"
                    un-checked-children="关闭"
                    @change="saveSettings"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="库存预警">
                  <a-switch
                    v-model:checked="settings.notifications.stockAlert"
                    checked-children="开启"
                    un-checked-children="关闭"
                    @change="saveSettings"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="订单更新">
                  <a-switch
                    v-model:checked="settings.notifications.orderUpdate"
                    checked-children="开启"
                    un-checked-children="关闭"
                    @change="saveSettings"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="系统更新">
                  <a-switch
                    v-model:checked="settings.notifications.systemUpdate"
                    checked-children="开启"
                    un-checked-children="关闭"
                    @change="saveSettings"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>

        <!-- 安全设置 -->
        <a-card
          title="安全设置"
          class="shadow-sm"
          :bordered="false"
        >
          <template #extra>
            <SecurityScanOutlined />
          </template>

          <p class="text-gray-600 mb-4">配置账户安全和数据保护选项。</p>

          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="自动登出时间（分钟）">
                  <a-input-number
                    v-model:value="settings.security.autoLogout"
                    :min="5"
                    :max="480"
                    class="w-full"
                    @change="saveSettings"
                  />
                  <p class="text-xs text-gray-500 mt-1">无操作自动登出时间</p>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据备份">
                  <a-switch
                    v-model:checked="settings.security.dataBackup"
                    checked-children="开启"
                    un-checked-children="关闭"
                    @change="saveSettings"
                  />
                  <p class="text-xs text-gray-500 mt-1">自动备份重要数据</p>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="数据加密">
              <a-switch
                v-model:checked="settings.security.encryptData"
                checked-children="开启"
                un-checked-children="关闭"
                @change="saveSettings"
              />
              <p class="text-xs text-gray-500 mt-1">加密存储敏感数据</p>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 数据管理 -->
        <a-card
          title="数据管理"
          class="shadow-sm"
          :bordered="false"
        >
          <p class="text-gray-600 mb-4">导入导出设置，重置配置。</p>

          <a-form layout="vertical">
            <a-form-item label="设置备份">
              <div class="flex space-x-2">
                <a-button @click="exportSettings">
                  导出设置
                </a-button>
                <a-upload
                  :show-upload-list="false"
                  :before-upload="() => false"
                  accept=".json"
                  @change="importSettings"
                >
                  <a-button>
                    导入设置
                  </a-button>
                </a-upload>
              </div>
              <p class="text-xs text-gray-500 mt-1">备份或恢复您的扩展设置</p>
            </a-form-item>

            <a-form-item label="重置设置">
              <a-popconfirm
                title="确定要重置所有设置吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="resetSettings"
              >
                <a-button danger>
                  重置所有设置
                </a-button>
              </a-popconfirm>
              <p class="text-xs text-gray-500 mt-1">将所有设置恢复为默认值</p>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 关于信息 -->
        <a-card
          class="shadow-sm"
          :bordered="false"
        >
          <a-alert
            type="info"
            show-icon
            message="胡建大卖家"
            description="专业的Temu店铺管理工具，助力您的电商事业蒸蒸日上。如有问题请联系客服支持。"
          />
        </a-card>
      </div>
    </div>
  </div>
</template>
