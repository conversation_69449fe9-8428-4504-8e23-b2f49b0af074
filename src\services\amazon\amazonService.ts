/**
 * Amazon服务统一入口
 * 整合所有Amazon相关功能，提供给UI层使用
 */

import { AmazonDataService } from './amazonDataService'
import { AmazonCollectionController } from './amazonCollectionController'
import type {
  AmazonProductData,
  DianxiaomiProductData,
  AmazonServiceConfig,
  AmazonServiceResult,
  BatchProcessResult,
  CollectionConfig,
  CollectionResult
} from './types'

// 接口定义已移动到 types.ts 文件中

/**
 * Amazon服务统一入口类
 * 提供给UI层的主要接口
 */
export class AmazonService {
  private static instance: AmazonService
  private dataService: AmazonDataService
  private collectionController: AmazonCollectionController

  private constructor() {
    this.dataService = new AmazonDataService()
    this.collectionController = AmazonCollectionController.getInstance()
  }

  static getInstance(): AmazonService {
    if (!AmazonService.instance) {
      AmazonService.instance = new AmazonService()
    }
    return AmazonService.instance
  }

  /**
   * 处理单个Amazon商品
   * 主要入口函数，提供给UI层使用
   */
  async processSingleProduct(
    productUrl: string,
    config?: Partial<AmazonServiceConfig>
  ): Promise<AmazonServiceResult> {
    const startTime = Date.now()
    
    try {
      console.info('[AmazonService] 开始处理Amazon商品:', productUrl)

      // 转换配置格式
      const collectionConfig = this.convertToCollectionConfig(config)

      // 使用统一的采集控制器
      const result = await this.collectionController.collectSingleProduct(
        productUrl,
        collectionConfig
      )

      if (!result.success) {
        return {
          success: false,
          error: result.error,
          warnings: result.warnings,
          metadata: {
            processingTime: Date.now() - startTime
          }
        }
      }

      return {
        success: true,
        data: {
          amazonData: result.productData!.spuData,
          dianxiaomiData: result.productData!.dianxiaomiData,
          processedImages: result.productData!.processedImages
        },
        warnings: result.warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          imageProcessingTime: result.metadata.imageProcessingTime,
          pushTime: result.metadata.pushTime
        }
      }

    } catch (error) {
      console.error('[AmazonService] 处理商品失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '处理失败',
        metadata: {
          processingTime: Date.now() - startTime
        }
      }
    }
  }

  /**
   * 批量处理Amazon商品
   */
  async processBatchProducts(
    productUrls: string[],
    config?: Partial<AmazonServiceConfig>,
    onProgress?: (current: number, total: number, result?: AmazonServiceResult) => void
  ): Promise<BatchProcessResult> {
    const startTime = Date.now()

    try {
      console.info('[AmazonService] 开始批量处理Amazon商品:', productUrls.length, '个')

      const collectionConfig = this.convertToCollectionConfig(config)

      const batchResult = await this.collectionController.batchCollectProducts(
        productUrls,
        collectionConfig,
        (current, total, collectionResult) => {
          if (collectionResult) {
            const serviceResult: AmazonServiceResult = {
              success: collectionResult.success,
              data: collectionResult.productData ? {
                amazonData: collectionResult.productData.spuData,
                dianxiaomiData: collectionResult.productData.dianxiaomiData,
                processedImages: collectionResult.productData.processedImages
              } : undefined,
              error: collectionResult.error,
              warnings: collectionResult.warnings,
              metadata: {
                processingTime: collectionResult.metadata.totalTime,
                imageProcessingTime: collectionResult.metadata.imageProcessingTime,
                pushTime: collectionResult.metadata.pushTime
              }
            }
            onProgress?.(current, total, serviceResult)
          } else {
            onProgress?.(current, total)
          }
        }
      )

      const results: AmazonServiceResult[] = batchResult.results.map(result => ({
        success: result.success,
        data: result.productData ? {
          amazonData: result.productData.spuData,
          dianxiaomiData: result.productData.dianxiaomiData,
          processedImages: result.productData.processedImages
        } : undefined,
        error: result.error,
        warnings: result.warnings,
        metadata: {
          processingTime: result.metadata.totalTime,
          imageProcessingTime: result.metadata.imageProcessingTime,
          pushTime: result.metadata.pushTime
        }
      }))

      return {
        success: batchResult.success,
        results,
        summary: batchResult.summary,
        totalTime: Date.now() - startTime
      }

    } catch (error) {
      console.error('[AmazonService] 批量处理失败:', error)
      return {
        success: false,
        results: [],
        summary: {
          total: productUrls.length,
          successful: 0,
          failed: productUrls.length
        },
        totalTime: Date.now() - startTime
      }
    }
  }

  /**
   * 获取Amazon商品价格信息
   */
  async getProductPrice(extCode: string) {
    return await this.dataService.getAmazonPrice(extCode)
  }

  /**
   * 批量获取Amazon商品价格
   */
  async getBatchProductPrices(extCodes: string[]) {
    return await this.dataService.batchGetAmazonPrices(extCodes)
  }

  /**
   * 验证是否为Amazon商品
   */
  isAmazonProduct(extCode: string): boolean {
    return this.dataService.isAmazonProduct(extCode)
  }

  /**
   * 从extCode提取ASIN
   */
  extractASIN(extCode: string): string | null {
    return this.dataService.extractASINFromExtCode(extCode)
  }

  /**
   * 构建Amazon商品URL
   */
  buildAmazonURL(extCode: string): string | null {
    return this.dataService.buildAmazonURL(extCode)
  }

  /**
   * 转换配置格式
   */
  private convertToCollectionConfig(config?: Partial<AmazonServiceConfig>): Partial<CollectionConfig> {
    if (!config) return {}

    return {
      enableImageProcessing: config.collection?.enableImageProcessing,
      enableAutoPush: config.collection?.enableAutoPush || config.push?.autoPush,
      imageProcessingOptions: {
        maxImages: config.collection?.maxImages || 8,
        targetSize: 800,
        quality: config.collection?.imageQuality || 0.85
      },
      pushOptions: {
        batchSize: config.push?.batchSize || 5,
        delayBetweenPush: config.push?.delayBetweenPush || 2000
      }
    }
  }
}

// 导出单例实例
export const amazonService = AmazonService.getInstance()
export default amazonService

// 导出类型
export type {
  AmazonProductData,
  DianxiaomiProductData,
  AmazonServiceConfig,
  AmazonServiceResult,
  BatchProcessResult
}
