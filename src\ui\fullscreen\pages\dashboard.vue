<script setup lang="ts">
import { ref, computed, onMounted, h } from "vue"
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { useDashboard } from "../../../composables/useDashboard"
import { useShopBinding } from "../../../composables/useShopBinding"
import { getSiteOptions } from '../../../config/temuSites'

// 使用 Dashboard 数据管理
const {
  isLoading,
  products,
  tabs,
  activeTab,
  pageNum,
  pageSize,
  total,
  searchForm,
  currentShopInfo,
  mallId,
  refreshData,
  fetchProducts,
  searchProducts,
  switchTab,
  handlePageChange,
  jumpToSource,
  selectedProductIds,
  isAllSelected,
  isIndeterminate,
  handleSelectAll,
  handleSelectProduct,
  isProductSelected,
  getSelectedProducts,
  clearSelection,
  batchPriceConfirm,
  fetchAmazonPrice,
  batchFetchAmazonPrices,
  buildQueryParams
} = useDashboard()

// 使用店铺绑定状态
const { shopBinding } = useShopBinding()

// 站点选项
const siteOptions = computed(() => getSiteOptions().map(site => ({
  value: site.code.toLowerCase(),
  label: `${site.label}站`
})))

// 商品ID类型选项
const idTypeOptions = [
  { value: "SKC", label: "SKC" },
  { value: "SPU", label: "SPU" },
  { value: "SKU", label: "SKU" },
]

// 当前店铺信息
const currentShop = computed(() => {
  if (shopBinding.temuSiteInfo) {
    return {
      shopId: shopBinding.temuSiteInfo.mallId,
      label: `${shopBinding.temuSiteInfo.mallName} (${shopBinding.temuSiteInfo.isSemiManagedMall ? '半托' : '全托'})`,
      url: 'https://seller.kuajingmaihuo.com'
    }
  }
  return {
    shopId: 'default',
    label: 'Temu 店铺',
    url: 'https://seller.kuajingmaihuo.com'
  }
})

// 搜索表单展开状态
const searchExpanded = ref(false)

// 批量操作
const selectedCount = computed(() => selectedProductIds.value.length)

const batchActions = [
  { key: 'price-confirm', label: '批量核价', icon: '💰' },
  { key: 'amazon-price', label: 'Amazon价格', icon: '🛒' },
  { key: 'sync-stock', label: '同步库存', icon: '📦' },
  { key: 'export', label: '导出数据', icon: '📊' }
]

// 处理批量操作
const handleBatchAction = async (action: string) => {
  const selected = getSelectedProducts()
  if (selected.length === 0) {
    return
  }

  switch (action) {
    case 'price-confirm':
      await batchPriceConfirm(selected)
      break
    case 'amazon-price':
      await batchFetchAmazonPrices()
      break
    case 'sync-stock':
      // TODO: 实现批量同步库存
      break
    case 'export':
      // TODO: 实现导出功能
      break
  }
}

// 表格列定义 - 简化版
const columns = [
  {
    title: '',
    key: 'selection',
    width: 50,
    fixed: 'left'
  },
  {
    title: '产品信息',
    key: 'product',
    width: 400,
    fixed: 'left'
  },
  {
    title: '价格信息',
    key: 'price',
    width: 200
  },
  {
    title: 'SKU信息',
    key: 'sku',
    width: 200
  },
  {
    title: '库存状态',
    key: 'stock',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 组件挂载时加载数据
onMounted(() => {
  refreshData()
})
</script>

<template>
  <div class="fullscreen-dashboard">
    <!-- 工具栏 -->
    <div class="fullscreen-toolbar">
      <div class="toolbar-left">
        <h1 style="margin: 0; font-size: 24px; color: #333;">
          <span>📊</span>
          <span>商品管理工作台</span>
        </h1>
        <a-tag color="blue">{{ total }} 个商品</a-tag>
        <a-tag v-if="selectedCount > 0" color="orange">
          已选择 {{ selectedCount }} 个
        </a-tag>
      </div>

      <div class="toolbar-right">
        <!-- 店铺选择 -->
        <a-select
          v-model:value="mallId"
          style="min-width: 200px"
          placeholder="选择店铺"
        >
          <a-select-option :value="currentShop.shopId">
            {{ currentShop.label }}
          </a-select-option>
        </a-select>

        <!-- 刷新按钮 -->
        <a-button
          type="primary"
          :icon="h(ReloadOutlined)"
          :loading="isLoading"
          @click="refreshData"
        >
          刷新数据
        </a-button>

        <!-- 搜索切换 -->
        <a-button
          :type="searchExpanded ? 'primary' : 'default'"
          :icon="h(SearchOutlined)"
          @click="searchExpanded = !searchExpanded"
        >
          {{ searchExpanded ? '收起搜索' : '展开搜索' }}
        </a-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div v-if="searchExpanded" class="fullscreen-search">
      <a-form
        :model="searchForm"
        layout="inline"
        @finish="searchProducts"
      >
        <div class="search-form-row">
          <div class="search-form-item">
            <label>站点</label>
            <a-select
              v-model:value="searchForm.site"
              placeholder="选择站点"
              style="width: 120px"
              allow-clear
            >
              <a-select-option
                v-for="site in siteOptions"
                :key="site.value"
                :value="site.value"
              >
                {{ site.label }}
              </a-select-option>
            </a-select>
          </div>

          <div class="search-form-item">
            <label>商品ID</label>
            <a-input-group compact>
              <a-select
                v-model:value="searchForm.productIdType"
                style="width: 80px"
              >
                <a-select-option
                  v-for="type in idTypeOptions"
                  :key="type.value"
                  :value="type.value"
                >
                  {{ type.label }}
                </a-select-option>
              </a-select>
              <a-input
                v-model:value="searchForm.productId"
                placeholder="多个ID用空格分隔"
                style="width: 200px"
                allow-clear
              />
            </a-input-group>
          </div>

          <div class="search-form-item">
            <label>货号</label>
            <a-input
              v-model:value="searchForm.extCode"
              placeholder="多个货号用空格分隔"
              style="width: 200px"
              allow-clear
            />
          </div>

          <div class="search-form-item">
            <label>库存筛选</label>
            <a-input
              v-model:value="searchForm.stockFilter"
              placeholder="≤ 库存数量"
              type="number"
              style="width: 120px"
            />
          </div>

          <div class="search-form-item">
            <label>毛利率</label>
            <a-input-group compact style="width: 160px">
              <a-select
                v-model:value="searchForm.profitRateOperator"
                style="width: 60px"
              >
                <a-select-option value="gte">≥</a-select-option>
                <a-select-option value="lte">≤</a-select-option>
              </a-select>
              <a-input
                v-model:value="searchForm.profitRateFilter"
                placeholder="毛利率%"
                type="number"
                style="width: 100px"
              />
            </a-input-group>
          </div>

          <div class="search-form-item">
            <label>&nbsp;</label>
            <div class="button-group">
              <a-button type="primary" html-type="submit" class="action-button">
                <span>🔍</span>
                <span>查询</span>
              </a-button>
              <a-button @click="searchForm = {}">
                重置
              </a-button>
            </div>
          </div>
        </div>
      </a-form>
    </div>

    <!-- 主内容卡片 -->
    <div class="fullscreen-card">
      <!-- 批量操作栏 -->
      <div v-if="selectedCount > 0" class="batch-actions-bar">
        <div class="batch-info">
          <span>已选择 {{ selectedCount }} 个商品</span>
          <a-button type="link" size="small" @click="clearSelection">
            清除选择
          </a-button>
        </div>
        <div class="batch-buttons">
          <a-button
            v-for="action in batchActions"
            :key="action.key"
            type="primary"
            ghost
            size="small"
            @click="handleBatchAction(action.key)"
          >
            <span>{{ action.icon }}</span>
            <span>{{ action.label }}</span>
          </a-button>
        </div>
      </div>

      <!-- 标签页 -->
      <a-tabs
        v-model:active-key="activeTab"
        class="fullscreen-tabs"
        @change="switchTab"
      >
        <a-tab-pane
          v-for="tab in tabs"
          :key="tab.key"
          :tab="`${tab.label} ${tab.count > 0 ? '(' + tab.count + ')' : ''}`"
        >
          <!-- 表格 -->
          <div class="table-container">
            <a-table
              :columns="columns"
              :data-source="products"
              :loading="isLoading"
              :pagination="{
                current: pageNum,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: handlePageChange,
                onShowSizeChange: (current, size) => {
                  pageSize = size
                  handlePageChange(current, size)
                }
              }"
              :scroll="{ x: 1200, y: 'calc(100vh - 400px)' }"
              size="small"
              class="fullscreen-table"
              :row-selection="{
                selectedRowKeys: selectedProductIds,
                onChange: (keys) => selectedProductIds = keys,
                onSelectAll: handleSelectAll,
                getCheckboxProps: (record) => ({
                  disabled: false,
                  name: record.id,
                }),
              }"
            >
              <!-- 选择列 -->
              <template #bodyCell="{ column, record, index }">
                <!-- 产品信息列 -->
                <template v-if="column.key === 'product'">
                  <div class="product-info">
                    <div class="product-image">
                      <a-image
                        v-if="record.img"
                        :src="record.img"
                        :width="60"
                        :height="60"
                        :preview="false"
                        fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                      />
                      <div v-else class="no-image">📦</div>
                    </div>
                    <div class="product-details">
                      <div class="product-title">{{ record.title || '无标题' }}</div>
                      <div class="product-meta">
                        <a-tag size="small" color="blue">{{ record.id }}</a-tag>
                        <a-tag v-if="record.sku?.itemNo" size="small" color="green">
                          {{ record.sku.itemNo }}
                        </a-tag>
                      </div>
                    </div>
                  </div>
                </template>

                <!-- 价格信息列 -->
                <template v-else-if="column.key === 'price'">
                  <div class="price-info">
                    <div v-if="record.priceInfo">
                      <div class="price-row">
                        <span class="price-label">USD:</span>
                        <span class="price-value">${{ record.priceInfo.usdPrice || '0.00' }}</span>
                      </div>
                      <div class="price-row">
                        <span class="price-label">成本:</span>
                        <span class="price-value">¥{{ (parseFloat(record.priceInfo.usdPrice || '0') * 7.2).toFixed(2) }}</span>
                      </div>
                      <div class="price-row">
                        <span class="price-label">毛利:</span>
                        <span class="price-value profit">{{ ((84.07 - parseFloat(record.priceInfo.usdPrice || '0') * 7.2) / 84.07 * 100).toFixed(1) }}%</span>
                      </div>
                    </div>
                    <div v-else class="no-price">
                      <span>暂无价格</span>
                    </div>
                  </div>
                </template>

                <!-- SKU信息列 -->
                <template v-else-if="column.key === 'sku'">
                  <div class="sku-info">
                    <div v-if="record.sku">
                      <div class="sku-row">
                        <span class="sku-label">颜色:</span>
                        <span class="sku-value">{{ record.sku.color || '-' }}</span>
                      </div>
                      <div class="sku-row">
                        <span class="sku-label">尺寸:</span>
                        <span class="sku-value">{{ record.sku.size || '-' }}</span>
                      </div>
                      <div class="sku-row">
                        <span class="sku-label">规格:</span>
                        <span class="sku-value">{{ record.sku.spec || '-' }}</span>
                      </div>
                    </div>
                    <div v-else>
                      <span>无SKU信息</span>
                    </div>
                  </div>
                </template>

                <!-- 库存状态列 -->
                <template v-else-if="column.key === 'stock'">
                  <div class="stock-info">
                    <div v-if="record.priceInfo">
                      <div class="stock-number">{{ record.priceInfo.stock || 0 }}</div>
                      <div class="stock-status">
                        <span
                          :class="[
                            'status-indicator',
                            record.priceInfo.stock > 10 ? 'status-success' :
                            record.priceInfo.stock > 0 ? 'status-warning' : 'status-error'
                          ]"
                        >
                          {{ record.priceInfo.stock > 10 ? '充足' : record.priceInfo.stock > 0 ? '偏低' : '缺货' }}
                        </span>
                      </div>
                    </div>
                    <div v-else>
                      <span class="status-indicator status-error">未知</span>
                    </div>
                  </div>
                </template>

                <!-- 操作列 -->
                <template v-else-if="column.key === 'actions'">
                  <div class="action-buttons">
                    <a-button
                      type="link"
                      size="small"
                      @click="fetchAmazonPrice(record)"
                    >
                      🔄 更新价格
                    </a-button>
                    <a-button
                      type="link"
                      size="small"
                      @click="jumpToSource(record)"
                    >
                      🔗 查看货源
                    </a-button>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<style scoped>
/* 批量操作栏 */
.batch-actions-bar {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  padding: 12px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1976d2;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

/* 表格容器 */
.table-container {
  padding: 0;
}

/* 产品信息样式 */
.product-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.product-image {
  flex-shrink: 0;
}

.no-image {
  width: 60px;
  height: 60px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 24px;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-meta {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

/* 价格信息样式 */
.price-info {
  font-size: 12px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.price-label {
  color: #666;
  min-width: 40px;
}

.price-value {
  font-weight: 500;
}

.price-value.profit {
  color: #52c41a;
}

.no-price {
  color: #999;
  font-style: italic;
}

/* SKU信息样式 */
.sku-info {
  font-size: 12px;
}

.sku-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.sku-label {
  color: #666;
  min-width: 40px;
}

.sku-value {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100px;
}

/* 库存信息样式 */
.stock-info {
  text-align: center;
}

.stock-number {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stock-status {
  font-size: 12px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 标签页样式 */
.fullscreen-tabs {
  background: white;
}

.fullscreen-tabs :deep(.ant-tabs-content-holder) {
  padding: 0;
}

.fullscreen-tabs :deep(.ant-tabs-tabpane) {
  padding: 0;
}
</style>
