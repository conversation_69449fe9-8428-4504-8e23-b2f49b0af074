/**
 * Temu配置中心
 * 
 * 功能：
 * - 统一管理所有Temu相关的URL、端点和配置
 * - 定义缓存策略和过期时间
 * - 提供环境配置和常量定义
 * 
 * 依赖关系：
 * - 无依赖，被其他Temu模块引用
 * 
 * 使用场景：
 * - temu-auth.ts: 使用缓存配置和键名
 * - temu-api.ts: 使用API端点和默认配置
 * - temu-unified.ts: 使用拦截器配置
 */

// Temu API端点配置
export const TEMU_ENDPOINTS = {
  // 基础URL
  BASE_URL: 'https://seller.kuajingmaihuo.com',
  
  // API端点
  USER_INFO: '/bg/quiet/api/mms/userInfo',
  TODO_COUNT: '/marvel-supplier/api/xmen/select/queryTodoCount',
  PRODUCT_LIST: '/marvel-mms/cn/api/kiana/xmen/select/searchForSemiSupplier',
  OFFICIAL_PRICE: '/marvel-mms/cn/api/kiana/magnus/price/bargain-no-bom/batch/info/query',
  
  // 完整URL（用于直接调用）
  get USER_INFO_URL() { return this.BASE_URL + this.USER_INFO },
  get TODO_COUNT_URL() { return this.BASE_URL + this.TODO_COUNT },
  get PRODUCT_LIST_URL() { return this.BASE_URL + this.PRODUCT_LIST },
  get OFFICIAL_PRICE_URL() { return this.BASE_URL + this.OFFICIAL_PRICE }
}

// 缓存配置
export const TEMU_CACHE_CONFIG = {
  // 缓存过期时间（毫秒）
  TTL: {
    ANTI_CONTENT: 30 * 60 * 1000,    // 30分钟
    MALL_ID: 60 * 60 * 1000,         // 60分钟
    USER_INFO: 10 * 60 * 1000,       // 10分钟
    AUTH_HEADERS: 15 * 60 * 1000     // 15分钟
  },
  
  // localStorage键名
  KEYS: {
    // anti-content相关
    ANTI_CONTENT: 'temu_anti_content',
    ANTI_CONTENT_EXPIRY: 'temu_anti_content_expiry',
    ANTI_CONTENT_SOURCE: 'temu_anti_content_source',
    
    // mallId相关
    MALL_ID: 'temu_mall_id',
    MALL_ID_EXPIRY: 'temu_mall_id_expiry',
    MALL_ID_SOURCE: 'temu_mall_id_source',
    
    // 用户信息
    USER_INFO: 'temu_user_info',
    USER_INFO_EXPIRY: 'temu_user_info_expiry',
    
    // 其他认证头部
    AUTH_HEADERS: 'temu_auth_headers',
    AUTH_HEADERS_EXPIRY: 'temu_auth_headers_expiry',
    
    // 兼容旧版本的键名
    LEGACY_ANTI_CONTENT: 'ultimate_anti_content',
    LEGACY_MALL_ID: 'ultimate_mall_id',
    LEGACY_CACHED_ANTI_CONTENT: 'temu_cached_anti_content',
    LEGACY_CACHED_MALL_ID: 'temu_cached_mall_id',

    // page-interceptor.js使用的键名（当前版本）
    INTERCEPTOR_ANTI_CONTENT: 'temu_cs_anti_content',
    INTERCEPTOR_MALL_ID: 'temu_cs_mall_id',
    INTERCEPTOR_AUTHORIZATION: 'temu_cs_authorization',
    INTERCEPTOR_X_REQUESTED_WITH: 'temu_cs_x_requested_with',
    INTERCEPTOR_X_CSRF_TOKEN: 'temu_cs_x_csrf_token',
    INTERCEPTOR_SESSION_ID: 'temu_cs_session_id'
  }
}

// 网络拦截配置
export const TEMU_INTERCEPTOR_CONFIG = {
  // 需要拦截的域名
  TARGET_DOMAINS: [
    'seller.kuajingmaihuo.com',
    'seller.temu.com',
    'seller-cn.temu.com',
    'agentseller.temu.com',
    'agentseller-us.temu.com'
  ],
  
  // anti-content的可能键名变体
  ANTI_CONTENT_KEYS: [
    'anti-content',
    'Anti-Content', 
    'ANTI-CONTENT',
    'antiContent',
    'anti_content',
    'Anti_Content',
    'ANTI_CONTENT'
  ],
  
  // mallId的可能键名变体
  MALL_ID_KEYS: [
    'mallid',
    'mallId',
    'MallId',
    'MALLID',
    'mall_id',
    'Mall_Id',
    'Mall-Id'
  ],
  
  // 其他需要缓存的认证头部
  AUTH_HEADER_KEYS: [
    'authorization',
    'x-requested-with',
    'x-csrf-token',
    'session-id',
    'x-session-id'
  ]
}

// 默认请求头配置
export const TEMU_DEFAULT_HEADERS = {
  'Accept': '*/*',
  'Accept-Language': 'zh-CN,zh;q=0.9',
  'Content-Type': 'application/json',
  'Cache-Control': 'max-age=0',
  'Origin': 'https://seller.kuajingmaihuo.com',
  'Referer': 'https://seller.kuajingmaihuo.com/',
  'Sec-Ch-Ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
  'Sec-Ch-Ua-Mobile': '?0',
  'Sec-Ch-Ua-Platform': '"Windows"',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-origin',
  'Priority': 'u=1, i'
}

// 标签页查询配置
export const TEMU_TAB_CONFIG = {
  // 查询Temu标签页的URL模式
  URL_PATTERNS: [
    '*://seller.kuajingmaihuo.com/*',
    '*://seller.temu.com/*',
    '*://seller-cn.temu.com/*',
    '*://agentseller.temu.com/*',
    '*://agentseller-us.temu.com/*'
  ],
  
  // 检查是否为Temu域名的函数
  isTemuDomain(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return this.URL_PATTERNS.some(pattern => {
        const domain = pattern.replace('*://', '').replace('/*', '')
        return urlObj.hostname.includes(domain.split('.')[0])
      })
    } catch {
      return false
    }
  }
}

// 调试配置
export const TEMU_DEBUG_CONFIG = {
  // 是否启用详细日志
  VERBOSE_LOGGING: true,
  
  // 日志前缀
  LOG_PREFIX: '[Temu]',
  
  // 是否在控制台显示缓存状态
  SHOW_CACHE_STATUS: true,
  
  // 是否显示网络请求详情
  SHOW_NETWORK_DETAILS: true
}

// 错误消息配置
export const TEMU_ERROR_MESSAGES = {
  NO_TAB_FOUND: '未找到 Temu 商家后台标签页，请先打开并登录 Temu 商家后台',
  CHROME_API_UNAVAILABLE: 'Chrome API 不可用，请检查扩展权限配置',
  NETWORK_ERROR: '网络请求失败，请检查网络连接',
  AUTH_FAILED: '认证失败，请重新登录 Temu 商家后台',
  INVALID_RESPONSE: 'API 返回数据格式错误',
  SCRIPT_INJECTION_FAILED: '脚本注入失败，请刷新页面重试'
}

// 导出统一配置对象
export const TEMU_CONFIG = {
  ENDPOINTS: TEMU_ENDPOINTS,
  CACHE: TEMU_CACHE_CONFIG,
  INTERCEPTOR: TEMU_INTERCEPTOR_CONFIG,
  DEFAULT_HEADERS: TEMU_DEFAULT_HEADERS,
  TAB: TEMU_TAB_CONFIG,
  DEBUG: TEMU_DEBUG_CONFIG,
  ERROR_MESSAGES: TEMU_ERROR_MESSAGES
} as const

// 类型定义
export interface CacheItem {
  value: string
  expiry: number
  source?: string
}

export interface AuthHeaders {
  'anti-content'?: string
  'mallid'?: string
  [key: string]: string | undefined
}

export interface LoginStatus {
  isLoggedIn: boolean
  userInfo?: {
    userId: string
    userName?: string
    email?: string
  }
  shopInfo?: {
    mallId: string
    shopId: string
    mallName: string
    shopName: string
  }
  message?: string
  error?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  errorCode?: number
}
