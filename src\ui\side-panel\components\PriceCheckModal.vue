<template>
  <a-modal
    v-model:open="visible"
    title="价格确认"
    width="800px"
    :footer="null"
    @cancel="handleClose"
  >
    <div class="price-check-content">
      <!-- 商品基本信息 -->
      <div class="mb-4">
        <div class="flex items-start space-x-3">
          <a-image
            :src="productData.image"
            :alt="productData.title"
            :width="80"
            :height="80"
            class="rounded-lg"
          />
          <div class="flex-1">
            <div class="font-medium text-gray-900 mb-2">{{ productData.title }}</div>
            <div class="text-sm text-gray-500 space-y-1">
              <div>SPU: {{ productData.spu }}</div>
              <div>SKC: {{ productData.skc }}</div>
              <div>站点: {{ productData.site }}</div>
              <div>币种: {{ productData.currency }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- SKU详细信息 -->
      <div class="border rounded-lg p-4 mb-4">
        <div class="flex items-start space-x-3">
          <a-image
            :src="productData.sku.image"
            :alt="productData.sku.color"
            :width="30"
            :height="30"
            class="rounded"
          />
          <div class="flex-1">
            <!-- 颜色和货号 -->
            <div class="mb-3">
              <div
                class="text-sm mb-1"
                style="max-width: 260px;"
              >
                颜色: {{ productData.sku.color }}
              </div>
              <span class="text-xs text-gray-500">
                货号：{{ productData.sku.itemNo }}
              </span>
            </div>

            <!-- 货盘平台和库存 -->
            <div class="space-y-2 mb-3">
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">货盘平台:</span>
                  <a
                    :href="sourceUrl"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-xs text-blue-600 hover:text-blue-800"
                  >
                    {{ platformName }}
                  </a>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-500">货盘库存:</span>
                  <span class="text-red-600 font-bold text-base">{{ priceInfo.stock || 10 }}</span>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">成本:</span>
                <div class="flex items-center space-x-1">
                  <span class="text-xs">¥ {{ costPrice }}</span>
                  <span class="text-xs text-gray-500">($ {{ priceInfo.price || 0 }})</span>
                </div>
              </div>
            </div>

            <!-- 申报价格 -->
            <div class="mb-2">
              <span class="text-xs text-gray-500">申报价格：</span>
              <span class="text-sm font-bold">{{ productData.declaredPrice }}</span>
            </div>

            <!-- 官方申报价 -->
            <div class="mb-2">
              <span class="text-xs text-gray-500">官方申报价：</span>
              <span class="text-base font-bold text-blue-600">¥{{ officialPrice }}</span>
            </div>

            <!-- 毛利 -->
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500">毛利:</span>
              <div>
                <span 
                  class="text-base font-bold"
                  :class="profit >= 0 ? 'text-green-600' : 'text-red-600'"
                >
                  ¥{{ profit.toFixed(2) }}
                </span>
                <span class="text-sm text-gray-500 ml-1">
                  ({{ profitRate.toFixed(2) }}%)
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 价格更新状态 -->
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <div class="flex items-center justify-between">
          <div>
            <div class="text-sm font-medium">价格更新状态</div>
            <div class="text-xs text-gray-500 mt-1">
              最后更新: {{ lastUpdateTime }}
            </div>
          </div>
          <a-button
            type="primary"
            size="small"
            :loading="isUpdating"
            @click="updatePrice"
          >
            {{ isUpdating ? '更新中...' : '刷新价格' }}
          </a-button>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-3">
        <a-button @click="handleClose">
          关闭
        </a-button>
        <a-button
          type="primary"
          @click="jumpToSource"
        >
          查看货源
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useNotification } from '../../../composables/useNotification'

// Props
interface Props {
  open: boolean
  productData: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  'jump-to-source': [product: any]
}>()

// Composables
const { info, error } = useNotification()

// 响应式数据
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const isUpdating = ref(false)
const priceInfo = ref({
  price: 0,
  stock: 10,
  imageUrl: '',
  timestamp: Date.now()
})

// 计算属性
const sourceUrl = computed(() => {
  const extCode = props.productData?.sku?.itemNo || ''
  const match = extCode.match(/^([A-Z0-9]{10})\[([a-z]+)\](\d+)$/)
  
  if (match) {
    const [, asin, platform] = match
    switch (platform.toLowerCase()) {
      case 'am':
      case 'amazon':
        return `https://www.amazon.com/dp/${asin}`
      case 'eb':
      case 'ebay':
        return `https://www.ebay.com/itm/${asin}`
      default:
        return ''
    }
  }
  return ''
})

const platformName = computed(() => {
  const extCode = props.productData?.sku?.itemNo || ''
  const match = extCode.match(/^([A-Z0-9]{10})\[([a-z]+)\](\d+)$/)
  
  if (match) {
    const [, , platform] = match
    switch (platform.toLowerCase()) {
      case 'am':
      case 'amazon':
        return 'AM'
      case 'eb':
      case 'ebay':
        return 'EB'
      default:
        return platform.toUpperCase()
    }
  }
  return 'Unknown'
})

const costPrice = computed(() => {
  const usdPrice = priceInfo.value.price || 0
  const exchangeRate = 7.2 // 假设汇率
  return (usdPrice * exchangeRate).toFixed(2)
})

const officialPrice = computed(() => {
  // 从申报价格中提取数字
  const declaredPrice = props.productData?.declaredPrice || '0'
  const match = declaredPrice.match(/[\d.]+/)
  return match ? parseFloat(match[0]).toFixed(2) : '0.00'
})

const profit = computed(() => {
  const official = parseFloat(officialPrice.value)
  const cost = parseFloat(costPrice.value)
  return official - cost
})

const profitRate = computed(() => {
  const official = parseFloat(officialPrice.value)
  if (official === 0) return 0
  return (profit.value / official) * 100
})

const lastUpdateTime = computed(() => {
  return new Date(priceInfo.value.timestamp).toLocaleString('zh-CN')
})

// 方法
const handleClose = () => {
  visible.value = false
}

const jumpToSource = () => {
  emit('jump-to-source', props.productData)
  handleClose()
}

const updatePrice = async () => {
  if (!props.productData?.sku?.itemNo) {
    error('错误', '缺少货号信息')
    return
  }

  isUpdating.value = true
  
  try {
    const extCode = props.productData.sku.itemNo
    const match = extCode.match(/^([A-Z0-9]{10})\[([a-z]+)\](\d+)$/)
    
    if (!match) {
      throw new Error('货号格式不正确')
    }

    const [, asin] = match

    // 发送价格更新请求
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      const response = await chrome.runtime.sendMessage({
        action: 'GET_AMAZON_PRICE',
        asin: asin,
        extCode: extCode,
        sourceUrl: sourceUrl.value
      })

      if (response.success) {
        priceInfo.value = {
          ...response.data,
          timestamp: Date.now()
        }
        info('成功', '价格信息已更新')
      } else {
        throw new Error(response.error || '价格更新失败')
      }
    } else {
      throw new Error('Chrome扩展API不可用')
    }
  } catch (err) {
    console.error('更新价格失败:', err)
    error('错误', err instanceof Error ? err.message : '价格更新失败')
  } finally {
    isUpdating.value = false
  }
}

// 监听产品数据变化，自动加载价格信息
watch(() => props.productData, async (newData) => {
  if (newData?.sku?.itemNo && props.open) {
    // 尝试从IndexDB加载已有的价格信息
    // 这里可以添加从IndexDB读取价格信息的逻辑
  }
}, { immediate: true })
</script>

<style scoped>
.price-check-content {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
