<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Amazon数据组装测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .status-valid {
            border-color: #52c41a;
            background-color: #f6ffed;
        }
        .status-invalid {
            border-color: #ff4d4f;
            background-color: #fff2f0;
        }
        .status-warning {
            border-color: #faad14;
            background-color: #fffbe6;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .btn-success {
            background: #52c41a;
        }
        .btn-warning {
            background: #faad14;
        }
        .btn-danger {
            background: #ff4d4f;
        }
        .form-group {
            margin: 16px 0;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 12px;
            min-height: 150px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }
        .log-area {
            background: #001529;
            color: #fff;
            padding: 16px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .result-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Amazon数据组装测试页面</h1>
        <p>使用现有的数据组装方法，将 spuData 和 skuDataList 组装成 dianxiaomiData</p>

        <div class="status-card status-invalid">
            <h4>⚠️ 重要使用说明</h4>
            <p><strong>此页面需要在Chrome扩展环境中运行！</strong></p>
            <p>请通过以下方式之一访问：</p>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li>在扩展的popup页面中打开</li>
                <li>在扩展的options页面中打开</li>
                <li>通过 chrome-extension:// 协议访问</li>
                <li>将此文件添加到manifest.json的web_accessible_resources中</li>
            </ul>
        </div>

        <div class="status-card status-warning">
            <h4>🔧 使用的现有方法</h4>
            <ul style="margin: 8px 0; padding-left: 20px;">
                <li><strong>DataMapperService.mapAmazonToDianxiaomi()</strong> - 主要组装方法</li>
                <li><strong>AmazonDataService.convertToDianxiaomiFormat()</strong> - 备用方法</li>
                <li><strong>缓存配置：</strong> 从 temu-extension-basic-config 读取</li>
                <li><strong>图片处理：</strong> 可选的图片上传和处理</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h3>操作面板</h3>
        <button class="btn" id="checkCacheBtn">🔍 检查缓存配置</button>
        <button class="btn btn-warning" id="loadSampleBtn">📝 加载示例数据</button>
        <button class="btn btn-success" id="assembleBtn">🔧 组装数据</button>
        <button class="btn btn-danger" id="clearLogBtn">🗑️ 清除日志</button>
    </div>

    <div class="grid-2">
        <div class="container">
            <h3>SPU数据输入</h3>
            <div class="form-group">
                <label class="form-label" for="spuData">SPU数据 (JSON格式)：</label>
                <textarea class="form-textarea" id="spuData" placeholder="请输入SPU数据..."></textarea>
            </div>
        </div>

        <div class="container">
            <h3>SKU数据输入</h3>
            <div class="form-group">
                <label class="form-label" for="skuDataList">SKU数据列表 (JSON格式)：</label>
                <textarea class="form-textarea" id="skuDataList" placeholder="请输入SKU数据列表..."></textarea>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>组装结果</h3>
        <div id="resultArea" class="result-area">等待数据组装...</div>
    </div>

    <div class="container">
        <h3>调试日志</h3>
        <div id="logArea" class="log-area">准备就绪...</div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea')
            const timestamp = new Date().toLocaleTimeString()
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`
            logArea.scrollTop = logArea.scrollHeight
        }

        function clearLog() {
            document.getElementById('logArea').textContent = ''
        }

        // 检查缓存配置
        async function checkCache() {
            try {
                log('检查缓存配置...')

                // 检查是否在扩展环境中
                if (typeof chrome === 'undefined' || !chrome.storage) {
                    log('当前不在Chrome扩展环境中，请将此页面作为扩展页面打开', 'warning')
                    log('建议：将此文件放在扩展目录中，通过扩展页面访问', 'warning')
                    return
                }

                const result = await chrome.storage.local.get(['temu-extension-basic-config'])
                const config = result['temu-extension-basic-config']

                if (config) {
                    log('缓存配置存在:', 'success')
                    log(JSON.stringify(config, null, 2))
                } else {
                    log('缓存配置不存在，将使用默认配置', 'warning')
                }
            } catch (error) {
                log('检查缓存配置失败: ' + error.message, 'error')
            }
        }

        // 加载示例数据
        function loadSampleData() {
            log('加载示例数据...')
            
            const sampleSpuData = {
                "asin": "B08N5WRWNW",
                "title": "Echo Dot (4th Gen) | Smart speaker with Alexa | Charcoal",
                "brand": "Amazon",
                "price": 49.99,
                "currency": "USD",
                "mainImageUrl": "https://m.media-amazon.com/images/I/714Rq4k05UL._AC_SL1000_.jpg",
                "imageUrls": [
                    "https://m.media-amazon.com/images/I/714Rq4k05UL._AC_SL1000_.jpg",
                    "https://m.media-amazon.com/images/I/61-s7IXQY3L._AC_SL1000_.jpg"
                ],
                "categoryPath": "Electronics > Smart Home > Smart Speakers",
                "sourceUrl": "https://www.amazon.com/dp/B08N5WRWNW",
                "bulletPoints": [
                    "Meet the all-new Echo Dot - Our most popular smart speaker with Alexa.",
                    "Sleek, compact design - Fits perfectly into small spaces.",
                    "Improved speaker quality - Richer and louder sound than Echo Dot Gen 3."
                ],
                "specifications": {
                    "Dimensions": "3.9\" x 3.9\" x 3.5\"",
                    "Weight": "12.8 oz",
                    "Connectivity": "Wi-Fi, Bluetooth"
                },
                "stockStatus": "In Stock",
                "rating": 4.7,
                "reviewCount": 234567
            }

            const sampleSkuDataList = [
                {
                    "asin": "B08N5WRWNW",
                    "parentAsin": "B08N5WRWNW",
                    "currency": "USD",
                    "stockStatus": "In Stock",
                    "price": 49.99,
                    "imageUrl": "https://m.media-amazon.com/images/I/714Rq4k05UL._AC_SL1000_.jpg",
                    "variationAttributes": "{}"
                }
            ]

            document.getElementById('spuData').value = JSON.stringify(sampleSpuData, null, 2)
            document.getElementById('skuDataList').value = JSON.stringify(sampleSkuDataList, null, 2)
            
            log('示例数据加载完成', 'success')
        }

        // 组装数据
        async function assembleData() {
            try {
                log('开始组装数据...')

                // 检查扩展环境
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('当前不在Chrome扩展环境中，无法调用扩展API')
                }

                // 获取输入数据
                const spuDataText = document.getElementById('spuData').value.trim()
                const skuDataListText = document.getElementById('skuDataList').value.trim()

                if (!spuDataText || !skuDataListText) {
                    throw new Error('请输入SPU数据和SKU数据列表')
                }

                let spuData, skuDataList
                try {
                    spuData = JSON.parse(spuDataText)
                    skuDataList = JSON.parse(skuDataListText)
                } catch (parseError) {
                    throw new Error('JSON格式错误: ' + parseError.message)
                }

                log('数据解析成功')
                log('SPU数据: ' + JSON.stringify(spuData, null, 2))
                log('SKU数据列表: ' + JSON.stringify(skuDataList, null, 2))

                // 调用现有的组装方法
                log('调用数据组装服务...')
                const result = await chrome.runtime.sendMessage({
                    action: 'ASSEMBLE_DIANXIAOMI_DATA',
                    spuData: spuData,
                    skuDataList: skuDataList
                })

                if (result.success) {
                    log('数据组装成功!', 'success')
                    const resultArea = document.getElementById('resultArea')
                    resultArea.textContent = JSON.stringify(result.dianxiaomiData, null, 2)
                    log('组装结果已显示在结果区域')
                } else {
                    throw new Error(result.error || '数据组装失败')
                }

            } catch (error) {
                log('组装数据失败: ' + error.message, 'error')
                const resultArea = document.getElementById('resultArea')
                resultArea.textContent = '组装失败: ' + error.message
            }
        }

        // 事件监听器
        document.getElementById('checkCacheBtn').addEventListener('click', checkCache)
        document.getElementById('loadSampleBtn').addEventListener('click', loadSampleData)
        document.getElementById('assembleBtn').addEventListener('click', assembleData)
        document.getElementById('clearLogBtn').addEventListener('click', clearLog)

        // 页面加载完成后自动检查缓存
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，准备测试数据组装功能')
            checkCache()
        })
    </script>
</body>
</html>
