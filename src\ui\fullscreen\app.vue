<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { UserOutlined, SettingOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 导航菜单
const navItems = ref([
  {
    key: 'dashboard',
    label: '工作台',
    icon: '🏠',
    route: '/fullscreen/dashboard'
  },
  {
    key: 'product-center',
    label: '上品中心',
    icon: '📊',
    route: '/fullscreen/product-center'
  }
])

// 当前激活的导航项
const activeNav = computed(() => {
  const currentPath = route.path
  return navItems.value.find(item => currentPath.includes(item.key))?.key || 'dashboard'
})

// 导航到指定页面
const navigateTo = (route: string) => {
  router.push(route)
}

// 关闭全屏页面
const closeFullscreen = () => {
  window.close()
}

// 打开设置
const openSettings = () => {
  chrome.runtime.openOptionsPage()
}

// 用户信息（示例）
const userInfo = ref({
  name: '胡建大卖家',
  avatar: '',
  memberType: '星耀会员'
})
</script>

<template>
  <div class="fullscreen-layout">
    <!-- 顶部导航栏 -->
    <header class="fullscreen-header">
      <div class="header-left">
        <!-- Logo -->
        <div class="header-logo">
          <span>🚀</span>
          <span>胡建大卖家</span>
          <a-tag color="gold" size="small">全屏模式</a-tag>
        </div>

        <!-- 导航菜单 -->
        <nav class="header-nav">
          <a
            v-for="item in navItems"
            :key="item.key"
            :class="['nav-item', { active: activeNav === item.key }]"
            @click="navigateTo(item.route)"
          >
            <span>{{ item.icon }}</span>
            <span>{{ item.label }}</span>
          </a>
        </nav>
      </div>

      <div class="header-right">
        <!-- 用户信息 -->
        <div class="user-info">
          <a-avatar :icon="UserOutlined" size="small" />
          <span style="margin-left: 8px; color: rgba(255, 255, 255, 0.9);">
            {{ userInfo.name }}
          </span>
          <a-tag color="gold" size="small" style="margin-left: 8px;">
            {{ userInfo.memberType }}
          </a-tag>
        </div>

        <!-- 操作按钮 -->
        <a-space>
          <a-button
            type="text"
            :icon="SettingOutlined"
            style="color: rgba(255, 255, 255, 0.8);"
            @click="openSettings"
          >
            设置
          </a-button>
          <a-button
            type="text"
            :icon="FullscreenExitOutlined"
            style="color: rgba(255, 255, 255, 0.8);"
            @click="closeFullscreen"
          >
            退出全屏
          </a-button>
        </a-space>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="fullscreen-content">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .header-nav {
    gap: 12px;
  }
  
  .user-info span {
    display: none;
  }
}
</style>
